<?php
/*
Plugin Name: <PERSON>renzlotsen Page Builder
Description: Ein benutzerdefinierter Page Builder, der den Standard-WordPress-Editor ersetzt.
Version: 1.1
Author: <PERSON> im Auftrag von Grenzlotsen GmbH
*/

/** Check  PHP-Konfiguration */
// error_log('PHP Version: ' . phpversion());
// error_log('error_log setting: ' . ini_get('error_log'));
// error_log('log_errors setting: ' . ini_get('log_errors'));
// error_log('error_reporting setting: ' . ini_get('error_reporting'));

// Absoluter Pfad für PHP Includes
if (!defined('PLUGIN_ROOT_PATH')) {
    define('PLUGIN_ROOT_PATH', plugin_dir_path(__FILE__));
}

// Relativer Pfad ab /wp-content für URLs
if (!defined('PLUGIN_RELATIVE_PATH')) {
    $fullPath = plugin_dir_path(__FILE__);
    $wpContentPos = strpos($fullPath, '/wp-content/');
    if ($wpContentPos !== false) {
        $relativePath = substr($fullPath, $wpContentPos);
    } else {
        $relativePath = ''; // Fallback, falls `/wp-content/` nicht gefunden wird.
    }
    define('PLUGIN_RELATIVE_PATH', $relativePath);
}

/**
 * Check if compatible theme is active
 */
function check_theme_active() {
    $theme = wp_get_theme();
    if ($theme->get('Name') !== 'Zollwärts Theme') {
        add_action('admin_notices', 'show_theme_warning');
    }
}

function show_theme_warning() {
    echo '<div class="notice notice-error"><p>';
    echo __('Das Grenzlotsen-Page Builder Plugin benötigt das Zollwärts- oder Grenzlotsen-Theme, um korrekt zu funktionieren. Bitte installiere und aktiviere eines der genannten Themes.', 'newwwways-page-builder');
    echo '</p></div>';
}

add_action('admin_init', 'check_theme_active');


/**
 * Include files
 */
// Deaktiviere den Gutenberg-Editor
include('includes/deactivate-gutenberg.php');

// Füge das Admin-Menü hinzu
include('includes/admin-menu.php');

// Registriere Scripts und Styles
include('includes/enqueue-scripts.php');



// Fist disable the default WP-Content/TinyMCE Editor
add_action('admin_head', 'hide_default_editor');

/* hide with css */
function hide_default_editor() {
    $screen = get_current_screen();
    if ('page' === $screen->id || 'post' === $screen->id || 'newsletter_archiv' === $screen->id || 'ressources_archiv' === $screen->id || 'erfolgsgeschichten' === $screen->id || 'events' === $screen->id || 'sessions' === $screen->id) {
        echo '<style>
            .block-editor .editor-post-title,
            .block-editor .editor-default-block-appender,
            .block-editor .block-editor-block-list__layout,
            #postdivrich {
                display: none !important;
            }
        </style>';
    }
}

// Remove the Excerpt Help Text (it is required)
function remove_excerpt_help_text() {
    echo '<style>
        #postexcerpt .inside p { 
            display: none; 
        }
    </style>';
}
add_action('admin_head', 'remove_excerpt_help_text');



// Make sure AJAX is working to save data 
add_action('admin_head', 'my_custom_fonts');

function my_custom_fonts() {
  echo '<script type="text/javascript">
    var ajaxurl = "' . admin_url('admin-ajax.php') . '";
  </script>';
}

function grenzlotsen_page_builder_admin_scripts() {
    wp_enqueue_script('tinymce');
    wp_enqueue_script('jquery');
    wp_enqueue_script('wp-tinymce');
    wp_enqueue_media(); // Enqueue WordPress media scripts
}
add_action('admin_enqueue_scripts', 'grenzlotsen_page_builder_admin_scripts');


/* Meta Boxes */
function grenzlotsen_page_builder_add_meta_box() {
    // $screens = ['post', 'page']; // Füge hier weitere Post-Typen hinzu, falls nötig
    $screens = ['post', 'page', 'newsletter_archiv', 'ressources_archiv', 'erfolgsgeschichten', 'sessions', 'events']; // Füge hier weitere Post-Typen hinzu, falls nötig
    foreach ($screens as $screen) {
        add_meta_box(
            'grenzlotsen_page_builder_id',          // Unique ID
            'Grenzlotsen Page Builder',             // Box title
            'grenzlotsen_page_builder_html',        // Content callback, must be of type callable
            $screen,                                 // Post type
            'normal',                              // Context (normal, side, advanced)
            'high'                                 // Priority (high, core, default, low)
        );
    }
}
add_action('add_meta_boxes', 'grenzlotsen_page_builder_add_meta_box');

// register post meta 
function grenzlotsen_register_meta() {
    register_post_meta('post', '_page_builder_blocks', array(
        'show_in_rest' => true,
        'single' => true,
        'type' => 'string',
        'revisions_enabled' => true,  // Revisionen aktivieren
    ));

    register_post_meta('post', '_page_builder_frontend_settings', array(
        'show_in_rest' => true,
        'single' => true,
        'type' => 'string',
        'revisions_enabled' => true,  // Revisionen aktivieren
    ));
}
add_action('init', 'grenzlotsen_register_meta');

function enable_revisions_for_page_builder_fields( $revisioned_keys ) {
    // Füge die Meta-Daten zur Liste der zu revisionierenden Felder hinzu
    if ( ! in_array( '_page_builder_blocks', $revisioned_keys, true ) ) {
        $revisioned_keys[] = '_page_builder_blocks';
    }
    if ( ! in_array( '_page_builder_frontend_settings', $revisioned_keys, true ) ) {
        $revisioned_keys[] = '_page_builder_frontend_settings';
    }
    return $revisioned_keys;
}
add_filter( 'wp_post_revision_meta_keys', 'enable_revisions_for_page_builder_fields' );



/** Load Page Builder CSS */
// function newwways_page_builder_global() {
//     $css_file_path = plugin_dir_path( __FILE__ ) . 'includes/css/page-builder.css';
//     $css_file_url  = plugin_dir_url( __FILE__ ) . 'includes/css/page-builder.css';

//     if ( file_exists( $css_file_path ) ) {
//         $version = filemtime( $css_file_path );
//         wp_enqueue_style( 'mein-stylesheet', $css_file_url, array(), $version );
//     } else {
//         error_log( 'CSS-Datei nicht gefunden: ' . $css_file_path );
//     }
// }
// add_action( 'admin_enqueue_scripts', 'newwways_page_builder_global' );


function grenzlotsen_page_builder_html($post) {
    /* Get data from Fields */ 
    $mein_text_input = get_post_meta($post->ID, '_mein_text_input', true);
    // Nonce für die Sicherheit hinzufügen
    wp_nonce_field('grenzlotsen_page_builder', 'grenzlotsen_page_builder_nonce');

    
    $blocks = get_post_meta($post->ID, '_page_builder_blocks', true) ?: '[]'; // Standardmäßig ein leeres Array im JSON-Format
    $blocks = wp_unslash($blocks); // Hier werden die überflüssigen Backslashes entfernt
    // Nur ausgeben, wenn es gültiges JSON ist
    if (is_string($blocks)) {
        echo '<script>console.log(' . wp_json_encode($blocks) . ');</script>';
        echo "<script>var initialBlocks = " . wp_json_encode($blocks) . ";</script>";
    } else {
        echo "<script>console.error('Daten sind kein gültiger JSON-String: ', " . wp_json_encode($blocks) . ");</script>";
    }
    
    $frontend_settings = get_post_meta($post->ID, '_page_builder_frontend_settings', true) ?: '{}'; // Standardmäßig ein leeres Array im JSON-Format
    $frontend_settings = wp_unslash($frontend_settings); // Hier werden die überflüssigen Backslashes entfernt

    // Nonce für die Sicherheit hinzufügen
    wp_nonce_field('grenzlotsen_page_builder', 'grenzlotsen_page_builder_nonce');

    /* Get Available Dates from CPT Sessions */ 
    function get_available_session_dates() {
        global $wpdb;

        $dates = $wpdb->get_col("
            SELECT DISTINCT meta_value 
            FROM {$wpdb->postmeta} 
            WHERE meta_key = '_session_date'
            ORDER BY meta_value ASC
        ");

        return $dates;
    }

    $available_dates = get_available_session_dates();

    ?>
    <script>
        var meinTextInput = <?php echo json_encode($mein_text_input); ?>;
        console.log('meinTextInput:', meinTextInput);

        // Page Builder Content
        var postID = <?php echo json_encode($post->ID); ?>;
        // var initialBlocks = JSON.parse(<?php echo json_encode($blocks); ?>); // Parst die JSON-Daten zu einem Array
        var initialBlocks = JSON.parse(<?php echo wp_json_encode($blocks); ?>); // Parst die JSON-Daten zu einem Array
        console.log('typeOf initialBlocks: ', typeof initialBlocks);
        console.log('initialBlocks: ', initialBlocks);

        // Frontend Settings 
        console.log('JSON encode initialFrontendSettings: ', <?php echo json_encode($frontend_settings); ?>);
        var initialFrontendSettings = JSON.parse(<?php echo json_encode($frontend_settings); ?>); // Parst die JSON-Daten zu einem Array
        console.log('initialFrontendSettings: ', initialFrontendSettings);

        // TODO: einmal über die Blocks Loopen und die Eigenschaft "advancedSettings: { activeTab: 'spacing' }" setzen
        let initialBlocksUi = initialBlocks.map(block => ({
            ...block,
            uiValues: { 
                activeTab: 'content',
                activeTabNumber: 1,
                minified: false,
                secondaryTabNumber: 0,
                isAddNewItemLightboxOpen: false,
            }
        }));

        console.log('initialBlocksUi: ', initialBlocksUi)

    </script>


    <form id="post">
        <!-- Dein Formularinhalt hier -->
        <input type="hidden" id="page-builder-blocks" name="page_builder_blocks" />
        <input type="hidden" id="page-builder-frontend-settings" name="page_builder_frontend_settings" />
        <!-- Der Rest deines Formulars -->
    </form>


    <!-- Get data from Page Settings (admin_menu.php) -->
    <?php 
        $theme_settings = wp_unslash(get_option('grenzlotsen_page_builder_content')); 
    ?>
    
    <script>
        let themeSettings = JSON.parse(<?php echo json_encode($theme_settings); ?>)
        console.log('themeSettings: ', themeSettings)
        console.log('themeSettings.htmlForms: ', themeSettings.htmlForms)
        // const htmlForms = themeSettings.htmlForms
    </script>

    <div
		class="spin"
		style="border-top-color: #73b740;"
	></div>

    <style>
        .spin {
            display: block;
            width: 40px;
            height: 40px;
            margin: calc(10vh - 40px) auto;
            border: 3px solid transparent;
            border-radius: 50%;
            animation: spin 1s ease infinite;
        }

        @keyframes spin {
            to {
                transform: rotate(180deg);
            }
        }
    </style>

    <script>
        // call the function when the page loads
        window.addEventListener('load', function() {
            // get the spinner 
            const spinner = document.querySelector('.spin');
            // hide the spinner
            spinner.style.display = 'none';

            // get the page buidler (#app)
            const app = document.querySelector('#app');
            // show the page builder
            app.style.display = 'block';
        })
    </script>

    <div id="page-builder-app">
        <div class="testing-vue">
            <!-- Vue 3 -->
            <!-- <script src="/wp-content/plugins/newwways-page-builder/includes/js/vue/vue.global.js"></script>
            <script src="/wp-content/plugins/newwways-page-builder/includes/js/vue/Sortable.min.js"></script>
            <script src="/wp-content/plugins/newwways-page-builder/includes/js/vue/vuedraggable.umd.js"></script> -->
            <script src="<?php echo plugin_dir_url(__FILE__) . 'includes/js/vue/vue.global.js'; ?>"></script>
            <script src="<?php echo plugin_dir_url(__FILE__) . 'includes/js/vue/Sortable.min.js'; ?>"></script>
            <script src="<?php echo plugin_dir_url(__FILE__) . 'includes/js/vue/vuedraggable.umd.js'; ?>"></script>


            <div id="app" style="display: none;">


                <!-- Lightbox to add new rows/elements to the page -->
                <div v-if="isAddNewElementLightboxOpen" class="lightbox add-content-module">
                    <div class="lightbox-content">
                        <div class="lightbox-header">
                            <h3>Add new Module</h3>
                            <span @click="isAddNewElementLightboxOpen = false" class="btn">X</span>
                        </div>
                        <div class="lightbox-body">
                            <!-- <p>index: {{index}} - itemIndex: {{itemIndex}}</p> -->
                            <ul class="module-selection">
                                <li v-for="availableModule in availableBlocksForType">
                                    <div @click="addNewBlock(availableModule)" class="button add-module-button">
                                        <div v-if="availableModule.componentPreviewImage" class="component-preview-image">
                                            <img :src="`/wp-content/plugins/newwways-page-builder/components/component-preview-images/${availableModule.componentPreviewImage}`">
                                        </div>
                                        <div v-if="availableModule.name" class="module-name">{{availableModule.name}}</span>
                                    </span>
                                </li>
                            </ul>
                        </div>
                        <!-- <div class="lightbox-footer">
                            <span @click="isAddNewElementLightboxOpen = false">Done</span>
                        </div> -->
                    </div>
                </div>


            
                <!-- Page Builder Header -->
                <div class="page-builder-header">
                    <!-- <div @click="togglePreview()" style="border: solid #333 1px; padding: 10px 18px; border-radius: 4px;">Preview</div> -->
                    <span></span>
                    <div style="display: flex; gap: 2rem;">
                        <div v-if="isUserAdmin" style="display: flex; flex-direction: row; align-items: center;">
                            <p>Advanced JSON editing</p>
                            <label class="toggle-switch">
                                <input type="checkbox" v-model="enableAdvancedJsonEditing">
                                <span class="slider"></span>
                            </label>
                        </div>
                        <div @click="toggleFullscreen()" style="border: solid #333 1px; padding: 10px 18px; border-radius: 4px;"><span v-if="isFullscreen">Exit </span>Fullscreen</div>
                    </div>
                </div>

                <div v-if="enableAdvancedJsonEditing" class="advanced-settings">
                    <h2>Raw JSON</h2>
                    <div class="raw-json-blocks" style="display: flex;">
                        <input type="text" v-model="blockAsString"></input>
                        <span @click="updateBlocks()" class="btn">update JSON</span>
                        <!-- <p>blockAsString: {{blockAsString}}</p> -->
                        <!-- <p>{{ JSON.stringify(blocks) }}</p> -->
                    </div>
                    <hr>
                </div>

                <div class="draggable-elements-wrapper">
                    <draggable
                        v-model="blocks"
                        :group="{ name: 'modules', pull: false, put: false }" 
                        @start="drag = true"
                        @end="() => {
                            drag = false;
                            onDragEnd()
                        }"
                        item-key="id"
                        chosen-class="empty-dashed"
                        ghost-class="ghost"
                        chosen-class="chosen"
                        handle=".drag-handle"
                    >
                        <template #item="{element, index: elementIndex}">
                            <div 
                                class="dnd w-full mb-3 flex justify-between dark:bg-gray-600 items-center shadow rounded-lg cursor-move"
                            >
                                <div class="element-header-wrapper p-4 pb-0">
                                    <div class="element-header-row">
                                        <h2 v-if="element.name">{{ element.name }}</h2>
                                        <h2 v-else-if="element.type">{{ element.type }}</h2>
                                        <p>
                                            <!-- id: {{ element.id }}   -->
                                            <!-- <span style="color: white;">uiValues: {{element.uiValues}}</span> -->
                                        </p>
                                        <div class="actions flex">
                                            <div class="btn" @click="element.uiValues.minified = !element.uiValues.minified">_</div>
                                            <div class="btn drag-handle" style="cursor: grab;">&#x2630;</div>
                                            <div class="btn" @click="duplicateBlock(element, elementIndex)">
                                                <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                    <path fill-rule="evenodd" d="M18 3a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-1V9a4 4 0 0 0-4-4h-3a1.99 1.99 0 0 0-1 .267V5a2 2 0 0 1 2-2h7Z" clip-rule="evenodd"/>
                                                    <path fill-rule="evenodd" d="M8 7.054V11H4.2a2 2 0 0 1 .281-.432l2.46-2.87A2 2 0 0 1 8 7.054ZM10 7v4a2 2 0 0 1-2 2H4v6a2 2 0 0 0 2 2h7a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3Z" clip-rule="evenodd"/>
                                                </svg>
                                            </div>
                                            <div v-if="enableAdvancedJsonEditing" class="btn" @click="copyBlock(element, elementIndex)">
                                                <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                    <path fill-rule="evenodd" d="M9 7V2.221a2 2 0 0 0-.5.365L4.586 6.5a2 2 0 0 0-.365.5H9Zm2 0V2h7a2 2 0 0 1 2 2v9.293l-2-2a1 1 0 0 0-1.414 1.414l.293.293h-6.586a1 1 0 1 0 0 2h6.586l-.293.293A1 1 0 0 0 18 16.707l2-2V20a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V9h5a2 2 0 0 0 2-2Z" clip-rule="evenodd"/>
                                                </svg>
                                            </div>
                                            <div class="btn" @click="removeBlock(element.id)">X</div>
                                        </div>
                                    </div>
                                    <div v-if="!element.uiValues.minified" class="element-header-row">
                                        <div class="element-nav">
                                            <div 
                                                class="btn bottom-aligned" 
                                                :key="element.id"
                                                :class="element.uiValues.activeTab === 'content' ? 'active' : ''"
                                                @click="element.uiValues.activeTab = 'content', updateActiveTab(element, 1)"
                                            >
                                                Content
                                            </div>
                                            <div 
                                                class="btn bottom-aligned" 
                                                :key="element.id"
                                                :class="element.uiValues.activeTab === 'appearance' ? 'active' : ''"
                                                @click="element.uiValues.activeTab = 'appearance', updateActiveTab(element, 2)"    
                                            >
                                                Appearance
                                            </div>
                                            <div 
                                                class="btn bottom-aligned" 
                                                :key="element.id"
                                                :class="element.uiValues.activeTab === 'spacing' ? 'active' : ''"
                                                @click="element.uiValues.activeTab = 'spacing', updateActiveTab(element, 3)"
                                            >
                                                Spacing
                                            </div>
                                        </div>
                                        
                                    </div>
                                </div>

                                <div v-if="!element.uiValues.minified">
                                    <div v-if="element.type === 'paragraph' || element.type === 'headline' || element.type === 'text-section'">
                                        <label :for="'tinymce-' + element.id">Text Section:</label><br/>
                                        <textarea :id="'tinymce-' + element.id" v-model="element.content"></textarea>
                                        <p>{{element.content}}</p>
                                    </div>
                                    
                                    <!-- Headline -->
                                    <div v-if="element.type === 'headline' ">
                                        <label :for="`nw-input-${element.id}`">Mein Texteingabefeld:</label><br/>
                                        <input type="text" :id="`nw-input-${element.id}`" v-model="element.content" />
                                    </div>

                                    <!-- /////////// -->
                                    <!-- hero module -->
                                    <!-- /////////// -->
                                    <!-- hero module -->
                                    <?php include PLUGIN_ROOT_PATH . 'components/hero-section.php'; ?>


                                    <!-- ////////////////////// -->
                                    <!-- Flexible Content Modul -->
                                    <!-- ////////////////////// -->
                                    <?php include PLUGIN_ROOT_PATH . 'components/flexible-content.php'; ?>


                                    <!-- ///////// -->
                                    <!-- Success Story Teaser -->
                                    <!-- ///////// -->
                                    <?php /*
                                    <div v-if="element.type === 'success-story-teaser'" class="success-story-teaser">
                                        <tab-transition-component :active-tab-number="element.uiValues.activeTabNumber">
                                            <div v-if="element.uiValues.activeTab === 'content'" class="content-wrapper p-4">
                                                <div class="grid">
                                                    <div>
                                                        <div>
                                                            <label :for="`nw-input-tagline-${element.id}`">Tagline</label><br/>
                                                            <input type="text" :id="`nw-input-tagline-${element.id}`" v-model="element.tagline" />
                                                        </div>
                                                        <div>
                                                            <label :for="`nw-input-heading-${element.id}`">Heading</label><br/>
                                                            <input type="text" :id="`nw-input-heading-${element.id}`" v-model="element.heading" />
                                                        </div>
                                                        <div>
                                                            <label :for="`nw-input-content-${element.id}`">Content</label><br/>
                                                            <textarea :id="`nw-input-content-${element.id}`" v-model="element.content"></textarea>
                                                        </div>
                                                    </div>
                                                    <hr>
                                                    <div class="post-output-settings">
                                                        <div>
                                                            <label :for="`nw-input-post-count-${element.id}`">Number of Success Stories</label><br/>
                                                            <input type="number" :id="`nw-input-post-count-${element.id}`" v-model="element.postCount" />
                                                        </div>
                                                        <div>
                                                            <label :for="`nw-input-post-offset-${element.id}`">Offset</label><br/>
                                                            <input type="number" :id="`nw-input-post-offset-${element.id}`" v-model="element.postOffset" />
                                                        </div>
                                                        <div>
                                                            <label :for="`nw-input-category-${element.id}`">Category</label><br/>
                                                            <select :id="`nw-input-category-${element.id}`" v-model="element.category">
                                                                <option v-for="category in successStoryCategories" :key="category.id" :value="category.slug">{{ category.name }}</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Appearance -->
                                            <div v-if="element.uiValues.activeTab === 'appearance'" class="appearance-wrapper p-4">
                                                <h2>Appearance</h2>
                                                <select v-model="element.postAppearance">
                                                    <option value="standard">Standard</option>
                                                    <option value="featured">Featured</option>
                                                </select>
                                            </div>

                                            <!-- Spacing -->
                                            <div v-if="element.uiValues.activeTab === 'spacing'" class="advanced-settings-wrapper p-4">
                                                <?php include('components/advancedSettings/spacing-component.php'); ?>
                                            </div>
                                        </tab-transition-component>
                                    </div>
                                    */
                                    ?>
                                    
                                    
                                    <!-- /////////////////// -->
                                    <!-- Testimonial Section -->
                                    <!-- /////////////////// -->
                                    <?php include('components/testimonial-section.php'); ?>

                                    
                                    
                                    <!-- ///////// -->
                                    <!-- Post List -->
                                    <!-- ///////// -->
                                    <div v-if="element.type === 'post-list'" class="post-list">
                                        <tab-transition-component :active-tab-number="element.uiValues.activeTabNumber">
                                            <!-- Content -->
                                            <div v-if="element.uiValues.activeTab === 'content'" class="content-wrapper p-4">
                                                <div class="grid">
                                                    <div>
                                                        <div>
                                                            <label :for="`nw-input-tagline-${element.id}`">tagline</label><br/>
                                                            <input type="text" :id="`nw-input-tagline-${element.id}`" v-model="element.tagline" />
                                                        </div>
                                                        <div>
                                                            <label :for="`nw-input-heading-${element.id}`">Heading</label><br/>
                                                            <input type="text" :id="`nw-input-heading-${element.id}`" v-model="element.heading" />
                                                        </div>
                                                        <div>
                                                            <label :for="`nw-input-content-${element.id}`">Content</label><br/>
                                                            <textarea :id="`nw-input-content-${element.id}`" v-model="element.content"></textarea>
                                                        </div>
                                                    </div>
                                                    <hr>
                                                    <div class="post-output-settings">
                                                        <div>
                                                            <label :for="`nw-input-post-count-${element.id}`">Number of Posts</label><br/>
                                                            <input type="number" :id="`nw-input-post-count-${element.id}`" v-model="element.postCount" />
                                                        </div>
                                                        <div>
                                                            <label :for="`nw-input-post-offset-${element.id}`">Offset</label><br/>
                                                            <input type="number" :id="`nw-input-post-offset-${element.id}`" v-model="element.postOffset" />
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Appearance -->
                                            <div v-if="element.uiValues.activeTab === 'appearance'" class="appearance-wrapper p-4">
                                                <h2>Appearance</h2>

                                                <!-- <p>Featued Post (makes last Post bigger than others)</p>
                                                <label class="toggle-switch">
                                                    <input type="checkbox" v-model="element.useFeaturedLayout">
                                                    <span class="slider"></span>
                                                </label> -->
                                                <!-- create dropdown for appearance -->
                                                <select v-model="element.postAppearance">
                                                    <option value="standard">Standard</option>
                                                    <option value="featured">Featured</option>
                                                </select>
                                            </div>

                                            <!-- Spacing -->
                                            <div v-if="element.uiValues.activeTab === 'spacing'" class="advanced-settings-wrapper p-4">
                                                <!-- <p>element.uiValues: {{element.uiValues}}</p> -->
                                                <?php include('components/advancedSettings/spacing-component.php'); ?>
                                            </div>
                                        </tab-transition-component>
                                    </div>

                                    <!-- //////////////// -->
                                    <!-- Text Image Modul -->
                                    <!-- //////////////// -->
                                    <div v-if="element.type === 'text-image'" class="text-image">
                                        <!-- Content -->
                                        <div v-if="element.uiValues.activeTab === 'content'" class="content-wrapper p-4">
                                            <div class="block-content">
                                                <div class="col col-1" :class="{reverseOrder: element.reverseOrder}">
                                                    <div>
                                                        <label :for="`nw-input-tagline-${element.id}`">tagline</label><br/>
                                                        <input type="text" :id="`nw-input-tagline-${element.id}`" v-model="element.tagline" />
                                                    </div>

                                                    <label :for="`nw-input-${element.id}`">Headline</label><br/>
                                                    <input type="text" :id="`nw-input-${element.id}`" v-model="element.text" />

                                                    <ul v-if="element.textContent?.length"> 
                                                        <li v-for="(textContent, index) in element.textContent">
                                                            <!-- Delete button -->
                                                            <span @click="deleteTextContent(element, index, itemIndex)" class="btn">X</span>

                                                            <!-- if type is headline or paragraph -->
                                                            <div v-if="textContent.type === 'headline' || textContent.type === 'paragraph'">
                                                                <label :for="`nw-input-${element.id}-${index}`">{{textContent.type}}</label><br/>
                                                                <input type="text" :id="`nw-input-${element.id}-${index}`" v-model="textContent.content" />
                                                            </div>
                                                            
                                                            <!-- list -->
                                                            <div v-if="textContent.type === 'list'">
                                                                <ul>
                                                                    <li v-for="(item, itemIndex) in textContent.listItems">
                                                                        <div class="item-list-header flex">
                                                                            <label :for="`nw-input-${element.id}-${index}-${itemIndex}`">Item</label><br/>
                                                                            <span @click="deleteListItem(element, index, itemIndex)" class="btn">X</span>
                                                                        </div>
                                                                        <input type="text" :id="`nw-input-${element.id}-${index}-${itemIndex}`" v-model="item.content" />
                                                                    </li>
                                                                    <li v-if="textContent.listItems?.length === 0">No List Items</li>
                                                                </ul>
                                                                <span @click="addListItem(textContent.listItems)" class="btn">add list item</span>
                                                            </div>

                                                            <!-- button -->
                                                            <div class="inner-element" v-if="textContent.type === 'button'">
                                                                <!-- Button text -->
                                                                <label :for="`nw-input-${element.id}-${index}`">{{textContent.type}} text</label><br/>
                                                                <input type="text" :id="`nw-input-${element.id}-${index}`" v-model="textContent.content" />
                                                                
                                                                <!-- Button Link -->
                                                                <label :for="`nw-input-${element.id}-${index}`">{{textContent.type}} Link</label><br/>
                                                                <input type="text" :id="`nw-input-${element.id}-${index}`" v-model="textContent.link" />

                                                                <!-- useModal Switch -->
                                                                <p>useModal: {{textContent.useModal}}</p>
                                                                <label class="toggle-switch">
                                                                    <input type="checkbox" v-model="element.useImage">
                                                                    <span class="slider"></span>
                                                                </label>
                                                            </div>
                                                        </li>
                                                    </ul>

                                                    <div class="grid" style="grid-template-columns: 1fr 1fr;">
                                                        <span @click="addFieldToBlock(element, 'headline')" class="btn">add headline</span>
                                                        <span @click="addFieldToBlock(element, 'paragraph')" class="btn">add paragraph</span>
                                                        <span @click="addFieldToBlock(element, 'list')" class="btn">add list</span>
                                                        <span @click="addFieldToBlock(element, 'button')" class="btn">add button</span>
                                                    </div>

                                                    <!-- <p>raw element: {{ element }}</p> -->

                                                </div>
                                            
                                                <div class="col col-2">
                                                    <label :for="`nw-image-${element.id}`">Bild hochladen:</label>
                                                    <button type="button" @click="openMediaLibrary(element, 'image')">Bild auswählen</button>
                                                    <div v-if="element.image">
                                                        <img :src="element.image" alt="Uploaded Image" style="max-width: 100%; height: auto;" />
                                                    </div>
                                                </div>
                                            </div>
                                            
                                            <!-- Switch sides -->
                                            <p>Reverse Order: {{element.reverseOrder}}</p>
                                            <label class="toggle-switch">
                                                <input type="checkbox" v-model="element.reverseOrder">
                                                <span class="slider"></span>
                                            </label>
                                        </div>

                                        <!-- Spacing -->
                                        <div v-if="element.uiValues.activeTab === 'spacing'" class="advanced-settings-wrapper p-4">
                                            <!-- <p>element.uiValues: {{element.uiValues}}</p> -->
                                            <?php include('components/advancedSettings/spacing-component.php'); ?>
                                        </div>
                                    </div>


                                    <!-- ////////////////// -->
                                    <!-- Feature Grid Modul -->
                                    <!-- ////////////////// -->
                                    <div v-if="element.type === 'featureGrid'" class="feature-grid">
                                        <tab-transition-component :active-tab-number="element.uiValues.activeTabNumber">
                                            <!-- Content -->
                                            <div v-if="element.uiValues.activeTab === 'content'" class="content-wrapper p-4">
                                                <div class="block-content">
                                                    <div>
                                                        <label :for="`nw-input-tagline-${element.id}`">Tagline</label><br/>
                                                        <input type="text" :id="`nw-input-tagline-${element.id}`" v-model="element.tagline" />
                                                    </div>
                                                    
                                                    <div>
                                                        <label :for="`nw-input-heading-${element.id}`">Heading</label><br/>
                                                        <input type="text" :id="`nw-input-heading-${element.id}`" v-model="element.heading" />
                                                    </div>

                                                    <div>
                                                        <label :for="`nw-input-paragraph-${element.id}`">Absatz</label><br/>
                                                        <textarea :id="`nw-input-paragraph-${element.id}`" v-model="element.paragraph" /></textarea>
                                                    </div>

                                                    <div class="grid" :style="{ 'gridTemplateColumns': `repeat(${element.gridTemplate}, 1fr)` }">
                                                        <div class="grid-item" v-for="(gridItem, index) in element.gridItems" :key="index">
                                                            <div class="grid-item-header">
                                                                
                                                                <!-- edit Grid Item -->
                                                                <!-- <button type="button" @click="editGridItem(element, 'gridItems', index)">
                                                                    <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                                        <path fill-rule="evenodd" d="M9.586 2.586A2 2 0 0 1 11 2h2a2 2 0 0 1 2 2v.089l.473.196.063-.063a2.002 2.002 0 0 1 2.828 0l1.414 1.414a2 2 0 0 1 0 2.827l-.063.064.196.473H20a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2h-.089l-.196.473.063.063a2.002 2.002 0 0 1 0 2.828l-1.414 1.414a2 2 0 0 1-2.828 0l-.063-.063-.473.196V20a2 2 0 0 1-2 2h-2a2 2 0 0 1-2-2v-.089l-.473-.196-.063.063a2.002 2.002 0 0 1-2.828 0l-1.414-1.414a2 2 0 0 1 0-2.827l.063-.064L4.089 15H4a2 2 0 0 1-2-2v-2a2 2 0 0 1 2-2h.09l.195-.473-.063-.063a2 2 0 0 1 0-2.828l1.414-1.414a2 2 0 0 1 2.827 0l.064.063L9 4.089V4a2 2 0 0 1 .586-1.414ZM8 12a4 4 0 1 1 8 0 4 4 0 0 1-8 0Z" clip-rule="evenodd"/>
                                                                    </svg>
                                                                </button> -->

                                                                <!-- duplicate Grid Item -->
                                                                <!-- <button type="button" @click="duplicateGridItem(element, 'gridItems', index)">
                                                                    <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                                        <path fill-rule="evenodd" d="M18 3a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-1V9a4 4 0 0 0-4-4h-3a1.99 1.99 0 0 0-1 .267V5a2 2 0 0 1 2-2h7Z" clip-rule="evenodd"/>
                                                                        <path fill-rule="evenodd" d="M8 7.054V11H4.2a2 2 0 0 1 .281-.432l2.46-2.87A2 2 0 0 1 8 7.054ZM10 7v4a2 2 0 0 1-2 2H4v6a2 2 0 0 0 2 2h7a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3Z" clip-rule="evenodd"/>
                                                                    </svg>
                                                                </button>    -->

                                                                <!-- remove Grid Item -->
                                                                <button type="button" @click="removeItemFromElement(element, 'gridItems', index)">
                                                                    <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                                        <path fill-rule="evenodd" d="M8.586 2.586A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4a2 2 0 0 1 .586-1.414ZM10 6h4V4h-4v2Zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Z" clip-rule="evenodd"/>
                                                                    </svg>
                                                                </button>

                                                            </div>
                                                            <div class="grid-item-content">
                                                                <label :for="`nw-image-${element.id}-${index}`">Asset aus Mediathek:</label>
                                                                <!-- Toogle -->
                                                                <label class="toggle-switch">
                                                                    <input type="checkbox" v-model="gridItem.isIconImage">
                                                                    <span class="slider"></span>
                                                                </label>
                                                                <div v-if="gridItem.isIconImage">
                                                                    <button :id="`nw-image-${element.id}-${index}`" type="button" @click="openMediaLibraryAndSaveUrlAndImage(gridItem.image)">Icon auswählen</button>
                                                                    <div v-if="gridItem.image?.url" class="grid-icon-preview-wrapper">
                                                                        <img :src="gridItem.image.url" alt="Icon" style="height: auto;" />
                                                                    </div>
                                                                </div>
                                                                <div v-if="!gridItem.isIconImage">
                                                                    <textarea :id="`nw-image-${element.id}-${index}`" v-model="gridItem.icon"></textarea>
                                                                </div>
                                                                
                                                                <label :for="`nw-input-title-${element.id}-${index}`">Title</label><br/>
                                                                <input type="text" :id="`nw-input-title-${element.id}-${index}`" v-model="gridItem.title" />

                                                                <label :for="`nw-input-content-${element.id}-${index}`">Content</label><br/>
                                                                <textarea :id="`nw-input-content-${element.id}-${index}`" v-model="gridItem.content"></textarea>

                                                                <!-- <div v-for="(form, index) in availableForms" :ref="index">
                                                                    <span @click="addFormToGridItem(gridItem, form)" class="btn">Add Form</span>
                                                                </div> -->
                                                                <div class="form-select py-4">
                                                                    <select v-model="gridItem.choosenForm">
                                                                        <option value="0">No Form</option>
                                                                        <option v-for="(form, index) in availableForms" :value="form.id">{{form.title}}</option>
                                                                    </select>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <span @click="addFeatureGridItem(element)" class="btn">Add Grid Item</span>
                                                </div>

                                                <div class="additional-settings">
                                                    <!-- grid template -->
                                                    <div class="grid-template-settings py-4">
                                                        <label>Grid Template / Anzahl der Spalten</label>
                                                        <select v-model="element.gridTemplate">
                                                            <option value="1">1</option>
                                                            <option value="2">2</option>
                                                            <option value="3">3</option>
                                                            <option value="4">4</option>
                                                            <option value="5">5</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>


                                            <!-- Appearance -->
                                            <div v-if="element.uiValues.activeTab === 'appearance'" class="appearance-wrapper p-4">

                                                <!-- center grid -->
                                                <div v-if="element.centerContent !== undefined" class="flex-row">
                                                    <div class="input-group row full">
                                                        <p>Center grid items:</p>
                                                        <label class="toggle-switch">
                                                            <input type="checkbox" v-model="element.centerGirdItems">
                                                            <span class="slider"></span>
                                                        </label>
                                                    </div>
                                                </div>

                                                <!-- center content -->
                                                <div v-if="element.centerContent !== undefined">
                                                    <p>center content: </p>
                                                    <label class="toggle-switch">
                                                        <input type="checkbox" v-model="element.centerContent">
                                                        <span class="slider"></span>
                                                    </label>
                                                </div>

                                                <!-- Include Numbers -->
                                                <div v-if="element.useNumbersAboveIcon !== undefined">
                                                    <p>Nummern auf Icons/Images (durchnummeriert): {{element.useNumbersAboveIcon}}</p>
                                                    <label class="toggle-switch">
                                                        <input type="checkbox" v-model="element.useNumbersAboveIcon">
                                                        <span class="slider"></span>
                                                    </label>
                                                </div>

                                                <!-- disableModule -->
                                                <p>Disable Module: {{element.disableModule}}</p>
                                                <label class="toggle-switch">
                                                    <input type="checkbox" v-model="element.disableModule">
                                                    <span class="slider"></span>
                                                </label>
                                            </div>


                                            <!-- Spacing -->
                                            <div v-if="element.uiValues.activeTab === 'spacing'" class="advanced-settings-wrapper p-4">
                                                <!-- <p>element.uiValues: {{element.uiValues}}</p> -->
                                                <?php include PLUGIN_ROOT_PATH . 'components/advancedSettings/spacing-component.php'; ?>
                                            </div>
                                        </tab-transition-component>
                                    </div>


                                    <!-- ////////////// -->
                                    <!-- Slider Section -->
                                    <!-- ////////////// -->
                                    <?php include('components/slider-section.php'); ?>
                                    
                                
                                    <!-- //////////////// -->
                                    <!-- Timeline Section -->
                                    <!-- //////////////// -->
                                    <?php include('components/timeline-section.php'); ?>
                                
                                
                                    <!-- ////////////////// -->
                                    <!-- Pricing Table Modul -->
                                    <!-- ////////////////// -->
                                    <div v-if="element.type === 'pricing-table' && element.id.length" class="pricing-table">
                                        <!-- Content -->
                                        <div v-if="element.uiValues.activeTab === 'content'" class="content-wrapper p-4">
                                            <div class="block-content">
                                                <!-- Tagline -->
                                                <div>
                                                    <label :for="`nw-input-tagline-${element.id}`">Tagline</label><br/>
                                                    <input type="text" :id="`nw-input-tagline-${element.id}`" v-model="element.tagline" />
                                                </div>

                                                <!-- Headline -->
                                                <div>
                                                    <label :for="`nw-input-headline-${element.id}`">Headline</label><br/>
                                                    <input type="text" :id="`nw-input-headline-${element.id}`" v-model="element.headline" />
                                                </div>

                                                <!-- Content -->
                                                <div>
                                                    <label :for="`nw-input-content-${element.id}`">Content</label><br/>
                                                    <textarea :id="`nw-input-content-${element.id}`" v-model="element.content"></textarea>
                                                </div>

                                                <div class="actual-pricing-table">
                                                    <!-- Pricing Header -->
                                                    <div class="pricing-header" style="display: grid;" :style="{ 'grid-template-columns': `repeat(${element.pricingColCount + 1}, 1fr)` }">
                                                        <div class="just-for-space"></div>
                                                        <div v-for="(pricingColumn, index) in element.pricingColumns" :key="index">
                                                            <!-- <p>{{pricingColumn.title}}</p> -->
                                                            <!-- Column Title -->
                                                            <label :for="`nw-input-title-${element.id}-${index}`">Column Title</label><br/>
                                                            <input type="text" :id="`nw-input-title-${element.id}-${index}`" v-model="pricingColumn.title" />

                                                            <!-- Column Description -->
                                                            <label :for="`nw-input-description-${element.id}-${index}`">Description</label><br/>
                                                            <textarea :id="`nw-input-description-${element.id}-${index}`" v-model="pricingColumn.description"></textarea>
                                                        </div>
                                                    </div>

                                                    <!-- Pricing Feature -->
                                                    <div>
                                                        <div class="pricing-feature-rows" v-for="(masterRow, index) in element.masterPricingColumn?.rows" :key="index" :style="{ 'grid-template-columns': `repeat(${element.pricingColCount + 1}, 1fr)` }">
                                                            <div class="grid-item-content">
                                                                <div class="feature-header" style="display: flex;">
                                                                    <span @click="deletePricingTableRow(element, index)" class="btn">X</span>
                                                                </div>
                                                                <div class="feature-text-wrapper">
                                                                    <label :for="`nw-input-row-${element.id}-${index}`">Feature Content</label><br/>
                                                                    <textarea :id="`nw-input-row-${element.id}-${index}`" v-model="masterRow.name"></textarea>
                                                                </div>
                                                            </div>
                                                            <div v-for="(pricingColumn, pricingColumnIndex) in element?.pricingColumns" :key="pricingColumnIndex" class="grid-item-content">
                                                                <div v-if="pricingColumn.rows && pricingColumn.rows[index]">
                                                                    <!-- <label :for="`nw-checkbox-row-${element.id}-${index}-${pricingColumnIndex}`">checked or not</label><br/> -->
                                                                    <input type="checkbox" :id="`nw-checkbox-row-${element.id}-${index}-${pricingColumnIndex}`" v-model="pricingColumn.rows[index].check" />
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <div class="pricing-feature-rows" :style="{ 'grid-template-columns': `repeat(${element.pricingColCount + 1}, 1fr)` }">
                                                            <div class="grid-item-content">    
                                                                <span @click="addPricingTableRow(element)" class="btn">
                                                                    Add Row
                                                                </span>
                                                            </div>
                                                            <div class="grid-item-content"></div>
                                                            <div class="grid-item-content"></div>
                                                        </div>
                                                    </div>

                                                    <!-- Pricing footer  -->
                                                    <div class="pricing-footer" style="display: grid;" :style="{ 'grid-template-columns': `repeat(${element.pricingColCount + 1}, 1fr)` }">
                                                        <div class="grid-item"></div>
                                                        <div class="grid-item" v-for="(pricingColumn, index) in element.pricingColumns" :key="index">
                                                            <div class="grid-item-content">
                                                                <!-- CTA Row -->
                                                                <div class="cta-col">
                                                                    <label :for="`nw-input-price-${element.id}-${index}`">Price</label><br/>
                                                                    <input type="text" :id="`nw-input-price-${element.id}-${index}`" v-model="pricingColumn.ctaRow.price" />


                                                                    <!-- Additional Rows in CTA -->
                                                                    <div v-for="(ctaRow, ctaRowIndex) in pricingColumn.ctaRow.rows" :key="ctaRowIndex" class="cta-row-wrapper">
                                                                        <span @click="deletePricingTableCtaRowInnerRow(pricingColumn.ctaRow, ctaRowIndex)" class="btn">X</span>
                                                                        <div class="content">
                                                                            <div class="left-content">
                                                                                <label :for="`nw-input-cta-left-content-${element.id}-${index}-${ctaRowIndex}`">Left Content</label><br/>
                                                                                <textarea :id="`nw-input-cta-right-content-${element.id}-${index}-${ctaRowIndex}`" v-model="ctaRow.leftContent"></textarea>
                                                                            </div>
                                                                            <div class="right-content">
                                                                                <label :for="`nw-input-cta-left-content-${element.id}-${index}-${ctaRowIndex}`">Right Content</label><br/>
                                                                                <textarea :id="`nw-input-cta-right-content-${element.id}-${index}-${ctaRowIndex}`" v-model="ctaRow.rightContent"></textarea>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <span @click="addPricingTableCtaRowInnerRow(pricingColumn.ctaRow)" class="btn">Add CTA Row</span>


                                                                    <!-- CTA -->
                                                                    <div class="cta-btn-wrapper">
                                                                        <label :for="`nw-input-button-text-${element.id}-${index}`">Button Text</label><br/>
                                                                        <input type="text" :id="`nw-input-button-text-${element.id}-${index}`" v-model="pricingColumn.ctaRow.buttonText" />

                                                                        <label :for="`nw-input-button-link-${element.id}-${index}`">Button Link</label><br/>
                                                                        <input type="text" :id="`nw-input-button-link-${element.id}-${index}`" v-model="pricingColumn.ctaRow.buttonLink" />

                                                                        <!-- Use Modal -->
                                                                        <p>Use Modal: {{pricingColumn.ctaRow.useModal}}</p>
                                                                        <label class="toggle-switch">
                                                                            <input type="checkbox" v-model="pricingColumn.ctaRow.useModal">
                                                                            <span class="slider"></span>
                                                                        </label>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                    
                                                <!-- <span @click="addPricingTableOfferCol(element)" class="btn">Add Offer Column</span> -->
                                            </div>
                                        </div>

                                        <!-- Appearance -->
                                        <div v-if="element.uiValues.activeTab === 'appearance'" class="appearance-wrapper p-4">
                                            <div class="additional-settings">
                                                <!-- Pricing Column Count -->
                                                <label>Number of Columns</label>
                                                <select v-model.number="element.pricingColCount" @change="handlePricingColCountChangeOnUI(element, $event)">
                                                    <option value="1">1</option>
                                                    <option value="2">2</option>
                                                    <option value="3">3</option>
                                                    <option value="4">4</option>
                                                    <option value="5">5</option>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- Spacing -->
                                        <div v-if="element.uiValues.activeTab === 'spacing'" class="content-wrapper p-4">
                                            <?php include('components/advancedSettings/spacing-component.php'); ?>
                                        </div>
                                    </div>


                                    <!-- /////////////// -->
                                    <!-- Agenda Overview -->
                                    <!-- /////////////// -->
                                    <div v-if="element.type === 'agenda-overview'" class="agenda-overview">
                                        <!-- Content -->
                                        <div v-if="element.uiValues.activeTab === 'content'" class="content-wrapper p-4">
                                            <div class="grid">
                                                <div>
                                                    <div>
                                                        <label :for="`nw-input-tagline-${element.id}`">tagline</label><br/>
                                                        <input type="text" :id="`nw-input-tagline-${element.id}`" v-model="element.tagline" />
                                                    </div>
                                                    <div>
                                                        <label :for="`nw-input-heading-${element.id}`">Heading</label><br/>
                                                        <input type="text" :id="`nw-input-heading-${element.id}`" v-model="element.heading" />
                                                    </div>
                                                    <div>
                                                        <label :for="`nw-input-content-${element.id}`">Content</label><br/>
                                                        <textarea :id="`nw-input-content-${element.id}`" v-model="element.content"></textarea>
                                                    </div>
                                                </div>

                                                <!--
                                                <div>
                                                    <ul v-if="!element.useImage">
                                                        <li v-for="(cta, index) in element.ctas" :key="index">
                                                            <label :for="`nw-input-cta-text-${element.id}-${index}`">CTA Text</label><br/>
                                                            <input type="text" :id="`nw-input-cta-${element.id}-${index}`" v-model="cta.text" />

                                                            <label :for="`nw-input-cta-link-${element.id}-${index}`">CTA Link</label><br/>
                                                            <input type="text" :id="`nw-input-cta-link-${element.id}-${index}`" v-model="cta.link" />
                                                            
                                                            <p>useModal: {{cta.useModal}}</p>
                                                            <label class="toggle-switch">
                                                                <input type="checkbox" v-model="cta.useModal">
                                                                <span class="slider"></span>
                                                            </label>
                                                        </li>
                                                    </ul>
                                                </div>
                                                -->

                                                <!-- Datum auswählen -->
                                                <p>availableDates: {{availableDates}}</p>
                                                <div>
                                                    <label :for="`nw-input-date-${element.id}`">Event Date</label><br/>
                                                    <select :id="`nw-input-date-${element.id}`" v-model="element.selectedDate">
                                                        <option v-for="date in availableDates" :value="date">{{ date }}</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    

                                    <!-- Pricing table old -->
                                    <div v-if="element.type === 'pricing-table-old'" class="pricing-table">
                                        <!-- Content -->
                                        <div v-if="element.uiValues.activeTab === 'content'" class="content-wrapper p-4">
                                            <div class="block-content">
                                                <!-- Tagline -->
                                                <div>
                                                    <label :for="`nw-input-tagline-${element.id}`">Tagline</label><br/>
                                                    <input type="text" :id="`nw-input-tagline-${element.id}`" v-model="element.tagline" />
                                                </div>

                                                <!-- Headline -->
                                                <div>
                                                    <label :for="`nw-input-headline-${element.id}`">Headline</label><br/>
                                                    <input type="text" :id="`nw-input-headline-${element.id}`" v-model="element.headline" />
                                                </div>

                                                <!-- Content -->
                                                <div>
                                                    <label :for="`nw-input-content-${element.id}`">Content</label><br/>
                                                    <textarea :id="`nw-input-content-${element.id}`" v-model="element.content"></textarea>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Spacing -->
                                        <div v-if="element.uiValues.activeTab === 'spacing'" class="content-wrapper p-4">
                                            <?php include('components/advancedSettings/spacing-component.php'); ?>
                                        </div>
                                    </div>

                                    <!-- Image Grid Modul -->
                                    <div v-if="element.type === 'imageGrid'" class="image-grid">
                                        <!-- Content -->
                                        <div v-if="element.uiValues.activeTab === 'content'" class="content-wrapper p-4">
                                            <div class="block-content">
                                                <div>
                                                    <label :for="`nw-input-heading-${element.id}`">Heading</label><br/>
                                                    <input type="text" :id="`nw-input-heading-${element.id}`" v-model="element.heading" />
                                                </div>
                                                
                                                <div>
                                                    <label :for="`nw-input-paragraph-${element.id}`">Absatz</label><br/>
                                                    <textarea :id="`nw-input-paragraph-${element.id}`" v-model="element.paragraph" /></textarea>
                                                </div>

                                                <div class="old-deprecated-grid" >
                                                    <!-- <div class="grid-item" v-for="(gridItem, index) in element.gridItems" :key="index"> -->
                                                    <!-- <draggable 
                                                        :list="element.gridItems" 
                                                        group="grid" 
                                                        :animation="200" 
                                                        ghostClass="ghost" 
                                                        class="grid-item" 
                                                        :handle="`#nw-image-${element.id}-${index}`"
                                                    >
                                                        <template # -->
                                                    <draggable 
                                                        v-if="element.gridItems?.length" 
                                                        v-model="element.gridItems" 
                                                        :group="{ name: 'gridItems', pull: 'clone', put: false }" 
                                                        item-key="id" 
                                                        class="gridItems-draggable grid" 
                                                        :style="{ 'gridTemplateColumns': `repeat(${element.gridTemplate}, 1fr)` }"
                                                        @end="onDragEnd"
                                                    >
                                                        <template #item="{ element: gridItem, index }">
                                                            <div class="grid-item-wrapper grid-item">
                                                                <div class="button-wrapper">
                                                                    <p>use Video: {{element.useVideo}}</p>
                                                                    <label class="toggle-switch">
                                                                        <input type="checkbox" v-model="gridItem.useVideo">
                                                                        <span class="slider"></span>
                                                                    </label>
                                                                    <!-- attach Image -->
                                                                    <div v-if="!gridItem.useVideo" class="inner-button-wrapper">
                                                                        <button :id="`nw-image-${element.id}-${index}`" type="button" @click="openMediaLibrary(gridItem, 'iconUrl')">Bild auswählen</button>
                                                                        <button :id="`nw-image-${element.id}-${index}-delete`" type="button" @click="deleteImageGirdItem(element, index)">X</button>
                                                                    </div>
                                                                    <!-- attach Video -->
                                                                    <div v-if="gridItem.useVideo" class="inner-button-wrapper">
                                                                        <!-- Attach Thumbnail -->
                                                                        <button :id="`nw-image-${element.id}-${index}`" type="button" @click="openMediaLibrary(gridItem, 'thumbnailUrl')">Thumbnail auswählen</button>
                                                                        <!-- Attach Video -->
                                                                        <button :id="`nw-image-${element.id}-${index}`" type="button" @click="openMediaLibrary(gridItem, 'videoUrl')">Video auswählen</button>
                                                                        <!-- Delete whole item -->
                                                                        <button :id="`nw-image-${element.id}-${index}-delete`" type="button" @click="deleteImageGirdItem(element, index)">Delete item</button>
                                                                    </div>
                                                                </div>

                                                                <div class="grid-item-content">
                                                                    <div v-if="gridItem.iconUrl && !gridItem.useVideo" class="image-wrapper">
                                                                        <img :src="gridItem.iconUrl" alt="Icon" style="max-width: 100px; height: auto;" />
                                                                    </div>
                                                                    <div v-if="gridItem.thumbnailUrl && gridItem.useVideo" class="image-wrapper">
                                                                        <img :src="gridItem.thumbnailUrl" alt="Icon" style="max-width: 100px; height: auto;" />
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </template> 
                                                    </draggable>
                                                    <!-- </div> -->
                                                    <div class="grid-item new-item" @click="addImageGridItem(element)" class="btn" style="color: #fff; cursor: pointer;">
                                                        Add Grid Item
                                                        <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14m-7 7V5"/>
                                                        </svg>
                                                    </div>
                                                </div>

                                            </div>

                                            <div class="additional-settings">
                                                <!-- grid template -->
                                                <label>Grid Template / Anzahl der Spalten</label>
                                                <select v-model="element.gridTemplate">
                                                    <option value="1">1</option>
                                                    <option value="2">2</option>
                                                    <option value="3">3</option>
                                                    <option value="4">4</option>
                                                </select>
                                            </div>
                                        </div>

                                        <!-- Apperance -->
                                        <div v-if="element.uiValues.activeTab === 'appearance'" class="appearance-wrapper p-4">
                                            <h2>appearance</h2>
                                        </div>

                                        <!-- Spacing -->
                                        <div v-if="element.uiValues.activeTab === 'spacing'" class="spacing-wrapper p-4">
                                            <?php include('components/advancedSettings/spacing-component.php'); ?>
                                        </div>
                                    </div>


                                    <!-- CTA section -->
                                    <div v-if="element.type === 'cta-section'" class="cta-section">
                                        <tab-transition-component :active-tab-number="element.uiValues.activeTabNumber">
                                            <!-- Content -->
                                            <div v-if="element.uiValues.activeTab === 'content'" class="content-wrapper p-4">
                                                <div class="block-content">
                                                    <div class="grid col-2">
                                                        <div>
                                                            <div>
                                                                <label :for="`nw-input-tagline-${element.id}`">tagline</label><br/>
                                                                <input type="text" :id="`nw-input-tagline-${element.id}`" v-model="element.tagline" />
                                                            </div>
                                                            <div>
                                                                <label :for="`nw-input-heading-${element.id}`">Heading</label><br/>
                                                                <input type="text" :id="`nw-input-heading-${element.id}`" v-model="element.heading" />
                                                            </div>
                                                            <div>
                                                                <label :for="`nw-input-content-${element.id}`">Content</label><br/>
                                                                <textarea :id="`nw-input-content-${element.id}`" v-model="element.content"></textarea>
                                                            </div>

                                                            <div class="quote" v-if="element.useQuote">
                                                                <label :for="`nw-input-quote-${element.id}`">Quote</label><br/>
                                                                <textarea :id="`nw-input-quote-${element.id}`" v-model="element.quote"></textarea>
                                                            </div>

                                                            <!-- Show CTAs if image is not used -->
                                                            <ul v-if="element.useImage">
                                                                <li v-for="(cta, index) in element.ctas" :key="index">
                                                                    <label :for="`nw-input-cta-text-${element.id}-${index}`">CTA Text</label><br/>
                                                                    <input type="text" :id="`nw-input-cta-${element.id}-${index}`" v-model="cta.text" />

                                                                    <label :for="`nw-input-cta-link-${element.id}-${index}`">CTA Link</label><br/>
                                                                    <input type="text" :id="`nw-input-cta-link-${element.id}-${index}`" v-model="cta.link" />
                                                                    <!-- <p>cta: {{cta.text}} | {{cta.link}} | {{index}}</p> -->
                                                                    <!-- Switch sides -->
                                                                    <p>useModal: {{cta.useModal}}</p>
                                                                    <label class="toggle-switch">
                                                                        <input type="checkbox" v-model="cta.useModal">
                                                                        <span class="slider"></span>
                                                                    </label>
                                                                </li>
                                                            </ul>
                                                        </div>

                                                        <div>
                                                            <!-- Show CTAs if image is not used -->
                                                            <ul v-if="!element.useImage">
                                                                <li v-for="(cta, index) in element.ctas" :key="index">
                                                                    <label :for="`nw-input-cta-text-${element.id}-${index}`">CTA Text</label><br/>
                                                                    <input type="text" :id="`nw-input-cta-${element.id}-${index}`" v-model="cta.text" />

                                                                    <label :for="`nw-input-cta-link-${element.id}-${index}`">CTA Link</label><br/>
                                                                    <input type="text" :id="`nw-input-cta-link-${element.id}-${index}`" v-model="cta.link" />
                                                                    <!-- <p>cta: {{cta.text}} | {{cta.link}} | {{index}}</p> -->
                                                                    <!-- Switch sides -->
                                                                    <p>useModal: {{cta.useModal}}</p>
                                                                    <label class="toggle-switch">
                                                                        <input type="checkbox" v-model="cta.useModal">
                                                                        <span class="slider"></span>
                                                                    </label>
                                                                </li>
                                                            </ul>

                                                            <!-- Show image if image is used -->
                                                            <div v-if="element.useImage">
                                                                <button type="button" @click="openMediaLibrary(element, 'image')" >Bild auswählen</button>
                                                                <div v-if="element.image">
                                                                    <img :src="element.image" alt="Uploaded Image" style="max-width: 100%; height: auto;" />
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Appearance -->
                                            <div v-if="element.uiValues.activeTab === 'appearance'" class="appearance-wrapper p-4">
                                                <!-- Use Image -->
                                                <p>useImage: {{element.useImage}}</p>
                                                <label class="toggle-switch">
                                                    <input type="checkbox" v-model="element.useImage">
                                                    <span class="slider"></span>
                                                </label>

                                                <!-- use Background Image -->
                                                <p>useBackgroundImage: {{element.useBackgroundImage}}</p>
                                                <label class="toggle-switch">
                                                    <input type="checkbox" v-model="element.useBackgroundImage">
                                                    <span class="slider"></span>
                                                </label>

                                                <br>
                                                <!-- set backgroundImage -->
                                                <div class="backround-image-wrapper" v-if="element.useBackgroundImage">
                                                    <p>element.backgroundImage: {{element.backgroundImage}}</p>
                                                    <span class="btn" @click="openMediaLibrary(element, 'backgroundImage')" >Background Image auswählen</span>
                                                    <span class="btn" @click="element.backgroundImage = ''" >Delete Image</span>
                                                    <div v-if="element.backgroundImage">
                                                        <img :src="element.backgroundImage" alt="Uploaded Image" style="max-width: 100%; max-height: 240px; height: auto;" />
                                                    </div>
                                                </div>

                                                <!-- use Background Image -->
                                                <p>useQuote: {{element.useQuote}}</p>
                                                <label class="toggle-switch">
                                                    <input type="checkbox" v-model="element.useQuote">
                                                    <span class="slider"></span>
                                                </label>

                                            </div>

                                            <!-- Spacing -->
                                            <div v-if="element.uiValues.activeTab === 'spacing'" class="spacing-wrapper p-4">
                                                <?php include('components/advancedSettings/spacing-component.php'); ?>
                                            </div>
                                        </tab-transition-component>
                                    </div>


                                    <!-- Sticky Text-Image-Section -->
                                    <?php include PLUGIN_ROOT_PATH . 'components/sticky-text-image-section.php'; ?>


                                    <!-- Conclusion -->
                                    <div v-if="element.type === 'conclusion'" class="conclusion-wrapper p-4">
                                        <tab-transition-component :active-tab-number="element.uiValues.activeTabNumber">
                                            <!-- Content -->
                                            <div v-if="element.uiValues.activeTabNumber === 1" class="content-wrapper">
                                                <div class="block-content">
                                                    <div class="edit-headline" style="margin-bottom: 2rem;">
                                                        <label :for="`nw-headline-${element.id}`">Headline</label>
                                                        <input type="text" v-model="element.headline" :id="`nw-headline-${element.id}`" placeholder="Headline (z.B. Fazit)" />
                                                    </div>
                                                    <textarea 
                                                        :id="'tinymce-' + element.id" 
                                                        :class="'tinymce-' + element.id" 
                                                        :data-flexible-content-index="element.id" 
                                                        :data-item-index="element.id" 
                                                        v-model="element.content">
                                                    </textarea>
                                                </div>
                                            </div>

                                            <!-- Appearance -->
                                            <div v-if="element.uiValues.activeTab === 'appearance'" class="appearance-wrapper">
                                                <h2>Appearance</h2>
                                                <p>no settings available</p>
                                            </div>

                                            <!-- Spacing -->
                                            <div v-if="element.uiValues.activeTab === 'spacing'" class="spacing-wrapper">
                                                <?php include('components/advancedSettings/spacing-component.php'); ?>
                                            </div>
                                        </tab-transition-component>

                                    </div>



                                    <!-- modal module -->
                                    <div v-if="element.type === 'modal'" class="modal-module p-4">
                                        <tab-transition-component :active-tab-number="element.uiValues.activeTabNumber">
                                            <!-- Content -->
                                             <div v-if="element.uiValues.activeTabNumber === 1" class="content-wrapper">
                                                
                                                <label :for="`nw-input-${element.id}-content`">Modal Content</label><br/>
                                                <textarea @click="log(element)" type="text" :id="`nw-input-${element.id}-content`" v-model="element.content">
                                                </textarea>
                                                <label :for="`nw-input-${element.id}-teaser`">Modal Teaser</label><br/>
                                                <textarea @click="log(element)" type="text" :id="`nw-input-${element.id}-teaser`" v-model="element.teaser">
                                                </textarea>
                                                <label :for="`nw-input-${element.id}-buttonText`">Modal Teaser Button Text</label><br/>
                                                <textarea @click="log(element)" type="text" :id="`nw-input-${element.id}-buttonText`" v-model="element.buttonText">
                                                </textarea>
                                            </div>

                                            <!-- Appearance -->
                                            <div v-if="element.uiValues.activeTab === 'appearance'" class="appearance-wrapper">
                                                <h2>Appearance</h2>
                                                <p>no settings available</p>
                                            </div>

                                            <!-- Spacing -->
                                            <div v-if="element.uiValues.activeTab === 'spacing'" class="spacing-wrapper">
                                                <?php include('components/advancedSettings/spacing-component.php'); ?>
                                            </div>
                                        </tab-transition-component>
                                    </div>

                                    <!-- raw html -->
                                    <div v-if="element.type === 'raw-html'" class="html-wrapper p-4">
                                        <tab-transition-component :active-tab-number="element.uiValues.activeTabNumber">
                                            <!-- Content -->
                                            <div v-if="element.uiValues.activeTabNumber === 1" class="content-wrapper">
                                                <div class="block-content">
                                                    <label :for="`nw-input-${element.id}`">HTML-Input:</label><br/>
                                                    <textarea @click="log(element)" type="text" :id="`nw-input-${element.id}`" v-model="element.html">
                                                    </textarea>
                                                </div>
                                            </div>

                                            <!-- Appearance -->
                                            <div v-if="element.uiValues.activeTab === 'appearance'" class="appearance-wrapper">
                                                <h2>Appearance</h2>
                                                <p>no settings available</p>
                                            </div>

                                            <!-- Spacing -->
                                            <div v-if="element.uiValues.activeTab === 'spacing'" class="spacing-wrapper">
                                                <?php include('components/advancedSettings/spacing-component.php'); ?>
                                            </div>
                                        </tab-transition-component>
                                    </div>


                                    <!-- flexible row -->
                                    <div v-if="element.type === 'row' ">
                                        <div v-for="(col, index) in element.cols">
                                            
                                            <div v-for="(module, index) in col.content">
                                                <?php include('components/accordion-component.php'); ?>
                                                <div v-if="module.type === 'text-image'">
                                                    <?php include('components/text-image-component.php'); ?>
                                                </div>
                                            </div>

                                            <!-- <p>col: {{col}}</p> -->

                                            <ul>
                                                <li v-for="availableModule in availableBlocks">
                                                    <span @click="addNewBlockToRow(element, index, availableModule)" class="button">{{availableModule.type}}</span>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>

                                <!-- End minified wrapper -->
                                </div>
                            </div>
                        </template>
                    </draggable>
                </div>

                <!-- TODO: Kann weg inkl. "modalToAddNewBlocksOpen" Variable -->
                <div class="add-block nw-button" style="display: flex; justify-content: center;">
                    <span @click="isAddNewElementLightboxOpen = true" class="btn">Add a new block</span>
                </div>
                <!-- <div class="backend-modal-wrapper" v-if="modalToAddNewBlocksOpen">
                    <div class="backend-modal">
                        <h2>Choose Block</h2>
                        <ul>
                            <li v-for="availableModule in availableBlocksForType">
                                <span @click="addNewBlock(availableModule)" class="button">{{availableModule.type}}</span>
                            </li>
                        </ul>
                    </div>
                </div> -->

                <div v-if="enableAdvancedJsonEditing" class="paste-json-block" style="display: flex;">
                    <input type="text" v-model="pastedJsonBlock" />
                    <span class="btn" @click="pasteJsonBlockToPage">Paste</span>
                </div>



                <div v-if="postType === 'page'">
                    <h2 style="padding: 0;>Page Settings</h2>
                    <div v-if="frontendSettings">
                        <p>Invert Header before scroll:</p>
                        <label class="toggle-switch">
                            <input type="checkbox" v-model="frontendSettings.invertHeader">
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>


                <!-- <div class="save-block nw-button">
                    <p @click="saveBlocks">Save Page Builder Data</p>
                </div> -->
            </div>
        
        </div>

        <script type="module">
        const { createApp, ref, onMounted, onUpdated, onUnmounted, nextTick, watch, computed } = Vue

        import ParagraphComponent from '<?php echo plugin_dir_url(__FILE__) . 'components/ParagraphComponent.js'?>';

        const app = createApp({
            components: {
                'paragraph-component': ParagraphComponent,
            },

            setup() {

                const isUserAdmin = <?php echo current_user_can('manage_options') ? 'true' : 'false'; ?>;

                /** 
                 * # Utils 
                 * 
                 * Helper functions:
                 *   - log
                 *   - logIndices
                 *   - generateUniqueId
                 * 
                 * 
                 */

                // log
                const log = (element) => {
                    console.log(element);
                }

                // log indices
                const logIndices = (index, index2) => {
                    console.log('index: ', index);
                    console.log('index2: ', index2);
                }

                // generate uid
                const generateUniqueId = () => {
                    let randomString = Math.random().toString(36).substr(2, 9);
                    return `nw-${randomString}`;
                }

                // deep copy 
                const deepCopy = (obj) => {
                    return JSON.parse(JSON.stringify(obj));
                }

                const arraysAreEqual = (arr1, arr2) => {
                    if(!arr1 || !arr2) return false

                    if (arr1.length !== arr2.length) return false;
                    return arr1.every((value, index) => value === arr2[index]);
                };

                const truncateText = (text, maxLength) => {
                    if (text.length <= maxLength) {
                        return text;
                    }
                    const truncated = text.slice(0, maxLength);
                    const lastSpaceIndex = truncated.lastIndexOf(' ');

                    // Schneide das Textstück bis zum letzten Leerzeichen, um ein Wort nicht zu zerschneiden.
                    return truncated.slice(0, lastSpaceIndex) + '...';
                };

                // Methode zum Laden der Bild-URL
                const fetchImageUrl = async (mediaId) => {
                    try {
                        const response = await fetch(`/wp-content/themes/dein-theme/get-image-url.php?mediaId=${mediaId}`)
                        const data = await response.json()
                        imageUrl.value = data.url
                    } catch (error) {
                        console.error('Error fetching image URL:', error)
                    }
                }


                /**
                 * Global Variables to manipulate the UI
                 */
                const isAddNewElementLightboxOpen = ref(false) // Add new element lightbox

            

                /**
                 * # Data from Theme Settings 
                 * responsible to get data from global "Page Builder" settings 
                 * 
                 * theme_settings:
                 *   - htmlForms
                 * 
                 */
                const themeSettings = JSON.parse(<?php echo json_encode($theme_settings); ?>);
                console.log('themeSettings: ', themeSettings);
                const availableForms = ref(themeSettings.htmlForms);
                console.log('%c availableForms: ', 'background: #222; color: #bada55', availableForms.value);
                
                // const blocks = ref(Array.isArray(initialBlocks) ? initialBlocks : []);
                const blocks = ref(initialBlocksUi ? initialBlocksUi : []);

                /**
                 * UI values for Frontend
                 * 
                 * frontend_settings:
                 *   - invertHeader
                 * 
                 */
                const defaultFrontendSettings = { invertHeader: false };
                const frontendSettings = ref(initialFrontendSettings ? initialFrontendSettings : defaultFrontendSettings);


                /** 
                 * # Additional data needet from CPT: 
                 * 
                 * additional_data from CPT:
                 *   - available_dates (from sessions CPT)
                 * 
                 */ 
                
                // Session Dates 
                const availableDates = ref(<?php echo json_encode($available_dates); ?>); // available_dates


                

                /**
                 * UI values for Backend
                 */
                const isFullscreen = ref(false);
                const showPreview = ref(false);

                /**
                 * Meta Box / Page Builder data 
                 */
                const togglePreview = () => {
                    console.log('togglePreview called');
                    // showPreview.value = !showPreview.value
                    pageBuilderMetaBox.classList.toggle('preview');  
                }
                
                const pageBuilderMetaBox = document.getElementById('grenzlotsen_page_builder_id');
                const toggleFullscreen = () => {
                    pageBuilderMetaBox.classList.toggle('fullscreen');
                    isFullscreen.value = !isFullscreen.value
                }

                
                /**
                 * # Advanced JSON-Editing
                 * 
                 *
                 */

                const enableAdvancedJsonEditing = ref(false);
                const pastedJsonBlock = ref('');
                const blockAsString = ref([]);

                // displaycurrent Page-Builder-JSON in input field
                onMounted(() => {
                    try {
                        blockAsString.value = JSON.stringify(blocks.value)
                    } catch (error) {
                        console.log('Invalid JSON', error);
                    }
                })

                // Update whole page 
                const updateBlocks = () => {
                    blocks.value = JSON.parse(blockAsString.value);
                }

                // Paste JSON block to page 
                const pasteJsonBlockToPage = () => {
                    // Debug-Ausgabe
                    console.log('%c pasteJsonBlockToPage: ', 'background: #222; color: #bada55', pastedJsonBlock.value);

                    // Block in JSON umwandeln
                    const newBlock = JSON.parse(pastedJsonBlock.value);
                    newBlock.id = generateUniqueId();
                    console.log('newBlock: ', newBlock);

                    // Block in das Array eintragen
                    blocks.value.push(newBlock);
                }


                /**
                 * Block-Level Functions 
                 * 
                 * - duplicateBlock
                 * - copyBlock
                 * - saveBlocks
                 * 
                 */

                // duplicate block 
                const duplicateBlock = (block, index) => {
                    console.log('%c duplicateBlock: ', 'background: #222; color: #bada55', block, index);
                    const newBlock = JSON.parse(JSON.stringify(block));
                    newBlock.id = generateUniqueId();
                    // newBlock.order = blocks.value.length + 1;
                    // blocks.value.push(newBlock);

                    // Füge das neue Objekt direkt nach dem alten in das Array ein
                    blocks.value.splice(index + 1, 0, newBlock);
                }

                // copy block
                const copyBlock = (block, index) => {
                    // Debug-Ausgabe
                    console.log('%c copyBlock: ', 'background: #222; color: #bada55', block, index);

                    // Block klonen
                    const newBlock = JSON.parse(JSON.stringify(block));
                    newBlock.id = generateUniqueId();

                    // Block in JSON umwandeln
                    const newBlockString = JSON.stringify(newBlock);
                    console.log('newBlockString: ', newBlockString);

                    // Kopieren in die Zwischenablage mit Fehlerbehandlung
                    if (navigator.clipboard) {
                        navigator.clipboard.writeText(newBlockString)
                            .then(() => console.log('Block erfolgreich kopiert!'))
                            .catch(err => console.error('Fehler beim Kopieren in die Zwischenablage:', err));
                    } else {
                        console.error('Clipboard API wird nicht unterstützt.');
                    }
                };

                // Save Blocks 
                // TODO: not sure if i want to use this
                //       I should check if this funktion triggers the revisions 
                const saveBlocks = () => {
                    debugger; 

                    console.log('saveBlocks called');
                    const form = document.querySelector('#post');
                    const input = document.createElement('input');
                    input.type = 'text';
                    input.name = 'page_builder_blocks';
                    input.value = JSON.stringify(blocksWithoutUiValues.value);
                    form.appendChild(input);
                };




                // Accordion Items
                const blankAccordionItem = {
                    question: '',
                    answer: '',
                }
                const addAccordionItem = (module) => {
                    module.accordionItems.push({...blankAccordionItem });
                }
                const deleteAccordionItem = (module, index) => {
                    module.accordionItems.splice(index, 1);
                }



                /** 
                 * Flexible Content Settings 
                 * 
                 */
                const availableColumnStyles = ['default', 'shadow-border'];

                /*** COL SETTINGS */
                const singleColSettings = ref({
                    // appearance
                    style: 'default',
                    centerContent: false,
                    // advanced styles 
                    editing: false,
                    width: 12,
                    color: '',
                    background: {
                        color: '',
                        backgroundImage: '',
                        backgroundPosition: '',
                        useGradient: false,
                        gradient: {
                            color1: '#fff',
                            position1: 0,
                            color2: '#000',
                            position2: 100,
                            angle: 0,
                            gradientType: 'linear',
                            circlePositionX: 50,
                            circlePositionY: 50,
                            gradient: 'linear-gradient(0deg, #fff 0%, #000 100%)',
                        },
                        forceTextColor: false,
                        textColor: '#000000',
                    },
                    margin: {
                        top: '',
                        right: '',
                        bottom: '',
                        left: '',
                    },
                    padding: {
                        top: '',
                        right: '',
                        bottom: '',
                        left: '',
                    },
                    translate: {
                        x: '',
                        y: '',
                    },
                    border: {
                        style: 'none',
                        width: '',
                        color: '',
                    },
                    borderRadius: {
                        isBorderRadiusSynced: true,
                        // seperate Radius for each side
                        topLeft: '',
                        topRight: '',
                        bottomRight: '',
                        bottomLeft: '',
                    }
                });

                const checkIfColSettingsExist = (element) => {
                    // return false 
                    console.log('checkIfColSettingsExist called');
                    console.log('element: ', element);
                    console.log('element.colSettings: ', element.colSettings);
                    console.log('new element.colSettings: ', JSON.parse(JSON.stringify(singleColSettings.value)));
                    if(element.colSettings === undefined) {
                        console.log('colSettings does not exist');
                        element.colSettings = [];
                        for(let i = 0; i < element.flexibleContent.length; i++) {
                            element.colSettings.push(JSON.parse(JSON.stringify(singleColSettings.value)))
                        }
                        console.log('colSettings created');
                        return true
                    } else {
                        console.log('colSettings already exists');
                        return true
                    }
                }

                const editCol = (colSettings) => {
                    console.log('editCol called', colSettings);
                    colSettings.editing = true;
                    colSettings.activeTabNumber = 1;
                }

                const syncBorderRadius = (colSettings, changedCorner) => {
                    console.log('colSettings: ', colSettings);
                    if (!colSettings.borderRadius.isBorderRadiusSynced) {
                        console.log('isBorderRadiusSynced is false – do nothing');
                        retun 
                    }

                    const newValue = colSettings.borderRadius[changedCorner];
                    colSettings.borderRadius.topLeft = newValue;
                    colSettings.borderRadius.topRight = newValue;
                    colSettings.borderRadius.bottomRight = newValue;
                    colSettings.borderRadius.bottomLeft = newValue;
                }

                const increaseColWidth = (element, index) => {
                    console.log('increaseColWidth called');
                    console.log('element: ', element);
                    console.log('index: ', index);

                    // just a hack to make it work for older blocks that don't have colSettings
                    if(!element.colSettings) {
                        element.colSettings = []
                        
                        // element.flexibleContent.forEach((contentArray, index) => {
                        //     col.colSettings = JSON.parse(JSON.stringify(singleColSettings.value));
                        // })
                        for(let i = 0; i < element.flexibleContent.length; i++) {
                            element.colSettings.push(JSON.parse(JSON.stringify(singleColSettings.value)));
                        }
                        console.log('element.colSettings[index]: ', element.colSettings[index])
                    }


                    // console.log('element.colSettings[index].width: ', element.colSettings[index].width)
                    element.colSettings[index].width += 1;
                    console.log('element.colSettings[index].width: ', element.colSettings[index].width)
                }
                const decreaseColWidth = (element, index) => {
                    console.log('decreaseColWidth called');
                    console.log('element: ', element);
                    console.log('index: ', index);

                    // just a hack to make it work for older blocks that don't have colSettings
                    if(!element.colSettings) {
                        element.colSettings = []
                        
                        for(let i = 0; i < element.flexibleContent.length; i++) {
                            element.colSettings.push(JSON.parse(JSON.stringify(singleColSettings.value)));
                        }
                        console.log('element.colSettings[index]: ', element.colSettings[index])
                    }

                    // console.log('element.colSettings[index].width: ', element.colSettings[index].width)
                    element.colSettings[index].width -= 1;
                    console.log('element.colSettings[index].width: ', element.colSettings[index].width)
                }


                


                /**
                 * SingleMultipleImage 
                 */
                const availableFloats = ['left', 'right', 'none'];
                const singleMultipleImage = ref({
                    name: 'singleImage',
                    type: 'singleImage',
                    mediaId: '', 
                    mediaUrl: '',
                    alt: '',
                    styles: {
                        color: '#f3f6fa',
                        opacity: 1,
                        zIndex: 1,
                        // positioning 
                        position: 'relative',
                        marginTop: 0,
                        marginRight: 0,
                        marginBottom: 0,
                        marginLeft: 0,
                        
                        float: '',
                        // mask effects
                        useImageMask: false,
                        imageMask: 'mask-1',
                        imageMaskTranslateY: 0,
                        imageMaskTranslateX: 0,
                        imageMaskScale: 1,
                        imageMaskRotate: 0,
                        useImageMaskAnimation: false,
                        // general styles
                        objectFit: '',
                        aspectRatio: '', 
                        // width: '', 
                        // height: '' 
                        width: 100,
                        height: 'auto',
                    },
                })

                const singleColoredSvg = ref({
                    name: 'singleColoredSvg',
                    type: 'singleColoredSvg',
                    styles: {
                        color: '#f3f6fa',
                        opacity: 1,
                        // positioning 
                        position: 'relative',
                        marginTop: '',
                        marginRight: '',
                        marginBottom: '',
                        marginLeft: '',
                        float: '',
                        // mask effects
                        useImageMask: true,
                        imageMask: 'mask-1',
                        imageMaskTranslateY: 0,
                        imageMaskTranslateX: 0,
                        imageMaskScale: 1,
                        useImageMaskAnimation: false,
                        // general styles
                        objectFit: '',
                        aspectRatio: '', 
                        // width: '', 
                        // height: '' 
                        width: '100%',
                        height: 'auto',
                    },
                })

                // Update image-composition Wrapper Height
                const imageCompositionWrappers = ref([]);

            
                // Funktion zur Rotation eines Punktes
                function rotatePoint(x, y, cx, cy, radians) {
                    const newX = cx + (x - cx) * Math.cos(radians) - (y - cy) * Math.sin(radians);
                    const newY = cy + (x - cx) * Math.sin(radians) + (y - cy) * Math.cos(radians);
                    return { x: newX, y: newY };
                }

                // Funktion zur Berechnung der Extrempunkte eines Pfades nach der Rotation
                function calculateExtremePoints(points, rotationDegrees, cx, cy, scaleX, scaleY) {
                    const radians = rotationDegrees * (Math.PI / 180);

                    // Berechnung der Extrempunkte nach Rotation
                    let minX = Infinity;
                    let maxX = -Infinity;
                    let minY = Infinity;
                    let maxY = -Infinity;

                    points.forEach(point => {
                        const rotatedPoint = rotatePoint(point.x * scaleX, point.y * scaleY, cx, cy, radians);
                        if (rotatedPoint.x < minX) {
                            minX = rotatedPoint.x;
                        }
                        if (rotatedPoint.x > maxX) {
                            maxX = rotatedPoint.x;
                        }
                        if (rotatedPoint.y < minY) {
                            minY = rotatedPoint.y;
                        }
                        if (rotatedPoint.y > maxY) {
                            maxY = rotatedPoint.y;
                        }
                    });

                    return { minY, maxY, height: maxY - minY, width: maxX - minX };
                }
                
                // Funktion zum Aktualisieren der Höhe des Wrappers
                const updateWrapperHeight = (wrapper, delay = 0) => {
                    nextTick(() => {
                        setTimeout(() => {
                            if (!wrapper) return;

                            let highestValue = 0;

                            // chekc images without mask
                            const imageWithoutMask = wrapper.querySelectorAll('.no-mask-image');
                            console.log('imageWithoutMask: ', imageWithoutMask);
                            if(imageWithoutMask.length > 0) {
                                imageWithoutMask.forEach(image => {
                                    const height = image.clientHeight;
                                    console.log('height: ', height);
    
                                    // get margin top from svg in px (value is in percentage)
                                    const marginTop = parseFloat(window.getComputedStyle(image).marginTop)
                                    const topDistance = marginTop ? marginTop : 0 
    
                                    // Höchste Höhe in der aktuellen Multi-Image-Liste ermitteln
                                    if (height + topDistance + 6 > highestValue) {
                                        highestValue = height + topDistance + 6;
                                    }
                                })
                            }

                            // Auswahl aller SVG-Elemente innerhalb der aktuellen Multi-Image-Liste
                            const svgElements = wrapper.querySelectorAll('.image-mask-svg');

                            if(svgElements.length > 0) {
                                svgElements.forEach(svgElement => {
                                    const pathElement = svgElement.querySelector('mask path');
                                    if (!pathElement) return;
    
                                    // Extrahieren der Pfaddaten
                                    const pathLength = pathElement.getTotalLength();
                                    const points = [];
    
                                    // Aufteilen des Pfades in Punkte
                                    for (let i = 0; i <= pathLength; i += pathLength / 100) { // 100 Punkte für Genauigkeit
                                        const point = pathElement.getPointAtLength(i);
                                        points.push({ x: point.x, y: point.y });
                                    }
    
                                    // Winkel der Rotation aus data Attribut
                                    const rotationDegrees = parseInt(svgElement.getAttribute('data-rotation-degrees')) || 0;
    
                                    // Berechnung des Skalierungsfaktors basierend auf der tatsächlichen Größe des SVG-Elements
                                    const svgWidth = svgElement.clientWidth;
                                    const svgHeight = svgElement.clientHeight;
                                    const viewBox = svgElement.viewBox.baseVal;
                                    const scaleX = svgWidth / viewBox.width;
                                    const scaleY = svgHeight / viewBox.height;
    
                                    // Berechnung des Mittelpunkts der Bounding Box
                                    const boundingBox = svgElement.getBBox();
                                    const cx = boundingBox.x + boundingBox.width / 2;
                                    const cy = boundingBox.y + boundingBox.height / 2;
    
                                    // Berechnung der Extrempunkte
                                    const extremes = calculateExtremePoints(points, rotationDegrees, cx, cy, scaleX, scaleY);
                                    const parent = svgElement.parentElement;
                                    parent.style.height = `${extremes.height}px`;
    
                                    // get margin top from svg in px (value is in percentage)
                                    const marginTop = parseFloat(window.getComputedStyle(svgElement).marginTop)
                                    const topDistance = marginTop ? marginTop : 0 
    
                                    // Höchste Höhe in der aktuellen Multi-Image-Liste ermitteln
                                    if (extremes.height + topDistance + 6 > highestValue) {
                                        highestValue = extremes.height + topDistance + 6;
                                    }
                                });
                            }



                            // Setze die Höhe der Multi-Image-Liste basierend auf der höchsten Höhe
                            wrapper.style.height = `${highestValue}px`;
                            wrapper.classList.add('calculated')


                        }, delay); // Eine kleine Verzögerung
                    });
                };

                onMounted(() => {
                    // Selektiere alle image-composition Wrapper
                    imageCompositionWrappers.value = Array.from(document.querySelectorAll('.image-composition'));

                    imageCompositionWrappers.value.forEach(wrapper => {
                        updateWrapperHeight(wrapper, 1000);
                    });

                    // Resize Event Listener hinzufügen
                    const debounceResize = () => {
                        clearTimeout(resizeTimeout);
                        resizeTimeout = setTimeout(() => {
                            imageCompositionWrappers.value.forEach(wrapper => {
                                updateWrapperHeight(wrapper);
                            });
                        }, 150); // Wartezeit, um die Anzahl der Funktionsaufrufe zu verringern
                    };

                    let resizeTimeout;
                    window.addEventListener('resize', debounceResize);
                });

                // Cleanup des Resize-Listeners beim Unmounten
                onUnmounted(() => {
                    window.removeEventListener('resize', debounceResize);
                });

                // Beobachte Änderungen an den Blocks und aktualisiere die Wrapper-Höhe
                watch(blocks, () => {
                    nextTick(() => {
                        imageCompositionWrappers.value = Array.from(document.querySelectorAll('.image-composition'));

                        imageCompositionWrappers.value.forEach(wrapper => {
                            updateWrapperHeight(wrapper);
                        });
                    });
                }, { deep: true });

                const adjustItems = () => {
                    console.log('adjust items called')
                    setTimeout(() => {
                        imageCompositionWrappers.value = Array.from(document.querySelectorAll('.image-composition'));

                        imageCompositionWrappers.value.forEach(wrapper => {
                            console.log('imageCompositionWrappers -> wrapper: ', wrapper)
                            updateWrapperHeight(wrapper);
                        });
                    }, 1000);
                    console.log('ajustItems finished')
                }

                function atLeastOneValidImageCompositionItem(item) {
                    return item.imageComposition.some(obj => 
                        (obj.type === 'singleImage' && obj.mediaId != null) || 
                        obj.type === 'singleImage' && obj.useImageMask === true || 
                        obj.type === 'singleColoredSvg'
                    );
                }

                // Add and remove items from image composition
                const addItemToImageComposition = (item, type) => {
                    if(type === 'singleImage') {
                        // Fallback for old image items
                        if(!item.imageComposition) {
                            item.imageComposition = [];
                        }
                        const newSingleImage = deepCopy(singleMultipleImage.value)
                        item.imageComposition.push({
                            ...newSingleImage,
                            uid: generateUniqueId()
                        });
                    }

                    if(type === 'coloredSvg') {
                        // Fallback for old image items
                        if(!item.imageComposition) {
                            item.imageComposition = [];
                        }
                        const newSingleColoredSvg = deepCopy(singleColoredSvg.value)
                        item.imageComposition.push({
                            ...newSingleColoredSvg,
                            uid: generateUniqueId()
                        });
                    }

                    item.activeImageCompositionSettings = item.imageComposition.length - 1
                }

                const removeItemFromImageComposition = (item, index) => {
                    item.imageComposition.splice(index, 1);

                    item.activeImageCompositionSettings = item.imageComposition.length - 1
                }


                const itemSpacing = {
                    margin: {
                        top: 0,
                        right: 0,
                        bottom: 0,
                        left: 0,
                    },
                    padding: {
                        top: 0,
                        right: 0,
                        bottom: 0,
                        left: 0,
                    },
                }
                // TODO: vorübergehen um itemSpacing hinzuzufügen 
                const checkIfItemHasSpacingSettings = (item) => {
                    if(item.spacingSettings) {
                        return true;
                    } else {
                        item.spacingSettings = deepCopy(itemSpacing);
                        return true;
                    }
                }



                /** Blank CTA */
                const availableCtaDirections = ['row', 'column'];
                const availableCtaAppearances = ['primary', 'secondary'];
                const blankCta = {
                    content: '', 
                    link: '',
                    openInNewTab: false,
                    useModal: false,
                    modalId: '',
                    useArrow: true,
                    appearance: 'primary',
                }

                const addCta = (item) => {
                    item.ctaArray.push(deepCopy(blankCta));
                }

                // Available Text Content (e.g. for text-image-section)
                const availableContentItems = ref([
                    { 
                        type: 'textEditor',
                        name: 'Text Editor',
                        iconSvg: `
                                    <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6.2V5h11v1.2M8 5v14m-3 0h6m2-6.8V11h8v1.2M17 11v8m-1.5 0h3"/>
                                    </svg>
                                `,
                        content: ''
                    },
                    { 
                        type: 'highlighted-text',
                        name: 'Highlighted Text',
                        iconSvg: `
                                    <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                        <path fill-rule="evenodd" d="M18.458 3.11A1 1 0 0 1 19 4v16a1 1 0 0 1-1.581.814L12 16.944V7.056l5.419-3.87a1 1 0 0 1 1.039-.076ZM22 12c0 1.48-.804 2.773-2 3.465v-6.93c1.196.692 2 1.984 2 3.465ZM10 8H4a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h6V8Zm0 9H5v3a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1v-3Z" clip-rule="evenodd"/>
                                    </svg>
                                `,
                        content: '',
                        styleSettings: {
                            fontSize: '4rem',
                            fontWeight: '600',
                            color: '#bf1692',
                            textAlign: 'left',
                            fontFamily: 'Poppins',
                            opacity: 1,
                            useGradient: false,
                            gradient: {
                                gradientColor1: '#bf1692',
                                gradientColor2: '#11114a',
                                gradientType: 'linear',
                                angle: 0,
                            },
                        },
                        itemAnimationSettings: {
                            useAnimation: false,
                            animationType: 'fadeIn',
                            animationDuration: 1000,
                            animationDelay: 0,
                            animationTimeFunction: 'ease',
                            animationFillMode: 'forwards',
                        },
                        textEffectSettings: {
                            useEffect: false,
                            effectType: 'none',
                        },
                        textAnimationSettings: {
                            useAnimation: false,
                            animationType: 'none',
                            animationStartValue: 0,
                            thousandsSeparator: '.',
                            animationDuration: 1000,
                            animationDelay: 0,
                            animationTimeFunction: 'ease',
                            animationFillMode: 'forwards',
                        },
                        spacingSettings: deepCopy(itemSpacing),
                    },
                    { 
                        type: 'image', 
                        name: 'Image',
                        iconSvg: `
                                    <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m3 16 5-7 6 6.5m6.5 2.5L16 13l-4.286 6M14 10h.01M4 19h16a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1Z"/>
                                    </svg>
                                `,
                        mediaId: '', 
                        mediaUrl: '',
                        alt: '',
                        caption: '',
                        styles: {
                            // mask effects
                            useImageMask: false,
                            imageMask: 'mask-1',
                            imageMaskTranslateY: 0,
                            imageMaskTranslateX: 0,
                            imageMaskScale: 1,
                            useImageMaskAnimation: false,
                            // general styles
                            objectFit: '',
                            aspectRatio: '', 
                            width: '', 
                            height: '' 
                        },
                        useImageComposition: false,
                        imageComposition: [JSON.parse(JSON.stringify(singleMultipleImage.value))],
                        activeImageCompositionSettings: 0
                    },
                    { 
                        type: 'tabel', 
                        name: 'Tabelle',
                        iconSvg: `
                                    <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                     <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 9h6m-6 3h6m-6 3h6M6.996 9h.01m-.01 3h.01m-.01 3h.01M4 5h16a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1Z"/>
                                    </svg>
                                `,
                        rows: [
                            {
                                columns: [
                                    { content: '' },
                                    { content: '' }
                                ]
                            },
                        ]
                    },
                    { 
                        type: 'icon-with-text', 
                        name: 'Icon with Text',
                        iconSvg: `
                                    <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                        <path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M9 8h10M9 12h10M9 16h10M4.99 8H5m-.02 4h.01m0 4H5"/>
                                    </svg>
                                `,

                        // if image
                        mediaId: '', 
                        mediaUrl: '',
                        alt: '',
                        // if SVG Code 
                        useSvgCode: false,
                        svgCode: '',
                        width: '', 
                        height: '',
                        // text 
                        content: '',
                    },
                    { 
                        type: 'accordion', 
                        name: 'Accordion',
                        iconSvg: `
                                    <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                        <path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M5 7h14M5 12h14M5 17h14"/>
                                    </svg>
                                `,
                        accordionItems: [ {...blankAccordionItem} ], 
                        accordionStyle: ['minimal', 'boxy'],
                    },
                    { 
                        type: 'CTAs', 
                        name: 'CTAs',
                        direction: 'row',
                        ctaArray: [ deepCopy(blankCta) ],
                    },
                    {
                        type: 'html-form',
                        name: 'HTML Form',
                        iconSvg: `
                                <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                    <path fill-rule="evenodd" d="M8 7V2.221a2 2 0 0 0-.5.365L3.586 6.5a2 2 0 0 0-.365.5H8Zm2 0V2h7a2 2 0 0 1 2 2v.126a5.087 5.087 0 0 0-4.74 1.368v.001l-6.642 6.642a3 3 0 0 0-.82 1.532l-.74 3.692a3 3 0 0 0 3.53 3.53l3.694-.738a3 3 0 0 0 1.532-.82L19 15.149V20a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9h5a2 2 0 0 0 2-2Z" clip-rule="evenodd"/>
                                    <path fill-rule="evenodd" d="M17.447 8.08a1.087 1.087 0 0 1 1.187.238l.002.001a1.088 1.088 0 0 1 0 1.539l-.377.377-1.54-1.542.373-.374.002-.001c.1-.102.22-.182.353-.237Zm-2.143 2.027-4.644 4.644-.385 1.924 1.925-.385 4.644-4.642-1.54-1.54Zm2.56-4.11a3.087 3.087 0 0 0-2.187.909l-6.645 6.645a1 1 0 0 0-.274.51l-.739 3.693a1 1 0 0 0 1.177 1.176l3.693-.738a1 1 0 0 0 .51-.274l6.65-6.646a3.088 3.088 0 0 0-2.185-5.275Z" clip-rule="evenodd"/>
                                </svg>
                                `,
                        htmlFormId: '',
                    },
                    { 
                        type: 'video', 
                        name: 'Video',
                        id: '',  
                    },
                    { 
                        type: 'paragraph', 
                        name: 'Paragraph',
                        content: '' 
                    },
                    { 
                        type: 'headline', 
                        name: 'Headline',
                        content: '' 
                    },
                    { 
                        type: 'list', 
                        name: 'List',
                        listItems: [] 
                    },
                    { 
                        type: 'button', 
                        name: 'Button',
                        content: '', 
                        link: '',
                        useModal: false
                    },
                    
                ])
                const deleteTextContent = (element, index) => {
                    console.log('deleteTextContent: ', element, index);
                    element.textContent.splice(index, 1);
                }
                const deleteListItem = (element, textContentIndex, index) => {
                    console.log('deleteListItem: ', element, index);
                    element.textContent[textContentIndex].listItems.splice(index, 1);
                }
                const changeOrder = (element, textContentIndex, nextOrPrev) => {
                    console.log('changeOrder (element, textContentIndex, nextOrPrev): ', element, textContentIndex, nextOrPrev);

                    // save element wich was clicked in tempElement 
                    if(nextOrPrev === 'next') {
                        const tempElement = element.textContent[textContentIndex];
                        element.textContent[textContentIndex] = element.textContent[textContentIndex + 1];
                        element.textContent[textContentIndex + 1] = tempElement;
                    } else if(nextOrPrev === 'prev') {
                        const tempElement = element.textContent[textContentIndex];
                        element.textContent[textContentIndex] = element.textContent[textContentIndex - 1];
                        element.textContent[textContentIndex - 1] = tempElement;
                    }
                    
                }


                /***
                 * Tabel functions 
                 */
                const addRow = (item) => {
                    const columnCount = item.rows.length > 0 ? item.rows[0].columns.length : 1; // Anzahl der Spalten der ersten Zeile
                    const newRow = {
                        columns: Array.from({ length: columnCount }, () => ({ content: '' }))
                    };
                    item.rows.push(newRow);
                }

                const removeRow = (item, rowIndex) => {
                    if (item.rows.length > 1) {
                        item.rows.splice(rowIndex, 1);
                    } else {
                        console.warn("At least one row must remain.");
                    }
                }

                const addColumn = (item) => {
                    item.rows.forEach(row => {
                        row.columns.push({ content: '' });
                    });
                }

                const removeColumn = (item, colIndex) => {
                    if (item.rows[0].columns.length > 1) {
                            item.rows.forEach(row => {
                            row.columns.splice(colIndex, 1);
                        });
                    } else {
                        console.warn("At least one column must remain.");
                    }
                }


                // Feature Grid Item
                const blankFeatureGridItem = {
                    icon: '',
                    image: {
                        id: '',
                        url: '',
                        alt: '',
                        caption: '',
                        link: '',
                    },
                    isIconImage: true,
                    title: '',
                    content: '',
                    useForm: false,
                    formContent: '',
                    choosenForm: 'No form selected',
                    // create unique ID
                    id: null,
                }
                // add new feature grid item
                const addFeatureGridItem = (element) => {
                    const newFeatureGridItem = deepCopy(blankFeatureGridItem)
                    newFeatureGridItem.id = generateUniqueId();

                    element.gridItems.push(newFeatureGridItem);
                }

                // Image Grid Item
                const blankImageGridItem = {
                    // old and deprecated
                    icon: '',
                    iconUrl: '',
                    // new
                    imageId: '',
                    imageUrl: '',
                    // Video
                    useVideo: false,
                    videoId: '',
                    videoUrl: '',
                    thumbnailId: '',
                    thumbnailUrl: '',
                    // old but still used
                    title: '',
                    content: '',
                    id: 0,
                }
                // add new image grid item
                const addImageGridItem = (element) => {
                    element.gridItems.push({...blankImageGridItem, id: element.gridItems.length + 1 });
                }


                /** Slider */
                const availableSliderAppearances = ref([
                    'default',
                    'team',
                    'logos',
                ])

                // Slider Item
                const blankSliderItem = {
                    image: {
                        id: '',
                        url: '',
                        alt: '',
                    },
                    title: '',
                    name: '',
                    id: 0,
                    useLink: false,
                    link: '',
                    openInNewTab: false,
                }
                // add new slider item
                const addSliderItem = (element) => {
                    const newSliderItem = deepCopy(blankSliderItem)
                    newSliderItem.id = generateUniqueId();

                    element.sliderItems.push(newSliderItem);
                    element.uiValues.secondaryTabNumber = element.sliderItems.length - 1;
                }
                const removeSliderItem = (element, index) => {
                    element.sliderItems.splice(index, 1);
                    element.uiValues.secondaryTabNumber = element.sliderItems.length - 1;
                }


                /**
                 * GOBAL FUNCTIONS 
                 * available for all elements because of its flexible nature
                 */
                const removeItemFromElement = (element, slector, index) => {
                    // check if selector string is separated by dots or not
                    if (slector.includes('.')) {
                        const selectors = slector.split('.');
                        console.log('selectors: ', selectors);

                        // Traverse the element object based on the selectors array
                        let target = element;

                        // Iterate through the selectors, except the last one
                        for (let i = 0; i < selectors.length - 1; i++) {
                            target = target[selectors[i]];  // Traverse down the object
                        }

                        // Now `target` refers to the object that contains the array we want to modify
                        const finalSelector = selectors[selectors.length - 1];
                        if (Array.isArray(target[finalSelector])) {
                            target[finalSelector].splice(index, 1);
                            console.log('element successfully removed');
                        } else {
                            console.error(`The property '${finalSelector}' is not an array.`);
                        }
                    } else {
                        // Handle the case where there are no dots in the selector
                        if (Array.isArray(element[slector])) {
                            element[slector].splice(index, 1);
                            console.log('element successfully removed');
                        } else {
                            console.error(`The property '${slector}' is not an array.`);
                        }
                    }
                }

                // Sticky (Text-Image-)Section
                const blankStickySection = {
                    content: {
                        heading: '',
                        paragraph: '',
                    },
                    imageData: {
                        id: '',
                        url: '',
                        alt: '',
                        caption: '',
                        link: '',
                    },
                    // svgCode: '',
                    id: 0,
                }
                // add new feature grid item
                const addStickySection = (element) => {
                    const newStickySection = deepCopy(blankStickySection)
                    newStickySection.id = generateUniqueId();

                    element.sections.push(newStickySection);
                }
                const removeStickySection = (element, index) => {
                    element.sections.splice(index, 1);
                }

                /**
                 * Image Grid
                 */
                const deleteImageGirdItem = (element, index) => {
                    element.gridItems.splice(index, 1);
                }

                /**
                 * ctas array
                 */
                const cta = ref([
                    { text: '', link: '', useModal: false, id: 0 },
                ])
                // add new cta to ctas array
                const addCtaToCtasArray = (element) => {
                    element.ctas.push({...cta, id: element.ctas.length + 1 });
                }

                const availableAppearances = [
                    'default',
                    'light-gray',
                    'purple-gradient',
                    'purple-gradient-fullwidth',
                ]
                const advancedSettingsOptions= ref({
                    spacing: {
                        top: ['default', 'none', 's', 'm', 'l', 'custom'],
                        topCustom: '',
                        right: ['default', 'none', 's', 'm', 'l', 'custom'],
                        rightCustom: '',
                        bottom: ['default', 'none', 's', 'm', 'l', 'custom'],
                        bottomCustom: '',
                        left: ['default','none', 's', 'm', 'l', 'custom'],
                        leftCustom: '',
                    },
                    // background: {
                    //     background: '',
                    //     useGradiend: false,
                    //     gradiend: '',
                    //     backgroundUrl: '',
                    //     backgroundPosition: '',
                    //     backgroundRepeat: '',
                    //     backgroundSize: '',
                    // },
                    // border: {
                    //     useBorder: false,   
                    //     borderWidth: '1px',
                    //     borderStyle: 'solid',
                    //     borderColor: 'black',
                    //     borderRadius: '0px',
                    // },
                    // // Special Classes for posts (beiträge)
                    appearance: availableAppearances,
                    reverseColsOnMobile: [false, true]
                })
                
                const advancedSettings = ref({
                    spacing: {
                        top: 'default',
                        topCustom: 0,
                        right: 'default',
                        rightCustom: 0,
                        bottom: 'default',
                        bottomCustom: 0,
                        left: 'default',
                        leftCustom: 0,
                    },
                    background: {
                        background: '',
                        useGradiend: false,
                        gradiend: '',
                        backgroundUrl: '',
                        backgroundPosition: '',
                        backgroundRepeat: '',
                        backgroundSize: '',
                    },
                    border: {
                        useBorder: false,   
                        borderWidth: '1px',
                        borderStyle: 'solid',
                        borderColor: 'black',
                        borderRadius: '0px',
                    },
                    // Special Classes for posts (beiträge)
                    appearance: 'default',
                    // Mobile Adjustments 
                    reverseColsOnMobile: false,
                })

                const uiValues = ref({
                    // activeTab: 'content',
                    // activeTabNumber: 1,
                    // isColSelectOpen: false, // only for flexible-content
                    // secondaryTabNumber: 0,

                    activeTab: 'content',
                    activeTabNumber: 1,
                    minified: false,
                    secondaryTabNumber: 0,
                    isAddNewItemLightboxOpen: false,
                })

                const addNumberToggle = (element) => {
                    element.useNumbersAboveIcon = false
                }

                

                // Master 
                const blankMasterPricingColumn = {
                    rows: [],
                }
                const blankMasterPricingRow = {
                    name: ''
                }

                // single offer col
                const pricingColumn = {
                    title: '',
                    description: '',
                    rows: [],
                    ctaRow: {
                        price: '',
                        buttonText: '',
                        buttonLink: '',
                        useModal: true,
                        rows: [],
                    }
                }
                const generateBlankPricingColumn = () => {
                    const blankPricingColumn = JSON.parse(JSON.stringify(pricingColumn))
                    const newblankPricingColumn = {
                        ...blankPricingColumn,
                        id: generateUniqueId()
                    }

                    return newblankPricingColumn
                }
                const blankSingleOfferCol = {
                    check: true
                }

                const addPricingTableRow = (element) => {
                    console.log('%c addPricingTableRow called', 'color: #007acc; font-weight: bold;');
                    element.masterPricingColumn.rows.push({...blankMasterPricingRow, id: generateUniqueId()});
                    element.pricingColumns.forEach((pricingCol, index)=> {
                        // console.log('pricingCol, index: ', pricingCol, index);
                        // console.log('{...blankSingleOfferCol}: ', {...blankSingleOfferCol});
                        pricingCol.rows.push({check: true, id: generateUniqueId(),})
                    })
                }

                const deletePricingTableRow = (element, index) => {
                    element.masterPricingColumn.rows.splice(index, 1);
                    element.pricingColumns.forEach((pricingCol, pricingColIndex)=> {
                        pricingCol.rows.splice(index, 1);
                    })
                }

                const addPricingTableOfferCol = (element) => {
                    console.log('addPricingTableOfferCol called')
                }

                const pricingTableCtaRowRows = {
                    leftContent: '',
                    rightContent: '',
                }
                const returnPricingTableCtaRow = (element) => {
                    return {...pricingTableCtaRowRows, id: generateUniqueId()}
                }
                const addPricingTableCtaRowInnerRow = (ctaRow) => {
                    ctaRow.rows.push(returnPricingTableCtaRow())
                }

                // deletePricingTableCtaRowInnerRow
                const deletePricingTableCtaRowInnerRow = (ctaRow, index) => {
                    ctaRow.rows.splice(index, 1)
                }


                // Diese Funktion synchronisiert die Anzahl der Preisspalten mit dem ausgewählten Wert
                const handlePricingColCountChange = (element, newValue) => {
                    // Nummer als Integer statt String sicherstellen
                    const newCountInt = parseInt(newValue, 10);
                    
                    // Setze den Wert direkt als Zahl zurück
                    element.pricingColCount = newCountInt;
                    
                    // Die aktuelle Anzahl der Spalten
                    const currentCount = element.pricingColumns ? element.pricingColumns.length : 0;
                    
                    // Passe die Anzahl der Preisspalten an
                    if (newCountInt > currentCount) {
                        // Neue Spalten hinzufügen
                        for (let i = currentCount; i < newCountInt; i++) {
                            // Neue Spalte mit deiner vorhandenen Funktion erstellen
                            const newColumn = generateBlankPricingColumn();
                            
                            // Wenn es bereits Master-Zeilen gibt, füge entsprechende Zeilen in der neuen Spalte hinzu
                            if (element.masterPricingColumn && element.masterPricingColumn.rows) {
                                newColumn.rows = element.masterPricingColumn.rows.map(() => ({
                                    check: true,
                                    id: generateUniqueId()
                                }));
                            }
                            
                            // Spalte zum Array hinzufügen
                            element.pricingColumns.push(newColumn);
                        }
                    } else if (newCountInt < currentCount) {
                        // Überschüssige Spalten entfernen
                        element.pricingColumns = element.pricingColumns.slice(0, newCountInt);
                    }
                };

                // Füge diese Funktion hinzu, um sicherzustellen, dass alle Preistabellen korrekt initialisiert sind
                const initPricingTable = (element) => {
                    console.log('%c Initialisiere Preistabelle', 'color: #007acc; font-weight: bold;');
                    
                    // Stelle sicher, dass pricingColCount eine Zahl ist
                    element.pricingColCount = parseInt(element.pricingColCount, 10) || 3; // Standardwert 3, falls nicht gesetzt
                    
                    // Stelle sicher, dass pricingColumns ein Array ist
                    if (!element.pricingColumns) {
                        element.pricingColumns = [];
                    }
                    
                    // Stelle sicher, dass masterPricingColumn existiert
                    if (!element.masterPricingColumn) {
                        element.masterPricingColumn = JSON.parse(JSON.stringify(blankMasterPricingColumn));
                    }
                    
                    // Synchronisiere die Anzahl der Spalten
                    handlePricingColCountChange(element, element.pricingColCount);
                };

                // Überwache Änderungen an blocks.value
                watch(() => blocks.value, () => {
                    nextTick(() => {
                        // Initialisiere alle Preistabellen
                        blocks.value.forEach(block => {
                            if (block.type === 'pricing-table') {
                                initPricingTable(block);
                            }
                        });
                    });
                }, { deep: true, immediate: true });

                // Füge auch diesen Event-Handler für direkte Änderungen am UI hinzu
                const handlePricingColCountChangeOnUI = (element, event) => {
                    const newValue = event.target.value;
                    handlePricingColCountChange(element, newValue);
                };

                // Aktualisiere das HTML-Element für das Dropdown:
                // <select v-model.number="element.pricingColCount" @change="handlePricingColCountChangeOnUI(element, $event)">
                // ...
                // </select>


                /** End Pricing Table */
                 
                const bubbleVideoSettings = {
                    videoId: '',
                    videoUrl: '',
                    type: 'video/mp4',
                    posterId: '',
                    posterUrl: '',
                    controls: true,
                    autoplay: false,
                    loop: false
                }
                // TODO: Delete this later 
                const addVideoLightboxToHero = (element) => {
                    console.log('element: ', element);
                    element.bubbleBackgrounds.rightBubbles.first.videoSettings = bubbleVideoSettings
                }


                // Fetch categories from backend
                const successStoryCategories = ref([]);
                // onMounted(() => {
                //     fetch('/wp-json/wp/v2/erfolgsgeschichten-kategorien')
                //         .then(response => response.json())
                //         .then(data => {
                //             successStoryCategories.value = data;
                //         })
                //         .catch(error => console.error('Error fetching successStoryCategories:', error));
                // });



                /**
                 * Testimonial Functions
                 */
                const maskStyles = [
                    'mask-1',
                    'mask-2',
                    'mask-3',
                ]
                const addTestimonialItem = (element) => {
                    console.log('addTestimonialItem called')
                    element.testimonials.push({
                        // Text 
                        name: '',
                        position: '',
                        company: '',
                        description: '',
                        content: '',
                        // Image
                        image: {
                            id: '',
                            url: '',
                            alt: '',
                            styles: {
                                rotateMask: '',
                                objectFit: '',
                                y: '', 
                                x: '',
                                imageScale: 1,
                                imageTranslateY: 0,
                                imageTranslateX: 0,
                                imageMask: 'mask-1',
                                backgroundColor: '#fff',
                            },
                        }
                    })

                    element.uiValues.secondaryTabNumber = element.testimonials.length - 1
                }
                const deleteTestimonial = (element, index) => {
                    console.log('deleteTestimonial called: splice index: ', index)
                    element.testimonials.splice(index, 1)

                    element.uiValues.secondaryTabNumber = element.testimonials.length - 1
                }


                const availableBlocks = ref([
                    { 
                        // Content 
                        type: 'hero', 
                        name: 'Hero',
                        componentPreviewImage: 'hero.png',
                        tagline: '', 
                        hedline: '', 
                        // just for typing animation 
                        typingAnimationContent: '',
                        headlineAfterTyping: '',
                        content: '', 
                        ctaText: '', 
                        ctaLink: '',
                        // Apperance
                        useImageVideoBackground: false,
                        backgroundType: 'image', // 'image' or 'video'
                        background: {
                            url: '',
                            id: '',
                        },
                        bubbleBackgrounds: {
                            leftBubble: {
                                first: {
                                    url: '',
                                    id: '',
                                    styles: {
                                        // mask effects
                                        imageMask: 'mask-1',
                                        imageMaskTranslateY: 0,
                                        imageMaskTranslateX: 0,
                                        imageMaskScale: 1,
                                        // general styles
                                        objectFit: '',
                                        aspectRatio: '', 
                                        width: '', 
                                        height: '' 
                                    },
                                },
                            },
                            rightBubbles: {
                                first: {
                                    url: '',
                                    id: '',
                                    isVideoLightbox: false,
                                    videoSettings: {
                                        id: '',
                                        url: '',
                                        type: 'video/mp4',
                                        poster: {
                                            id: '',
                                            url: '',
                                        },
                                        controls: true,
                                        autoplay: false,
                                        loop: false
                                    },
                                    styles: {
                                        // mask effects
                                        imageMask: 'mask-1',
                                        imageMaskTranslateY: 0,
                                        imageMaskTranslateX: 0,
                                        imageMaskScale: 1,
                                        // general styles
                                        objectFit: '',
                                        aspectRatio: '', 
                                        width: '', 
                                        height: '' 
                                    },
                                },
                                second: {
                                    url: '',
                                    id: '',
                                    styles: {
                                        // mask effects
                                        imageMask: 'mask-1',
                                        imageMaskTranslateY: 0,
                                        imageMaskTranslateX: 0,
                                        imageMaskScale: 1,
                                        // general styles
                                        objectFit: '',
                                        aspectRatio: '', 
                                        width: '', 
                                        height: '' 
                                    },
                                },
                                third: {
                                    url: '',
                                    id: '',
                                    styles: {
                                        // mask effects
                                        imageMask: 'mask-1',
                                        imageMaskTranslateY: 0,
                                        imageMaskTranslateX: 0,
                                        imageMaskScale: 1,
                                        // general styles
                                        objectFit: '',
                                        aspectRatio: '', 
                                        width: '', 
                                        height: '' 
                                    },
                                },
                            },
                        },
                        ctaUseModal: false,
                        isCentered: false, 
                        // Advanced Setting (primary spacing)
                        advancedSettings: advancedSettings,
                        uiValues: uiValues 
                    },
                    { 
                        type: 'text-image', 
                        name: 'Text Image',
                        tagline: '', 
                        headline: '', 
                        textContent: [], 
                        image: '', 
                        reverseOrder: false, 
                        advancedSettings: advancedSettings, 
                        uiValues: uiValues
                    },
                    { 
                        type: 'flexible-content', 
                        name: 'Flexible Content',
                        componentPreviewImage: 'flexible-content.png',
                        tagline: '', 
                        headline: '', 
                        paragraph: '',
                        verticalAlignment: 'normal',
                        equalHeightColumns: true,
                        reverseColumnsOnMobile: false,
                        colSettings: [ deepCopy(singleColSettings.value) ],
                        flexibleContent: [[]], 
                        advancedSettings: advancedSettings, 
                        uiValues: uiValues
                    },
                    {
                        type: 'testimonial-section',
                        name: 'Testimonial Section',
                        componentPreviewImage: 'testimonial-section.png',
                        tagline: '',
                        headline: '',
                        textContent: [],
                        ctas: [],
                        testimonials: [],
                        advancedSettings: advancedSettings,
                        uiValues: uiValues,
                    },
                    // {
                    //     type: 'success-story-teaser',
                    //     name: 'Success Story Teaser',
                    //     tagline: '',
                    //     headline: '',
                    //     textContent: [],
                    //     postCount: 4,
                    //     postOffset: 0,
                    //     category: '',
                    //     postAppearance: 'standard',
                    //     reverseOrder: false,
                    //     advancedSettings: advancedSettings,
                    //     uiValues: uiValues,
                    // },
                    { 
                        type: 'post-list', 
                        name: 'Post List',
                        tagline: '', 
                        headline: '', 
                        textContent: [],
                        postCount: 4,
                        postOffset: 0,
                        postAppearance: 'standard',
                        reverseOrder: false, 
                        advancedSettings: advancedSettings, 
                        uiValues: uiValues,
                    },
                    { 
                        type: 'featureGrid', 
                        name: 'Feature Grid',
                        heading: '', 
                        gridItems: [ blankFeatureGridItem ], 
                        gridTemplate: '4', 
                        centerGirdItems: false,
                        centerContent: false,
                        useNumbersAboveIcon: false, 
                        disableModule: false,
                        advancedSettings: advancedSettings, 
                        uiValues: uiValues 
                    },
                    { 
                        type: 'imageGrid', 
                        name: 'Image Grid',
                        heading: '', 
                        paragraph: '', 
                        gridItems: [ blankImageGridItem ], 
                        gridTemplate: '3', 
                        advancedSettings: advancedSettings, 
                        uiValues: uiValues 
                    },
                    { 
                        type: 'sticky-text-image', 
                        name: 'Sticky Text Image',
                        sections: [ blankStickySection ], 
                        advancedSettings: advancedSettings, 
                        uiValues: uiValues 
                    },
                    { 
                        type: 'cta-section', 
                        name: 'CTA Section',
                        tagline: '', 
                        headline: '', 
                        content: '', 
                        // image 
                        useImage: false,
                        image: '', 
                        // use backgroundImage 
                        useBackgroundImage: false,
                        backgroundImageId: '',
                        backgroundImageUrl: '',
                        backgroundImage: '',
                        // Quote  
                        useQuote: false,
                        quote: '',
                        // Ctas
                        ctas: [ cta ], 
                        // Standart Stuff 
                        advancedSettings: advancedSettings, 
                        uiValues: uiValues 
                    },
                    // { 
                    //     // Already optimized for building blocks in row
                    //     type: 'accordion', 
                    //     name: 'Accordion',
                    //     accordionItems: [ blankAccordionItem ], 
                    //     accordionStyle: ['minimal', 'boxy'],
                    //     advancedSettings: advancedSettings,
                    //     uiValues: uiValues,
                    // },
                    { 
                        type: 'slider-section',
                        name: 'Slider Section',
                        tagline: '', 
                        headline: '', 
                        content: '', 
                        // styles 
                        sliderStyle: 'default',
                        preserveAspectRatio: true,
                        imageAspectRatioWidth: 1,
                        imageAspectRatioheight: 1,
                        imageObjectFit: 'cover',
                        cta: {
                            useCta: false,
                            ctaText: '',
                            ctaLink: '',
                            openLinkInNewTab: false,
                            ctaStyle: 'primary',
                            useModal: false,
                            modalId: '',
                        },
                        sliderItems: [ blankSliderItem ], 
                        advancedSettings: advancedSettings, 
                        uiValues: uiValues 
                    },
                    { 
                        type: 'pricing-table', 
                        name: 'Pricing Table',
                        tagline: '', 
                        headline: '', 
                        content: '', 
                        pricingColCount: 2,
                        masterPricingColumn: { ...blankMasterPricingColumn },  // Sicherstellen, dass auch dies eine Kopie ist
                        pricingColumns: [ generateBlankPricingColumn(), generateBlankPricingColumn() ],  // Kopien erstellen
                        advancedSettings: { ...advancedSettings },  // Falls nötig, auch hier eine Kopie
                        uiValues: uiValues
                    },
                    { 
                        type: 'agenda-overview', 
                        name: 'Agenda Overview',
                        tagline: '', 
                        headline: '', 
                        content: '', 
                        ctas: [ cta ],
                        // Seletcted date 
                        selectedDate: '', 
                        // Advanced Setting (primary spacing)
                        advancedSettings: advancedSettings,
                        uiValues: uiValues 
                    },
                    { 
                        type: 'modal', 
                        name: 'Modal',
                        content: '', 
                        teaser: '', 
                        buttonText: '', 
                        advancedSettings: advancedSettings,
                        uiValues: uiValues 
                    },
                    { 
                        type: 'raw-html', 
                        name: 'Raw HTML',
                        html: '', 
                        advancedSettings: advancedSettings,
                        uiValues: uiValues
                    },

                    /** Only available in posts */
                    {
                        type: 'conclusion',
                        name: 'Text with Background (z.B. "Fazit", "Achtung" etc.)',
                        headline: '',
                        content: '',
                        advancedSettings: advancedSettings,
                        uiValues: uiValues
                    },

                    // Werden einzelne fields – auch und vor allem in dem theme 
                    { 
                        type: 'headline', 
                        content: '',
                        advancedSettings: advancedSettings, 
                        uiValues: uiValues 
                    },
                    { 
                        type: 'paragraph', 
                        content: '',
                        advancedSettings: advancedSettings, 
                        uiValues: uiValues 
                    },

                    // Flexible building blocks
                    {
                        type: 'row',
                        gridTemplate: '1',
                        cols: [
                            { content: [] },
                        ],
                        advancedSettings: advancedSettings, 
                        uiValues: uiValues 
                    },
                    
                ]);

                // const availableBlockTypesInPage = ref(['hero', 'flexible-content', 'testimonial-section', 'success-story-teaser', 'post-list', 'cta-section', 'featureGrid', 'imageGrid', 'sticky-text-image', 'slider-section', 'pricing-table', 'agenda-overview', 'modal', 'raw-html']);
                const availableBlockTypesInPage = ref(['hero', 'flexible-content', 'slider-section', 'testimonial-section', 'pricing-table', 'featureGrid', 'imageGrid', 'sticky-text-image', 'agenda-overview', 'raw-html']);
                // const availableBlockTypesInPost = ref(['row', 'headline', 'paragraph', 'text-image', 'accordion', 'modal', 'raw-html']);
                const availableBlockTypesInPost = ref(['flexible-content', 'raw-html']); // 'modal', 

                const availableBlockTypesInErfolgsgeschichten = ref(['flexible-content', 'featureGrid', 'raw-html']); // 'modal', 

                 // PHP-Ausgabe des aktuellen Post-Typs
                const postType = '<?php echo get_post_type($post->ID); ?>';
                console.log('%c postType', 'font-size: 24px; color: #0ea;', postType);

                // Wähle die verfügbaren Blöcke je nach Post-Typ aus
                const listOfAvailableBlocks = ref(availableBlockTypesInPost.value);
                const setAvailableBlocksForPostType = () => {
                    // console.log('computed setAvailableBlocksForPostType: ', postType);
                    // console.log('listOfAvailableBlocks.value: ', listOfAvailableBlocks.value)
                    
                    if (postType === 'page') {
                        listOfAvailableBlocks.value = availableBlockTypesInPage.value;
                    } 

                    if (postType === 'post') {
                        listOfAvailableBlocks.value = availableBlockTypesInPost.value;
                    }

                    if (postType === 'erfolgsgeschichten') {
                        console.log('erfolgsgeschichten')
                        listOfAvailableBlocks.value = availableBlockTypesInErfolgsgeschichten.value;
                        console.log('availableBlockTypesInErfolgsgeschichten.value: ', availableBlockTypesInErfolgsgeschichten.value)
                    }

                    console.log('listOfAvailableBlocks.value: ', listOfAvailableBlocks.value);
                }
                setAvailableBlocksForPostType();



                const availableBlocksForType = computed(() => {
                    return availableBlocks.value.filter(block => listOfAvailableBlocks.value.includes(block.type));
                    console.log('computed availableBlocksForType')
                })

                console.log('%c availableBlocksForType', 'font-size: 24px; color: #0ea;', availableBlocksForType.value);

                const addAdvancedSettings = (element) => {
                    element.advancedSettings = advancedSettings;
                }

                const blocksWithoutUiValues = computed(() => {
                    return blocks.value.map(block => {
                        const { uiValues, ...rest } = block;
                        return rest;
                    });
                });

                const modalToAddNewBlocksOpen = ref(false);

                const removeBlock = (id) => {
                    // blocks.value = blocks.value.filter(block => block.id !== id);
                    const blockIndex = blocks.value.findIndex(block => block.id === id);
                    if (blockIndex !== -1) {
                        const block = blocks.value[blockIndex];
                        const editor = tinymce.get(`tinymce-${block.id}`);
                        if (editor) {
                            editor.remove();
                        }
                        blocks.value.splice(blockIndex, 1);
                    }
                };


                /** Toggle between tabs (content, apperance and spacing) */
                const updateActiveTab = (element, tab) => {
                    if(typeof tab === 'string') {
                        console.log('updateActiveTab', element, tab);
                        element.uiValues.activeTab = tab;
                    }

                    if(typeof tab === 'number') {
                        console.log('tab is a number', tab);
                        element.uiValues.activeTabNumber = tab;
                    }

                    setTimeout(() => {
                        initTinyMCE();
                    }, 550);
                    // initTinyMCE();
                };

                

                const addNewBlock = (availableModule) => {
                    // Erstelle eine tiefe Kopie des availableModule
                    const newBlock = JSON.parse(JSON.stringify(availableModule));
                    newBlock.id = generateUniqueId();
                    blocks.value.push(newBlock);
                    initTinyMCE(); // TinyMCE für den neuen Block initialisieren

                    isAddNewElementLightboxOpen.value = false;
                };
                
                const addNewBlockToRow = (element, index, availableModule) => {
                    // filter out the block with the same type
                    // const filteredBlocks = blocks.value.filter(block => block.type!== type);

                    // add the new block to the array
                    element.cols[index].content.push({
                        ...availableModule,
                        id: generateUniqueId(),
                    });
                };

                // Add block to Text-Image Component 
                const addFieldToBlock = (block, fieldType) => {
                    console.log('block', block);
                    console.log('fieldType', fieldType);
                    console.log('block.textContent: ', block.textContent)

                    // fint the field from availableBlocks
                    const field = availableContentItems.value.find(field => field.type === fieldType);
                    console.log('field to push: ', field);
 
                    block.textContent.push(field);
                }

                // Add List Item in Text-Image Component
                const addListItem = (listItems) => {
                    console.log('addListItem called');
                    console.log('listItems: ', listItems);
                    // console.log('element.textContent: ', element.textContent);
                    // console.log('element.textContent.listItems: ', element.textContent.listItems);

                    listItems.push({
                        content: '',
                        order: listItems.length + 1,
                    });
                }

                /**
                 * Flexible Content Functions
                 */
                // Adjust Cols in flexible content block
                const adjustCols = (block, index, divisionArray) => {
                    console.log('adjustCols called (flexible content)');
                    // block is the reference to the block 
                    console.log('block: ', block);
                    // index is the new length
                    console.log('index: ', index);
                    // divisionArray
                    console.log('divisionArray: ', divisionArray);

                    /*
                    if(!divisionArray) {
                        console.log('there is no divisionArray');
                        if(block.flexibleContent.length > index) {
                            // collabse content in the maximum number of cols
                            for (let i = block.flexibleContent.length - 1; i >= index; i--) {
                                console.log('i = ', i);
    
                                // adjust content Items
                                const contentItems = block.flexibleContent[i];
                                console.log('contentItems: ', contentItems);
                                block.flexibleContent.splice(i, 1);
                                contentItems.forEach(contentItem => {
                                    console.log('contentItem: ', contentItem);
                                    console.log('index - 1: ', index - 1);
                                    console.log('block.flexibleContent[index - 1]: ', block.flexibleContent[index - 1]);
                                    block.flexibleContent[index - 1].push(contentItem);
                                })
    
                                // adjust colSettings
                                block.colSettings.splice(i, 1);
                            }
                        }
    
                        if(block.flexibleContent.length < index) {
                            // expand flexibleContent in the maximum number of cols
                            for (let i = block.flexibleContent.length; i < index; i++) {
                                console.log('i = ', i);
                                block.flexibleContent.push([]);
                            }
    
                            // adjust colSettings 
                            for (let i = block.colSettings.length; i < index; i++) {
                                console.log('i = ', i);
                                block.colSettings.push(deepCopy(singleColSettings.value));
                            }
                        }
                    }
                    */

                    /*** WIDTH DEVISION ARRAY ***/
                    if(divisionArray) {
                        console.log('there is a divisionArray');

                        /**
                         * Adjust existing col(s)
                         */
                        for (let i = 0; i < block.flexibleContent.length; i++) {
                            console.log('')
                            console.log('i from Adjust existing col(s) = ', i);
                            block.colSettings[i].width = divisionArray[i];
                        }

                        /**
                         * delete cols
                         * Wenn Cols weniger werden als vorhandene cols
                         */
                        if(block.flexibleContent.length > index) {
                            for (let i = block.flexibleContent.length - 1; i >= index; i--) {
                                console.log('i = ', i);
    
                                // adjust content Items
                                const contentItems = block.flexibleContent[i];
                                console.log('contentItems: ', contentItems);
                                block.flexibleContent.splice(i, 1);
                                contentItems.forEach(contentItem => {
                                    console.log('contentItem: ', contentItem);
                                    console.log('index - 1: ', index - 1);
                                    console.log('block.flexibleContent[index - 1]: ', block.flexibleContent[index - 1]);
                                    block.flexibleContent[index - 1].push(contentItem);
                                })
    
                                // adjust colSettings
                                block.colSettings.splice(i, 1);
                            }
                        }

                        /**
                         * Create new col(s)
                         * Wenn Cols mehr werden als vorhandene cols 
                         */
                        if(block.flexibleContent.length < index) {
                            // expand flexibleContent in the maximum number of cols
                            for (let i = block.flexibleContent.length; i < index; i++) {
                                console.log('i = ', i);
                                block.flexibleContent.push([]);
                            }
    
                            // adjust colSettings 
                            for (let i = block.colSettings.length; i < index; i++) {
                                console.log('i = ', i);
                                const adjustedColSettings = deepCopy(singleColSettings.value);
                                adjustedColSettings.width = divisionArray[i];
                                block.colSettings.push(adjustedColSettings);
                            }
                        }
                    }
                }


                // Add content to flexible content block
                const addContentItemToBlock = (block, index, itemType) => {
                    console.log('addContentItemToBlock called (flexible content)');
                    console.log('block: ', block);
                    console.log('index: ', index);
                    console.log('itemType: ', itemType);

                    // fint the field from availableBlocks
                    const field = availableContentItems.value.find(field => field.type === itemType);
                    console.log('field to push: ', field);

                    const newField = Object.assign({}, field);
                    // push the content to the block
                    block.flexibleContent[index].push(newField);

                    block.uiValues.isAddNewItemLightboxOpen = false;

                    const itemIndex = block.flexibleContent[index].length - 1;

                    // block.uiValues.isAddNewItemLightboxOpen = false;
                    console.log(`lightBoxValue = edit-content-item-${block.id}-${index}-${itemIndex}`);
                    block.uiValues.isAddNewItemLightboxOpen = `edit-content-item-${block.id}-${index}-${itemIndex}`;

                    initTinyMCE();

                }

                const deleteContentItemFromBlock = (block, index, contentIndex) => {
                    console.log('deleteContentItemFromBlock called (flexible content)');
                    console.log('block: ', block);
                    console.log('index: ', index);
                    console.log('contentIndex: ', contentIndex);

                    block.flexibleContent[index].splice(contentIndex, 1);
                }

                const attachMediaAsstes = (item, colIndex, contentItemIndex) => {
                    console.log('attachMediaAsstes called (flexible content)');
                    console.log('item: ', item);
                    console.log('colIndex: ', colIndex);
                    console.log('contentItemIndex: ', contentItemIndex);

                    let frame = wp.media({
                        title: 'Bild auswählen',
                        button: {
                            text: 'Bild verwenden'
                        },
                        multiple: false
                    });

                    frame.on('select', () => {
                        const attachment = frame.state().get('selection').first().toJSON();
                        console.log('attachment: ', attachment);
                        item.mediaUrl = attachment.url;
                        item.mediaId = attachment.id;
                    });

                    frame.open();
                }
                
                
                // const openMediaLibrary = (block, assetKey) => {
                //     console.log('openMediaLibrary called');

                //     let frame = wp.media({
                //         title: 'Bild auswählen',
                //         button: {
                //             text: 'Bild verwenden'
                //         },
                //         multiple: false
                //     });

                //     frame.on('select', () => {
                //         const attachment = frame.state().get('selection').first().toJSON();
                //         console.log('!index block[assetKey]:', block[assetKey]);
                //         // TODO: alles auf image statt url umstellen 
                //         block[assetKey] = attachment.url;
                //     });

                //     frame.open();
                // };
                const openMediaLibrary = (block, assetKeyPath, isArray = false) => {
                    console.log('openMediaLibrary called');

                    let frame = wp.media({
                        title: 'Bild auswählen',
                        button: {
                            text: 'Bild verwenden'
                        },
                        multiple: false
                    });

                    frame.on('select', () => {
                        const attachment = frame.state().get('selection').first().toJSON();
                        console.log('Selected attachment:', attachment.url);

                        const keys = assetKeyPath.split('.');
                        let target = block;

                        for (let i = 0; i < keys.length - 1; i++) {
                            target = target[keys[i]];
                        }

                        const finalKey = keys[keys.length - 1];

                        // if multiple Images
                        if (isArray) {
                            if (!Array.isArray(target[finalKey])) {
                                target[finalKey] = [];
                            }
                            target[finalKey].push(attachment.url);
                        // if single Image
                        } else {
                            target[finalKey] = attachment.url;
                        }
                    });

                    frame.open();
                };

                const removeImageFromElement = (block, assetKeyPath, isArray = false, index = null) => {
                    console.log('removeImageFromElement called');

                    const keys = assetKeyPath.split('.');
                    let target = block;

                    for (let i = 0; i < keys.length - 1; i++) {
                        target = target[keys[i]];
                    }

                    const finalKey = keys[keys.length - 1];

                    if (isArray && index !== null) {
                        if (Array.isArray(target[finalKey])) {
                            target[finalKey].splice(index, 1);
                        }
                    } else {
                        target[finalKey] = null;
                    }

                    console.log('Image removed:', assetKeyPath);
                };


                // Open New Media Library
                const openMediaLibraryAndSaveUrlAndImage = (targetObject, type = 'image', idKey = 'id', urlKey = 'url', allowMultiple = false) => {
                    console.log('openMediaLibraryAndSaveUrlAndImage called');
                    console.log('targetObject:', targetObject);

                    let mediaTitle = '';
                    if (type === 'image') {
                        mediaTitle = 'Bild';
                    } else if (type === 'video') {
                        mediaTitle = 'Video';
                    } else if (type === 'both') {
                        mediaTitle = 'Bild oder Video';
                    }

                    // Open Media Library
                    let frame = wp.media({
                        title: `${mediaTitle} auswählen`,
                        button: {
                            text: `${mediaTitle} verwenden`,
                        },
                        multiple: allowMultiple,
                        library: {
                            type: type === 'both' ? ['image', 'video'] : type, // Filter type based on `type`
                        }
                    });

                    frame.on('select', () => {
                        const attachments = frame.state().get('selection').toArray().map(attachment => attachment.toJSON());

                        attachments.forEach(attachment => {
                            console.log('Selected attachment:', attachment.url);

                            // Extract relative URL
                            const homeUrl = wpApiSettings.root.replace('/wp-json/', ''); // Get home URL from WP API settings
                            console.log('homeUrl:', homeUrl);
                            const relativeUrl = attachment.url.replace(homeUrl, ''); // Remove the home URL from the attachment URL

                            if (allowMultiple) {
                                if (!Array.isArray(targetObject)) {
                                    throw new Error("Target object must be an array when allowMultiple is true.");
                                }
                                targetObject.push({
                                    [urlKey]: relativeUrl,
                                    [idKey]: attachment.id,
                                });

                                console.log('targetObject:', targetObject);
                            } else {
                                targetObject[urlKey] = relativeUrl;
                                targetObject[idKey] = attachment.id;

                                console.log('targetObject:', targetObject);
                            }
                        });
                    });

                    frame.open();
                };


                const removeImageUrlAndIdFromElement = (targetObject, idKey = 'id', urlKey = 'url', allowMultiple = false) => {
                    console.log('removeImageUrlAndIdFromElement called');
                    console.log('targetObject before removal:', targetObject);

                    if (allowMultiple) {
                        if (!Array.isArray(targetObject)) {
                            throw new Error("Target object must be an array when allowMultiple is true.");
                        }

                        // Set each object's `idKey` and `urlKey` to null in the array
                        targetObject.forEach((item, index) => {
                            if (item[idKey] !== undefined && item[urlKey] !== undefined) {
                                targetObject[index][idKey] = null;
                                targetObject[index][urlKey] = null;
                            }
                        });
                    } else {
                        if (targetObject[idKey] !== undefined && targetObject[urlKey] !== undefined) {
                            targetObject[idKey] = null;
                            targetObject[urlKey] = null;
                        }
                    }

                    console.log('targetObject after removal:', targetObject);
                };

                

                

                // Ensure blocks are saved before form submission
                onMounted(() => {
                    const form = document.querySelector('#post');
                    form.addEventListener('submit', (event) => {
                        const input = document.getElementById('page-builder-blocks');
                        input.value = JSON.stringify(blocks.value);

                        const frontendSettingsInput = document.getElementById('page-builder-frontend-settings');
                        frontendSettingsInput.value = JSON.stringify(frontendSettings.value);
                    });

                    // Handle the "Preview" button click
                    const previewButton = document.querySelector('#post-preview'); // ID der Vorschau-Schaltfläche
                    if (previewButton) {
                        previewButton.addEventListener('click', (event) => {
                            const input = document.getElementById('page-builder-blocks');
                            input.value = JSON.stringify(blocks.value);

                            const frontendSettingsInput = document.getElementById('page-builder-frontend-settings');
                            frontendSettingsInput.value = JSON.stringify(frontendSettings.value);

                            // Optional: Console-Log für Debugging
                            console.log('Preview button clicked. Page Builder data updated.');
                        });
                    }
                });

                /**
                 * Initialisierung TinyMCE
                 */
                const pageFormats = 'Paragraph=p; Heading 1=h1; Heading 2=h2; Heading 3=h3; Heading 4=h4; Heading 5=h5; Heading 6=h6'
                const postFormats = 'Paragraph=p; Heading 2=h2; Heading 3=h3; Heading 4=h4; Heading 5=h5; Heading 6=h6'


                // Funktion zur Initialisierung von TinyMCE
                const initTinyMCE = () => {
                    console.log('initTinyMCE called');
                    nextTick(() => {
                        blocks.value.forEach((block) => {
                            console.log('block in forEach of tinyMCE: ', block);
                            const selector = `#tinymce-${block.id}`;
                            const existingEditor = tinymce.get(selector.substring(1));
                            if (existingEditor) {
                                existingEditor.remove();
                            }

                            if (block.type === 'paragraph' || block.type === 'headline' || block.type === 'text-section' || block.type === 'conclusion') {
                                console.log('block.type: ', block.type);
                                const selector = `#tinymce-${block.id}`;
                                console.log('selector: ', selector);
                                if (!tinymce.get(selector.substring(1))) { // Überprüfen, ob TinyMCE bereits initialisiert wurde
                                    tinymce.PluginManager.load('code', '<?php echo plugin_dir_url(__FILE__) . '/includes/js/tinyMCE/plugins/code/plugin.min.js'?>');
                                    console.log('tinymce.PluginManager: ', tinymce.PluginManager);

                                    tinymce.init({
                                        selector: selector,
                                        // plugins: 'link lists advlist paste table code fullscreen',
                                        plugins: 'link image textcolor lists',
                                        toolbar: 'formatselect | undo redo | bold italic underline | alignleft aligncenter alignright | outdent indent | numlist bullist | blockquote | link image',
                                        block_formats: postType === 'page' ? pageFormats : postFormats,
                                        menubar: false,
                                        setup: (editor) => {
                                            // testing custom button 
                                            editor.addButton( 'ampforwp_tc_button', {
                                                text: 'Copy The Content',
                                            });

                                            editor.on('change', () => {
                                                block.content = editor.getContent();
                                            });
                                        },
                                    });
                                }
                            }

                            if(block.type === 'flexible-content') {
                                console.log('block.type: ', block.type);
                                const selector = `.tinymce-${block.id}`;
                                console.log('selector: ', selector);

                                // Finde alle Elemente mit dem gegebenen Selektor
                                const elements = document.querySelectorAll(selector);
                                console.log('elements: ', elements);

                                // Zuerst alle existierenden TinyMCE-Editoren mit diesem Selektor entfernen
                                elements.forEach((element) => {
                                    const elementId = element.id;
                                    const existingEditor = tinymce.get(elementId);
                                    if (existingEditor) {
                                        existingEditor.remove();
                                    }
                                });

                                // Neuinitialisierung der TinyMCE-Editoren nach dem Entfernen
                                elements.forEach((element, index) => {
                                    console.log('element: ', element);
                                    console.log('index: ', index);
                                    const elementId = element.id;

                                    const containerHeight = document.querySelector('.lightbox-content').offsetHeight;
                                    const editorHeight = containerHeight - 232; // Hier einen Puffer von 100px für andere Inhalte lassen

                                    // Hier den richtigen Kontext finden, in dem gespeichert werden soll
                                    const flexibleContentIndex = parseInt(element.dataset.flexibleContentIndex);
                                    const itemIndex = parseInt(element.dataset.itemIndex);

                                    console.log('get the element with the id: ', elementId);
                                    if (!tinymce.get(elementId)) { // Überprüfen, ob TinyMCE bereits für dieses Element initialisiert wurde
                                        // tinymce.PluginManager.load('code', '/wp-content/plugins/newwways-page-builder/includes/js/tinyMCE/plugins/code/plugin.min.js');
                                        tinymce.PluginManager.load('code', '<?php echo plugin_dir_url(__FILE__) . '/includes/js/tinyMCE/plugins/code/plugin.min.js'?>');
                                        console.log('tinymce.PluginManager: ', tinymce.PluginManager);
                                        tinymce.init({
                                            selector: `#${elementId}`,
                                            plugins: 'code paste link image textcolor lists',
                                            paste_as_text: true, // Stellt sicher, dass alles als einfacher Text eingefügt wird
                                            toolbar: 'formatselect | tagline | gray-text | undo redo | bold italic underline | alignleft aligncenter alignright | outdent indent | numlist bullist check_bullist condensed_check_bullist | blockquote | link | code',
                                            block_formats: postType === 'post' ? postFormats : pageFormats,
                                            menubar: false,
                                            height: editorHeight, // Setzt die Höhe des Editors dynamisch
                                            setup: (editor) => {
                                                console.log('editor: ', editor);

                                                // Benutzerdefinierte Schaltfläche zur Toolbar hinzufügen
                                                editor.addButton('tagline', {
                                                    text: 'Tagline',
                                                    icon: false, // Hier könntest du ein Icon hinzufügen, wenn gewünscht
                                                    onclick: function () {
                                                        // Cursorposition speichern
                                                        const bookmark = editor.selection.getBookmark(2);

                                                        // Überprüfe, ob Text ausgewählt ist
                                                        const selectedText = editor.selection.getContent({format: 'text'});

                                                        if (selectedText) {
                                                            // Füge den ausgewählten Text als <p> mit der Klasse 'tagline' ein
                                                            editor.insertContent('<p class="tagline">' + selectedText + '</p>');
                                                        } else {
                                                            // Finde das Element an der aktuellen Cursorposition
                                                            const node = editor.selection.getNode();
                                                            
                                                            // Wenn das aktuelle Element kein <p> mit der Klasse 'tagline' ist, wandle es um
                                                            if (node.nodeName !== 'P' || !node.classList.contains('tagline')) {
                                                                // Füge einen <p> mit der Klasse 'tagline' hinzu
                                                                editor.execCommand('FormatBlock', false, 'p');
                                                                const currentElement = editor.selection.getNode();
                                                                currentElement.classList.add('tagline');
                                                            }
                                                        }

                                                        // Die Auswahl wiederherstellen, um den Text korrekt zu behalten
                                                        editor.selection.moveToBookmark(bookmark);
                                                    }
                                                });
                                                
                                                
                                                // Benutzerdefinierte Schaltfläche zur Toolbar hinzufügen
                                                editor.addButton('gray-text', {
                                                    text: 'Absatz in Grau',
                                                    icon: false, // Hier könntest du ein Icon hinzufügen, wenn gewünscht
                                                    onclick: function () {
                                                        // Cursorposition speichern
                                                        const bookmark = editor.selection.getBookmark(2);

                                                        // Überprüfe, ob Text ausgewählt ist
                                                        const selectedText = editor.selection.getContent({format: 'text'});

                                                        if (selectedText) {
                                                            // Füge den ausgewählten Text als <p> mit der Klasse 'tagline' ein
                                                            editor.insertContent('<p class="gray">' + selectedText + '</p>');
                                                        } else {
                                                            // Finde das Element an der aktuellen Cursorposition
                                                            const node = editor.selection.getNode();
                                                            
                                                            // Wenn das aktuelle Element kein <p> mit der Klasse 'tagline' ist, wandle es um
                                                            if (node.nodeName !== 'P' || !node.classList.contains('gray')) {
                                                                // Füge einen <p> mit der Klasse 'tagline' hinzu
                                                                editor.execCommand('FormatBlock', false, 'p');
                                                                const currentElement = editor.selection.getNode();
                                                                currentElement.classList.add('gray');
                                                            }
                                                        }

                                                        // Die Auswahl wiederherstellen, um den Text korrekt zu behalten
                                                        editor.selection.moveToBookmark(bookmark);
                                                    }
                                                });

                                                // register custom button for check list (ul with custom class)
                                                // Benutzerdefinierte Schaltfläche zur Toolbar hinzufügen
                                                editor.addButton('check_bullist', {
                                                    text: 'Check Bullet',
                                                    icon: false,
                                                    onclick: function () {
                                                        // Cursorposition speichern
                                                        const bookmark = editor.selection.getBookmark(2);

                                                        // Füge die ungeordnete Liste hinzu
                                                        editor.execCommand('InsertUnorderedList');

                                                        // Die Auswahl wiederherstellen, um das neu eingefügte UL-Element zu erreichen
                                                        editor.selection.moveToBookmark(bookmark);

                                                        // Finde das nächstgelegene UL-Element an der aktuellen Cursorposition
                                                        const node = editor.selection.getNode();
                                                        const closestUl = editor.dom.getParent(node, 'ul');

                                                        if (closestUl) {
                                                            closestUl.classList.add('check_bullist');
                                                        }
                                                    }
                                                });

                                                // register custom button for condensed check list (ul with custom class)
                                                // Benutzerdefinierte Schaltfläche zur Toolbar hinzufügen
                                                editor.addButton('condensed_check_bullist', {
                                                    text: 'Condensed Check Bullet',
                                                    icon: false,
                                                    onclick: function () {
                                                        // Cursorposition speichern
                                                        const bookmark = editor.selection.getBookmark(2);

                                                        // Füge die ungeordnete Liste hinzu
                                                        editor.execCommand('InsertUnorderedList');

                                                        // Die Zahl wiederherstellen, um das neu eingefügte UL-Element zu erreichen
                                                        editor.selection.moveToBookmark(bookmark);

                                                        // Finde das zurückstgelegene UL-Element an der aktuellen Cursorposition
                                                        const node = editor.selection.getNode();
                                                        const closestUl = editor.dom.getParent(node, 'ul');

                                                        if (closestUl) {
                                                            closestUl.classList.add('condensed');
                                                            closestUl.classList.add('check_bullist');
                                                        }
                                                    }
                                                });


                                                editor.on('change', () => {
                                                    const updatedContent = editor.getContent();
                                                    block.flexibleContent[flexibleContentIndex][itemIndex].content = updatedContent;
                                                    console.log('Updated content:', updatedContent);
                                                });
                                            },
                                            // Style für die custom ul den Editor
                                            content_style: `
                                            p.tagline {
                                                font-weight: bold;
                                                color: #606081;
                                            }
                                            p.gray {
                                                color: #606081;
                                            }
                                            ul, ol {
                                                padding-left: 20px;
                                            }
                                            ul.check_bullist { 
                                                list-style-type: square; 
                                                padding-left: 20px; 
                                            }
                                            ul.check_bullist li {
                                                position: relative;
                                                /* Entfernt die Standard-Punkte */
                                                list-style: none; 
                                            }
                                            ul.check_bullist li::before {
                                                content: '';
                                                position: absolute;
                                                left: -20px;
                                                top: 10px;
                                                transform: translateY(-50%);
                                                width: 14px;
                                                height: 15px;
                                                background-size: contain;
                                                background-repeat: no-repeat;
                                                background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><circle cx="10" cy="10" r="10" fill="red"/></svg>'); /* Dein SVG-Code hier */
                                                background-image: url('data:image/svg+xml;utf8,%3Csvg%20width%3D%2220px%22%20height%3D%2220px%22%20viewBox%3D%220%200%2020%2020%22%20version%3D%221.1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%3E%3Ctitle%3Eicons%2Fchecker%3C%2Ftitle%3E%3Cg%20id%3D%22wissen---blog%22%20stroke%3D%22none%22%20stroke-width%3D%221%22%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20id%3D%22blog-detail%22%20transform%3D%22translate(-294%2C%20-2959)%22%20fill%3D%22%23BF1692%22%20fill-rule%3D%22nonzero%22%3E%3Cg%20id%3D%22section%22%20transform%3D%22translate(294%2C%20226)%22%3E%3Cg%20id%3D%22item%22%20transform%3D%22translate(0%2C%201912)%22%3E%3Cg%20id%3D%22icons%2Fchecker%22%20transform%3D%22translate(0%2C%20823.1875)%22%3E%3Cpolygon%20points%3D%220%208.52273215%202.85714205%205.68183038%207.14285647%209.94319646%2017.1428577%200%2020%202.84090177%207.14285647%2015.625%22%3E%3C%2Fpolygon%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E');
                                            }    
                                            `
                                            , 
                                        });
                                    }
                                });
                            }
                        });
                    });
                };

                // TinyMCE initialisieren, wenn die Komponente verschoben wird 
                const onDragEnd = () => {
                    initTinyMCE(); // TinyMCE-Editoren nach dem DND-Vorgang neu initialisieren
                };

                // TinyMCE initialisieren, wenn die Komponente gemountet wird
                onMounted(() => {
                    initTinyMCE();
                });

                // TinyMCE initialisieren, wenn die Komponente aktualisiert wird
                onUpdated(() => {
                    initTinyMCE();
                });


                /**
                 * ///////////////////////////
                 * /// Reusable Components ///
                 * ///////////////////////////
                 */

                /*
                 * Transition Component
                 */
                // adjustItems
                app.component('tab-transition-component', {
                    props: {
                        activeTabNumber: {
                            type: Number,
                            required: true
                        }
                    },
                    data() {
                        return {
                            previousTabNumber: null,
                            oldHeight: null,
                            newHeight: null
                        };
                    },
                    computed: {
                        transitionName() {
                            if (this.previousTabNumber === null) {
                                return 'none'; // Keine Animation beim ersten Laden
                            }
                            return this.activeTabNumber > this.previousTabNumber ? 'tabsfromright' : 'tabsfromleft';
                        }
                    },
                    watch: {
                        activeTabNumber(newVal, oldVal) {
                            this.previousTabNumber = oldVal !== undefined ? oldVal : null;

                            // Emit event to parent component
                            this.$emit('active-tab-changed', newVal);
                            console.log('active-tab-changed: activeTabNumber: ', newVal);
                        }
                    },
                    methods: {
                        onBeforeEnter(el) {
                            console.log('%c before enter called', 'color: red; font-weight: bold;');

                            // el.style.height = '0px';
                            // el.style.height = this.oldHeight + 'px';
                        },
                        onEnter(el, done) {
                            console.log('%c enter called', 'color: green; font-weight: bold;');

                            this.newHeight = el.scrollHeight;
                            console.log('newHeight: ', this.newHeight);

                            const newHeight = el.scrollHeight;
                            console.log('newHeight: ', newHeight);

                            el.style.opacity = '0';
                            el.style.height = this.oldHeight + 'px';
                            
                            el.style.transition = 'all 0.5s ease';
                            el.style.opacity = '1';


                            void el.offsetHeight; // Reflow erzwingen
                            // el.style.height = newHeight + 'px';

                            el.style.height = this.newHeight + 'px';

                            // try to remove statc height
                            el.addEventListener('transitionend', function callback(event) {
                                if (event.propertyName === 'height') {
                                    el.style.height = '';
                                    el.style.transition = '';
                                    el.removeEventListener('transitionend', callback);
                                    done();
                                }
                            });

                        },
                        onBeforeLeave(el) {
                            console.log('%c before leave called', 'color: red; font-weight: bold;');

                            this.oldHeight = el.offsetHeight;
                            console.log('oldHeight: ', this.oldHeight);
                        },
                        onLeave(el) {
                            console.log('%c leave called', 'color: green; font-weight: bold;');

                            el.style.opcity = '1';
                            el.style.transition = 'all 0.5s ease';
                            el.style.opcity = '0';

                            void el.offsetHeight; // Reflow erzwingen
                        }
                    },
                    template: `
                        <transition
                            :name="transitionName"
                            mode="out-in"
                            @before-enter="onBeforeEnter"
                            @enter="onEnter"
                            @before-leave="onBeforeLeave"
                            @leave="onLeave"
                        >
                            <slot></slot>
                        </transition>
                    `
                });

                /**
                 * Flexibel Content Item Preview
                 * Module > "Flexible Content"
                 * Component > "Content Item Preview" (Preview of Content Item in Cols of Flexible Content)
                 * 
                 * Props:
                 * - item: Object
                 * - element: Object
                 * - index: Number
                 * - itemIndex: Number
                 * - iconSvg: String
                 * - preview: String
                 * - lightboxTitle: String
                 * 
                 * Methods:
                 * - handleEdit() -> 
                 * - handleDelete()
                 * 
                 */
                app.component('content-item-preview-component', {
                props: {
                    item: Object,
                    element: Object,
                    index: Number,
                    itemIndex: Number,
                    iconSvg: String,
                    mce: Boolean,
                    preview: String,
                    lightboxTitle: String,
                },
                template: `
                    <div class="content-item-preview-wrapper">
                        <!-- Actions -->
                        <div class="content-item-actions-wrapper">
                            <div class="content-item-actions">
                            <!-- Edit Button -->
                            <span class="btn" @click="handleEdit()">
                                <!-- Edit Icon -->
                                <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m14.304 4.844 2.852 2.852M7 7H4a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-4.5m2.409-9.91a2.017 2.017 0 0 1 0 2.853l-6.844 6.844L8 14l.713-3.565 6.844-6.844a2.015 2.015 0 0 1 2.852 0Z"/>
                                </svg>
                            </span>
                            <!-- Drag Handle Button -->
                            <span class="btn item-drag-handle">
                                <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M5 7h14M5 12h14M5 17h14"/>
                                </svg>
                            </span>
                            <!-- Delete Button -->
                            <span class="btn" @click="handleDelete">
                                <!-- Delete Icon -->
                                <svg class="w-6 h-6" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                                    <path fill-rule="evenodd" d="M8.586 2.586A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4a2 2 0 0 1 .586-1.414ZM10 6h4V4h-4v2Zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Z" clip-rule="evenodd"/>
                                </svg>
                            </span>
                            </div>
                        </div>

                        <!-- Content Item Header -->
                        <div class="content-item-header">
                            <div style="display: flex; align-items: center;">
                            <div class="item-icon-wrapper" v-html="iconSvg"></div>
                            <div class="content-item-name">
                                <p><strong>{{ item.name }}</strong></p>
                            </div>
                            </div>
                        </div>

                        <!-- Content Item Preview Body -->
                        <div class="content-item-preview-body">
                            <!-- Slot for custom content -->
                            <slot></slot>
                        </div>
                    </div>`
                    ,
                    computed: {
                        lightboxKey() {
                            return `edit-content-item-${this.element.id}-${this.index}-${this.itemIndex}`;
                        },
                        isLightboxOpen() {
                            return this.element.uiValues.isAddNewItemLightboxOpen === this.lightboxKey;
                        },
                    },
                    methods: {
                        handleEdit() {
                            this.element.uiValues.isAddNewItemLightboxOpen = this.lightboxKey;

                            // mce as string
                            console.log('mce type:', typeof this.mce);  // Prüfe den Typ des Props
                            console.log('handleEdit from content-item-preview-component', this.mce);
                            
                            if(this.mce) {
                                initTinyMCE();
                            }
                            // this.$emit('log-indices', this.index, this.itemIndex);
                            // this.$emit('init-tinymce');
                        },
                        handleDelete() {
                        this.$emit('delete-content-item', this.element, this.index, this.itemIndex);
                        },
                        closeLightbox() {
                        this.element.uiValues.isAddNewItemLightboxOpen = false;
                        },
                    },
                });
                app.component('item-tab-component', {
                    props: {
                        modelValue: {
                            type: Array,
                            required: true, // Dies stellt sicher, dass itemList ein Array ist
                        },
                        element: {
                            type: Object,
                            required: true,
                        },
                    },
                    data() {
                        return {
                            localItemlist: [...this.modelValue], // Erstelle eine lokale Kopie von modelValue (itemlist)
                        };
                    },
                    watch: {
                        // Überwache Änderungen an modelValue, aber vermeide unnötige Updates
                        modelValue: {
                            handler(newVal) {
                                // Wenn sich die Werte unterscheiden, aktualisiere localItemlist
                                if (JSON.stringify(newVal) !== JSON.stringify(this.localItemlist)) {
                                    this.localItemlist = [...newVal];
                                }
                            },
                            deep: true,
                        },
                        // Überwache Änderungen an localItemlist und emittiere sie zurück
                        localItemlist: {
                            handler(newVal) {
                                // Verhindere das Senden von Updates, wenn die Listen gleich sind
                                if (JSON.stringify(newVal) !== JSON.stringify(this.modelValue)) {
                                    this.$emit('update:modelValue', newVal);
                                }
                            },
                            deep: true,
                        }
                    },
                    template: `
                        <div class="tab-component">
                            <div class="tabs-wrapper">
                                <ul class="tabs">
                                    <draggable 
                                        v-model="localItemlist" 
                                        :group="{ name: 'tabs' }" 
                                        handle=".item-drag-handle" 
                                        :animation="200"
                                        tag="li"
                                        class="tab-wrapper"
                                        :item-key="item => item.id || item.name"
                                    >
                                        <template #item="{element: item, index}">
                                            <div 
                                                class="inner-tab" 
                                                @click="element.uiValues.secondaryTabNumber = index"
                                                :class="{ 'active': element.uiValues.secondaryTabNumber === index }"
                                            >
                                                <!-- {{ index + 1 }}. Item -->
                                                Tab: {{ item.name }}
                                                <span class="item-drag-handle">
                                                    <!-- Drag Handle Icon -->
                                                    ☰
                                                </span>
                                            </div>
                                        </template>
                                    </draggable>
                                </ul>
                                    
                                <ul class="tabs">
                                    <li class="tab-wrapper add-new" @click="addNewItem()">
                                        <div class="inner-tab">
                                            <span>
                                                Add New
                                            </span>
                                            <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14m-7 7V5"/>
                                            </svg>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                            <ul v-if="modelValue && modelValue.length > 0" class="tab-list"> 
                                <li v-for="(item, index) in modelValue" class="item-tab-wrapper">
                                    <div v-if="element.uiValues.secondaryTabNumber === index" class="active-tab">
                                        <div class="item-header" style="display: flex; justify-content: space-between;">
                                            <h2>Item {{ index + 1 }}</h2>
                                            <span class="btn" @click="deleteItem(index)">Item entfernen</span>
                                        </div>

                                        <div class="tab-content">
                                            <!-- Verwende einen Slot für den variablen Inhalt -->
                                            <slot :item="item" :index="index"></slot>
                                        </div>
                                    </div>
                                </li>
                            </ul> 
                        </div>
                    `,

                    methods: {
                        logItemList(itemlist) {
                            console.log('itemlist: ', itemlist);
                        },
                        // onDragEnd() {
                        //     this.$emit('update:itemlist', this.itemlist);  // Emittiere "update:itemlist" für die Synchronisierung
                        // },
                        addNewItem() {
                            this.$emit('add-new-item'); // Emittiere ein Event, um ein neues Item hinzuzufügen
                        },
                        deleteItem(index) {
                            // this.localItemlist.splice(index, 1);
                            this.$emit('delete-item', index); // Emittiere ein Event, um ein Item zu löschen
                        }
                    }
                });


                /**
                 * UI enhancements
                 * 
                 */

                // function enter(el, done) {
                //     // el.style.transform = 'translateX(100px)';
                //     // el.style.position = 'relative';
                //     const height = el.scrollHeight + 'px';
                //     el.style.opacity = '1';
                //     el.style.height = '0';
                //     el.style.padding = '';
                //     el.style.overflow = 'hidden';
                //     el.style.transition = 'all 0.5s ease';

                //     // Force a repaint to ensure the transition starts
                //     void el.offsetHeight;

                //     el.style.height = height;

                //     el.addEventListener('transitionend', function callback() {
                //         el.style.height = '';
                //         el.style.overflow = '';
                //         el.removeEventListener('transitionend', callback);
                //         done();
                //     });
                //     }

                // function leave(el, done) {
                //     // el.style.transform = 'translateX(-100px)';
                //     // el.style.position = 'absolute';
                //     el.style.opacity = '0';
                //     el.style.height = el.scrollHeight + 'px';
                //     el.style.padding = '0 1rem';
                //     el.style.overflow = 'hidden';
                //     el.style.transition = 'all 0.5s ease';

                //     // Force a repaint
                //     void el.offsetHeight;

                //     el.style.height = '0';

                //     el.addEventListener('transitionend', function callback() {
                //         el.style.height = '';
                //         el.style.overflow = '';
                //         el.removeEventListener('transitionend', callback);
                //         done();
                //     });
                // }
                

                return {
                    /**
                     * Information about WP Environment
                     */
                    checkIfColSettingsExist, 
                    // is User admin
                    isUserAdmin,
                    // post type (e.g. page, post)
                    postType,

                    // availableDates from session CPT
                    availableDates,
                    // data from theme Settings
                    availableForms,

                    // TODO: not sure if "frontendSettings" is needed. 
                    // Page Builder Frontend styles (e.g. header) 
                    frontendSettings,


                    /** Delete if finished */
                    addVideoLightboxToHero,

                    // Mask Styles (e.g. for Image-Item and Testimonial)
                    maskStyles,


                    /**
                     * Global Variables to manipulate the UI
                     */
                    // Add New Element Lightbox
                    isAddNewElementLightboxOpen,
                    // Page Builder layout functions 
                    togglePreview,
                    // Fullscreen
                    isFullscreen,
                    toggleFullscreen,


                    

                    // Data to enable copy and paste JSON data for page builder
                    blockAsString,
                    updateBlocks,
                    // duplicate block
                    duplicateBlock,
                    // copy block
                    copyBlock,
                    // enable pasting JSOn as blocks 
                    enableAdvancedJsonEditing, // global var to show and hide advanced json editing ui elements 
                    pastedJsonBlock,
                    pasteJsonBlockToPage,

                    // Utils
                    logIndices,
                    arraysAreEqual,
                    truncateText,

                    // 
                    initTinyMCE,
                    onDragEnd,

                
                    

                    

                    // Page Builder basic blocks
                    blocks,
                    availableBlocks,
                    availableBlocksForType,
                    modalToAddNewBlocksOpen,

                    // Change Content 
                    openMediaLibrary,
                    removeImageFromElement,

                    // New Media Asset functions 
                    openMediaLibraryAndSaveUrlAndImage,
                    removeImageUrlAndIdFromElement,
                    
                    // Change Blocks
                    removeBlock,
                    addNewBlock, // adds just a new block
                    addFieldToBlock, // adds a field to an block | block -> field

                    // switch between tabs (content, apperance and spacing)
                    updateActiveTab,

                    // For Text-Image module 
                    addListItem, // adds a listItem to an field | block-> field -> listItems array
                    deleteTextContent,
                    deleteListItem,
                    changeOrder,


                    /**
                     * Flexible content
                     */
                    // Cols
                    adjustCols, 
                    increaseColWidth,
                    decreaseColWidth,

                    syncBorderRadius,
                    editCol,
                    availableColumnStyles,
                    // Content items
                    addContentItemToBlock,
                    deleteContentItemFromBlock,
                    attachMediaAsstes,
                    // imageComposition
                    addItemToImageComposition,
                    removeItemFromImageComposition,
                    // global functions for different blocks
                    removeItemFromElement,

                    /** Content Item Functions */
                    adjustItems,
                    // TODO: Vorübergehend, um spacingSetting items hinzuzufügen 
                    checkIfItemHasSpacingSettings,

                    // For CTAs
                    availableCtaDirections,
                    availableCtaAppearances,
                    addCta,

                    // For Tabel 
                    addRow,
                    removeRow,
                    addColumn,
                    removeColumn,


                    // For Success Story Module
                    successStoryCategories,

                    // For Testimonial Module 
                    addTestimonialItem,
                    deleteTestimonial,

                    // for Pricing Table module
                    addPricingTableRow,
                    deletePricingTableRow,
                    addPricingTableCtaRowInnerRow,
                    deletePricingTableCtaRowInnerRow,
                    // add new col (currently not in use)
                    addPricingTableOfferCol,
                    handlePricingColCountChangeOnUI,


                    // For Feature Grid module
                    addFeatureGridItem,
                    // For Image Grid module
                    addImageGridItem,
                    deleteImageGirdItem,
                    // for accordion Module
                    addAccordionItem,
                    deleteAccordionItem,
                    // for Sticky Text-Image module
                    addStickySection,
                    removeStickySection,
                    // for slider item module
                    availableSliderAppearances,
                    addSliderItem,
                    removeSliderItem,

                    // Additional options for Flexible Content
                    advancedSettingsOptions,

                    saveBlocks,

                    // flexible row functions
                    addNewBlockToRow,

                    // TODO: Delete for Deloying 
                    addAdvancedSettings,
                    addNumberToggle,


                    // *** Utils *** //
                    fetchImageUrl,

                    // debugging 
                    log,

                    // ui enhancements
                    // enter,
                    // leave
                }
            }
        })
        app.component('draggable', vuedraggable);
        app.mount('#app')

        </script>

        <!-- <style>
            /* Page Builder Styles */
            .nw-button {
                background-color: #eee;
                color: #fff;
                padding: 10px;
                cursor: pointer;
                border-radius: 10px;
                border: 1px solid #333;
            }
        </style> -->


        <!-- <h1>Grenzlotsen Page Builder</h1>
        <textarea id="mein-tinymce-editor" name="content" style="height: 300px; width: 100%;">
            <?php echo esc_textarea($post->post_content); ?>
        </textarea>
        <button onclick="log()saveContent()">Speichern</button> -->


        <!-- <h1>Mein Page Builder</h1>
        <textarea id="mein-tinymce-editor" name="mein_text_input" style="height: 300px; width: 100%;">
            <?php echo esc_textarea($mein_text_input); ?>
        </textarea>
        <button onclick="log()saveContent()">Speichern</button> -->

        <script>
            document.addEventListener('DOMContentLoaded', function() {
                tinymce.init({
                    selector: '#mein-tinymce-editor',
                    plugins: 'link image textcolor lists',
                    toolbar: 'formatselect | undo redo | bold italic underline | alignleft aligncenter alignright | outdent indent | numlist bullist | blockquote | link image',
                    menubar: false,
                    setup: function (editor) {
                        editor.on('change', function () {
                            tinymce.triggerSave();
                        });
                    }
                });

                function saveContent() {
                    var content = tinymce.get('mein-tinymce-editor').getContent();
                    jQuery.post(ajaxurl, {
                        'action': 'save_my_content',
                        'content': content,
                        'post_id': <?php echo json_encode($post->ID); ?>
                    }, function(response) {
                        alert('Der Inhalt wurde gespeichert: ' + response);
                    });
                }
            });
        </script>
    </div>
<?php
}




// /* AJAX Handlers */
// add_action('wp_ajax_save_my_content', 'save_my_content_callback');

// function save_my_content_callback() {
//     if (!current_user_can('edit_post', $_POST['post_id'])) {
//         wp_die('Berechtigung verweigert');
//     }
//     $post_id = $_POST['post_id'];
//     $content = $_POST['content'];
//     wp_update_post([
//         'ID'           => $post_id,
//         'post_content' => wp_kses_post($content)
//     ]);
//     echo 'Speichern erfolgreich!';
//     wp_die();
// }




// function test_save_post_hook($post_id) {
//     // var_dump('Test hook called for post ID: ' . $post_id);
//     // error_log('Save post hook triggered for post ID: ' . $post_id, 3, '/homepages/8/d622921182/htdocs/clickandbuilds/Grenzlotsen/php-errors.log');
//     error_log('This is a test from a regular function!!!');
//     // trigger_error('Save post hook triggered for post ID: ' . $post_id, E_USER_WARNING);
// }
// add_action('save_post', 'test_save_post_hook');



function save_post_and_revision($post_id) {
    // $user_id = get_current_user_id();
    // set_transient("save_post_and_revision_{$user_id}", '/////// save_post_and_revision triggered ///////', 45);

    // Prüfe, ob es eine Revision ist
    if (wp_is_post_revision($post_id)) {
        // Speichere die PageBuilder Meta-Daten in der Revision
        save_page_builder_meta_in_revision($post_id);
        return; // Stoppe die Ausführung, um nicht doppelt zu speichern
    }

    // Wenn es keine Revision ist, ist es ein normaler Speichervorgang
    mein_save_post_meta($post_id);
}
add_action('save_post', 'save_post_and_revision', 10, 2);



function mein_save_post_meta($post_id) {
    if (wp_is_post_revision($post_id)) {
        return;
    }

    $user_id = get_current_user_id();
    // set_transient("save_post_hook_test_{$user_id}", '/////// mein_save_post_meta() triggered ///////', 45);

    // Check for a custom flag to avoid recursion
    if (did_action('save_post_custom_flag')) {
        return;
    }

    // Set a custom action to prevent recursion
    do_action('save_post_custom_flag');

    
    // Anstelle von error_log, speichere die Nachricht in einem Transient
    // $user_id = get_current_user_id(); // Speichere die Nachricht spezifisch für den Benutzer
    // set_transient("save_post_hook_test_{$user_id}", '/////// mein_save_post_meta() triggered ///////', 45);


    // Überprüfen, ob unser nonce Feld gesetzt ist.
    if (!isset($_POST['grenzlotsen_page_builder_nonce'])) {
        return;
    }
    // Überprüfen, dass das nonce Feld gültig ist.
    if (!wp_verify_nonce($_POST['grenzlotsen_page_builder_nonce'], 'grenzlotsen_page_builder')) {
        return;
    }
    // Überprüfen, ob der Benutzer die Berechtigung hat.
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }
    // Überprüfen, ob es sich nicht um einen automatisierten Prozess handelt.
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    // Prüfen, ob es sich um einen Beitrag (post) handelt
    if (get_post_type($post_id) === 'post') {
        // Hole die Inhalte der Meta Box
        $title_big = get_post_meta($post_id, '_grenzlotsen_title_big', true);
        $title_small = get_post_meta($post_id, '_grenzlotsen_title_small', true);
        $intro_text = get_post_meta($post_id, '_grenzlotsen_intro', true);

        // Formatieren der Meta Box-Inhalte mit HTML-Tags
        $formatted_meta_content = '';
        if (!empty($title_big)) {
            $formatted_meta_content .= '<h1>' . esc_html($title_big) . '</h1>';
        }
        if (!empty($title_small)) {
            $formatted_meta_content .= '<h2>' . esc_html($title_small) . '</h2>';
        }
        if (!empty($intro_text)) {
            $formatted_meta_content .= '<p>' . wp_kses_post($intro_text) . '</p>';
        }
    } else {
        $formatted_meta_content = '';
    }

    // Save page builder data 
    if (array_key_exists('page_builder_blocks', $_POST)) {
        $raw_blocks = $_POST['page_builder_blocks'];
        $blocks = wp_slash($_POST['page_builder_blocks']); // Slashes hinzufügen, um spezielle Zeichen zu erhalten
        // error_log(print_r($blocks, true)); // Logge die Daten in das PHP-Fehlerprotokoll

        update_post_meta($post_id, '_page_builder_blocks', $blocks);

        $extracted_content = extract_text_from_blocks($raw_blocks);

        // set_transient("save_post_hook_extracted_content_{$user_id}", 'extracted_content (from mein_save_post_meta()): ' . $extracted_content, 45);

        // Kombiniere den Inhalt des Page Builders mit den formatierten Meta Box-Inhalten
        $combined_content = $formatted_meta_content . ' ' . $extracted_content;

        if (!empty($combined_content)) {
            // Hier setzen wir das Flag, um zu verhindern, dass save_post erneut ausgelöst wird
            remove_action('save_post', 'save_post_and_revision', 10); // Entferne den Hook vorübergehend

            wp_update_post(array(
                'ID' => $post_id,
                'post_content' => $combined_content // Hier speichern wir den kombinierten Text in den post_content
            ));

            add_action('save_post', 'save_post_and_revision', 10); // Füge den Hook wieder hinzu
        }
    } else {
        // Hier setzen wir das Flag, um zu verhindern, dass save_post erneut ausgelöst wird
        remove_action('save_post', 'save_post_and_revision', 10); // Entferne den Hook vorübergehend

        wp_update_post(array(
            'ID' => $post_id,
            'post_content' => $formatted_meta_content // Hier speichern wir den kombinierten Text in den post_content
        ));

        add_action('save_post', 'save_post_and_revision', 10); // Füge den Hook wieder hinzu
    }

    // Save page builder frontend settings 
    if (array_key_exists('page_builder_frontend_settings', $_POST)) { 
        $frontend_settings = wp_slash($_POST['page_builder_frontend_settings']); // Slashes hinzufügen, um spezielle Zeichen zu erhalten 
        // error_log(print_r($frontend_settings, true)); // Logge die Daten in das PHP-Fehlerprotokoll
        update_post_meta($post_id, '_page_builder_frontend_settings', $frontend_settings); 
    }

    // Sanitize und speichere den Input 
    if (array_key_exists('mein_text_input', $_POST)) {
        update_post_meta(
            $post_id,
            '_mein_text_input',
            wp_kses_post($_POST['mein_text_input']) // Nutzung von wp_kses_post zur Beibehaltung von Formatierungen
        );
    }
}
// add_action('save_post', 'mein_save_post_meta', 10);



// Save Data in Revision 
// Hook to save PageBuilder meta data in revisions
// Hook to save PageBuilder meta data in revisions
function save_page_builder_meta_in_revision($revision_post_id) {
    $user_id = get_current_user_id();
    // set_transient("save_post_hook_from_revision_{$user_id}", '/////// save_page_builder_meta_in_revision triggered: revision_post_id: ' . $revision_post_id . '///////', 45);

    // Prüfen, ob es sich um einen Beitrag (post) handelt
    if (get_post_type($revision_post_id) === 'post') {
        // Hole die Inhalte der Meta Box
        $title_big = get_post_meta($revision_post_id, '_grenzlotsen_title_big', true);
        $title_small = get_post_meta($revision_post_id, '_grenzlotsen_title_small', true);
        $intro_text = get_post_meta($revision_post_id, '_grenzlotsen_intro', true);

        // Formatieren der Meta Box-Inhalte mit HTML-Tags
        $formatted_meta_content = '';
        if (!empty($title_big)) {
            $formatted_meta_content .= '<h1>' . esc_html($title_big) . '</h1>';
        }
        if (!empty($title_small)) {
            $formatted_meta_content .= '<h2>' . esc_html($title_small) . '</h2>';
        }
        if (!empty($intro_text)) {
            $formatted_meta_content .= '<p>' . wp_kses_post($intro_text) . '</p>';
        }
    } else {
        $formatted_meta_content = '';
    }

    // Prüfen, ob `page_builder_blocks` in $_POST vorhanden ist
    if (array_key_exists('page_builder_blocks', $_POST)) {
        // Hole die geänderten Daten direkt aus $_POST
        $raw_blocks = $_POST['page_builder_blocks'];
        $blocks = wp_slash($raw_blocks); // Füge Slashes hinzu, um spezielle Zeichen zu erhalten
        
        // set_transient("save_post_hook_from_revision_page_builder_blocks{$user_id}", '/////// there are blocks: print _POST: ' . print_r($_POST, true) . ' ///////  print raw_blocks: ' . print_r($raw_blocks, true) . ' /////// print blocks: ' . print_r($blocks, true) . ' /////// ///////', 45);

        // Speichere die Meta-Daten in der Revision
        update_metadata('post', $revision_post_id, '_page_builder_blocks', $blocks);


        $extracted_content = extract_text_from_blocks($raw_blocks);
        // Kombiniere den Inhalt des Page Builders mit den formatierten Meta Box-Inhalten
        $combined_content = $formatted_meta_content . ' ' . $extracted_content;

        if (!empty($combined_content)) {
            // Hier setzen wir das Flag, um zu verhindern, dass save_post erneut ausgelöst wird
            remove_action('save_post', 'save_post_and_revision', 10); // Entferne den Hook vorübergehend

            wp_update_post(array(
                'ID' => $revision_post_id,
                'post_content' => $combined_content // Hier speichern wir den kombinierten Text in den post_content
            ));

            add_action('save_post', 'save_post_and_revision', 10); // Füge den Hook wieder hinzu
        }
    } else {
        // set_transient("save_post_hook_from_revision_page_builder_blocks{$user_id}", '/////// there are no blocks -> $_POST: ' . print_r($_POST, true) . '///////', 45);
        // Hier setzen wir das Flag, um zu verhindern, dass save_post erneut ausgelöst wird
        remove_action('save_post', 'save_post_and_revision', 10); // Entferne den Hook vorübergehend

        wp_update_post(array(
            'ID' => $revision_post_id,
            'post_content' => $formatted_meta_content // Hier speichern wir den kombinierten Text in den post_content
        ));

        add_action('save_post', 'save_post_and_revision', 10); // Füge den Hook wieder hinzu
    }


    // Frontend-Einstellungen speichern (falls erforderlich)
    if (array_key_exists('page_builder_frontend_settings', $_POST)) {
        $frontend_settings = wp_slash($_POST['page_builder_frontend_settings']); // Füge Slashes hinzu
        update_metadata('post', $revision_post_id, '_page_builder_frontend_settings', $frontend_settings);

        // Debugging: Transient für Frontend-Einstellungen
        // set_transient("debug_revision_frontend_settings_{$user_id}", 'Revision frontend settings: ' . print_r($frontend_settings, true), 45);
    }
}





function extract_text_from_blocks($blocks) {
    $user_id = get_current_user_id(); // Speichere die Nachricht spezifisch für den Benutzer
    // set_transient("extract_text_from_blocks_fn_{$user_id}", '/////// extract_text_from_blocks() triggered ///////' . $blocks, 45);

    // Entferne die Slashes, die WordPress hinzugefügt hat
    $unslashed_blocks = wp_unslash($blocks);

    // Decodiere den JSON-Inhalt
    $decoded_blocks = json_decode($unslashed_blocks, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        // Falls es ein Problem mit dem Dekodieren gibt, speichere die Fehlermeldung
        // set_transient("extract_text_from_blocks_fn_decoding_error_{$user_id}", 'JSON Decode Error: ' . json_last_error_msg(), 45);
        return ''; // Beende die Funktion, wenn das JSON nicht korrekt ist
    }

    // Speicher die dekodierten Blöcke als transient für Debugging
    // set_transient("extract_text_from_blocks_fn_decoded_blocks_{$user_id}", 'extract_text_from_blocks() -> ' . print_r($decoded_blocks, true), 45);


    $text_content = '';

    // Rekursiv die Blöcke durchlaufen
    if (!empty($decoded_blocks) && is_array($decoded_blocks)) {
        foreach ($decoded_blocks as $block) {
            // Prüfen, ob es sich um einen "flexible-content"-Block handelt
            if (isset($block['type']) && $block['type'] === 'flexible-content' && isset($block['flexibleContent'])) {
                // Durchlaufe die flexiblen Inhalte
                foreach ($block['flexibleContent'] as $column) {
                    foreach ($column as $content_item) {
                        // Extrahiere den Text aus Texteditor-Blöcken
                        if (isset($content_item['type']) && $content_item['type'] === 'textEditor' && isset($content_item['content'])) {
                            $text_content .= $content_item['content'] . ' '; // Behalte die HTML-Formatierung bei
                        }

                        // Verarbeite Bilder und füge sie in den Content ein
                        if (isset($content_item['type']) && $content_item['type'] === 'image' && isset($content_item['mediaUrl'])) {
                            $img_tag = '<img src="' . esc_url($content_item['mediaUrl']) . '" ';
                            if (!empty($content_item['alt'])) {
                                $img_tag .= 'alt="' . esc_attr($content_item['alt']) . '" ';
                            }
                            $img_tag .= '/>';

                            // Füge das Bild in den Inhalt ein
                            $text_content .= $img_tag;

                            // Füge die Bildunterschrift hinzu, falls vorhanden
                            if (!empty($content_item['caption'])) {
                                $text_content .= '<figcaption>' . esc_html($content_item['caption']) . '</figcaption>';
                            }
                        }

                        // Verarbeite Tabellen
                        if (isset($content_item['type']) && $content_item['type'] === 'tabel' && isset($content_item['rows'])) {
                            $table_content = '<table>';

                            // Durchlaufe die Zeilen der Tabelle
                            foreach ($content_item['rows'] as $row) {
                                $table_content .= '<tr>';

                                // Durchlaufe die Spalten der Zeile
                                foreach ($row['columns'] as $column) {
                                    $table_content .= '<td>';
                                    $table_content .= isset($column['content']) ? esc_html($column['content']) : ''; // Inhalt der Zelle
                                    $table_content .= '</td>';
                                }

                                $table_content .= '</tr>';
                            }

                            $table_content .= '</table>';

                            // Füge die generierte Tabelle in den Text ein
                            $text_content .= $table_content;
                        }
                    }
                }
            }
        }
    }
    return $text_content;
}



// Anzeigen der gespeicherten Transient-Meldung im Admin-Bereich
add_action('admin_notices', function() {
    $user_id = get_current_user_id();
    // save_post_and_revision_
    if ($notice = get_transient("save_post_and_revision_{$user_id}")) {
        echo "<div class='notice notice-success is-dismissible'><p>{$notice}</p></div>";
        delete_transient("save_post_and_revision_{$user_id}"); // Lösche den Transient nach der Anzeige
    }

    // save hook
    if ($notice = get_transient("save_post_hook_test_{$user_id}")) {
        echo "<div class='notice notice-success is-dismissible'><p>{$notice}</p></div>";
        delete_transient("save_post_hook_test_{$user_id}"); // Lösche den Transient nach der Anzeige
    }
    
    if ($notice = get_transient("save_post_hook_extracted_content_{$user_id}")) {
        echo "<div class='notice notice-success is-dismissible'><p>{$notice}</p></div>";
        delete_transient("save_post_hook_extracted_content_{$user_id}"); // Lösche den Transient nach der Anzeige
    }

    // extract text from blocks
    if ($notice = get_transient("extract_text_from_blocks_fn_{$user_id}")) {
        echo "<div class='notice notice-success is-dismissible'><p>{$notice}</p></div>";
        delete_transient("extract_text_from_blocks_fn_{$user_id}"); // Lösche den Transient nach der Anzeige
    }
    if ($notice = get_transient("extract_text_from_blocks_fn_decoding_error_{$user_id}")) {
        echo "<div class='notice notice-success is-dismissible'><p>{$notice}</p></div>";
        delete_transient("extract_text_from_blocks_fn_decoding_error_{$user_id}"); // Lösche den Transient nach der Anzeige
    }
    if ($notice = get_transient("extract_text_from_blocks_fn_decoded_blocks_{$user_id}")) {
        echo "<div class='notice notice-success is-dismissible'><p>{$notice}</p></div>";
        delete_transient("extract_text_from_blocks_fn_decoded_blocks_{$user_id}"); // Lösche den Transient nach der Anzeige
    }

    // ÄNDERUNG DER VORSCHAU
    if ($notice = get_transient("save_post_hook_from_revision_{$user_id}")) {
        echo "<div class='notice notice-success is-dismissible'><p>{$notice}</p></div>";
        delete_transient("save_post_hook_from_revision_{$user_id}"); // Lösche den Transient nach der Anzeige
    }
    if ($notice = get_transient("debug_post_id_{$user_id}")) {
        echo "<div class='notice notice-success is-dismissible'><p>{$notice}</p></div>";
        delete_transient("debug_post_id_{$user_id}"); // Lösche den Transient nach der Anzeige
    }
    if ($notice = get_transient("save_post_hook_from_revision_page_builder_blocks{$user_id}")) {
        echo "<div class='notice notice-success is-dismissible'><p>{$notice}</p></div>";
        delete_transient("save_post_hook_from_revision_page_builder_blocks{$user_id}"); // Lösche den Transient nach der Anzeige
    }
});






// /** Triing to use evision hook */
// add_filter('wp_save_post_revision', 'grenzlotsen_save_page_builder_meta_to_revision', 10, 2);

// function grenzlotsen_save_page_builder_meta_to_revision($revision_id, $post) {
//     $blocks = get_post_meta($post->ID, '_page_builder_blocks', true);
//     $frontend_settings = get_post_meta($post->ID, '_page_builder_frontend_settings', true);

//     if ($blocks) {
//         update_metadata('post', $revision_id, '_page_builder_blocks', $blocks);
//     }

//     if ($frontend_settings) {
//         update_metadata('post', $revision_id, '_page_builder_frontend_settings', $frontend_settings);
//     }

//     error_log("Custom Page Builder Revision erstellt: $revision_id");
// }



/** TESTING TO SAVE DATA IN DEFAULT POST CONTENT */
// Funktion zum selektiven Speichern von Page Builder Revisionen
/*
error_log('Grenzlotsen Page Builder Revisionen');
add_action('save_post', 'grenzlotsen_save_post_meta_to_revision', 10, 2);

function grenzlotsen_save_post_meta_to_revision($post_id, $post) {
    // Sicherheitschecks: Autosave und Berechtigungen
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) return;
    if (!current_user_can('edit_post', $post_id)) return;

    // Nur auf Beiträge und Seiten anwenden
    if (!in_array($post->post_type, ['post', 'page'])) return;

    // Erzwungenes Speichern der Revision durch Ändern des Inhalts
    remove_action('save_post', 'grenzlotsen_save_post_meta_to_revision'); // Verhindern von Endlosschleifen
    wp_update_post(array(
        'ID' => $post_id,
        'post_content' => $post->post_content . ' '
    ));
    add_action('save_post', 'grenzlotsen_save_post_meta_to_revision');

    // Log Post Type 
    error_log('Post Type: ' . $post->post_type);


    // Holen der aktuellen Meta-Daten
    $current_blocks = get_post_meta($post_id, '_page_builder_blocks', true);
    $current_frontend_settings = get_post_meta($post_id, '_page_builder_frontend_settings', true);

    // Holen der letzten Revision
    $last_revision = wp_get_post_revisions($post_id, ['numberposts' => 1]);
    error_log('Last Revision: ' . print_r($last_revision, true));
    
    if (!empty($last_revision)) {
        $last_revision = array_shift($last_revision);
        // error log 
        error_log('Last Revision after array shift: ' . print_r($last_revision, true));
        $last_blocks = get_post_meta($last_revision->ID, '_page_builder_blocks', true);
        error_log('Last Blocks: ' . print_r($last_blocks, true));
        $last_frontend_settings = get_post_meta($last_revision->ID, '_page_builder_frontend_settings', true);
        error_log('Last Frontend Settings: ' . print_r($last_frontend_settings, true));

        // Prüfen, ob sich die Page Builder Daten geändert haben
        // if ($current_blocks !== $last_blocks || $current_frontend_settings !== $last_frontend_settings) {
            // get slashed blocks and frontend settings
            $slashed_blocks = wp_slash($_POST['page_builder_blocks']);
            error_log("slashed blocks: " . print_r($slashed_blocks, true));
            $slashed_frontend_settings = wp_slash($_POST['page_builder_frontend_settings']);
            error_log("slashed frontend settings: " . print_r($slashed_frontend_settings, true));

            create_page_builder_revision($post_id, $slashed_blocks, $slashed_frontend_settings);
        // }
    } else {
        // get slashed blocks and frontend settings
        $slashed_blocks = wp_slash($_POST['page_builder_blocks']);
        error_log("slashed blocks: " . print_r($slashed_blocks, true));
        $slashed_frontend_settings = wp_slash($_POST['page_builder_frontend_settings']);
        error_log("slashed frontend settings: " . print_r($slashed_frontend_settings, true));
        // Wenn es keine vorherige Revision gibt, erstellen wir eine
        create_page_builder_revision($post_id, $current_blocks, $current_frontend_settings);
    }
}

function create_page_builder_revision($post_id, $blocks, $frontend_settings) {
    $revision_id = wp_save_post_revision($post_id);
    error_log("Revision ID: $revision_id");
    
    if ($revision_id) {
        error_log("there is a revision id: $revision_id");
        update_metadata('post', $revision_id, '_page_builder_blocks', $blocks);
        update_metadata('post', $revision_id, '_page_builder_frontend_settings', $frontend_settings);
        error_log("Neue Page Builder Revision erstellt: $revision_id");
    } else {
        error_log("Konnte keine Revision für Post ID $post_id erstellen");
    }
}

// Funktion zum Wiederherstellen der Page Builder Daten bei einer Revision
add_action('wp_restore_post_revision', 'grenzlotsen_restore_page_builder_revision', 10, 2);


// TODO: Herausfinden, wie ich eine vorschau davon in die Revesionen bekomme um das zu debuggen
function grenzlotsen_restore_page_builder_revision($post_id, $revision_id) {
    $blocks = get_post_meta($revision_id, '_page_builder_blocks', true);
    error_log('Blocks from revision: ' . print_r($blocks, true));
    $frontend_settings = get_post_meta($revision_id, '_page_builder_frontend_settings', true);
    error_log('Frontend Settings from revision: ' . print_r($frontend_settings, true));

    if ($blocks !== false) {
        update_post_meta($post_id, '_page_builder_blocks', $blocks);
    }
    if ($frontend_settings !== false) {
        update_post_meta($post_id, '_page_builder_frontend_settings', $frontend_settings);
    }
}



/// Neu: try to show page builder blocks in revisions ////
add_filter('wp_post_revision_fields', 'grenzlotsen_add_page_builder_fields_to_revisions');

function grenzlotsen_add_page_builder_fields_to_revisions($fields) {
    $fields['_page_builder_blocks'] = 'Page Builder Blocks';
    $fields['_page_builder_frontend_settings'] = 'Page Builder Frontend Settings';
    return $fields;
}

add_filter('wp_get_revision_ui_diff', 'grenzlotsen_revision_ui_diff', 10, 3);

function grenzlotsen_revision_ui_diff($diff, $field, $current_revision, $previous_revision = null) {
    error_log('grenzlotsen_revision_ui_diff called');
    error_log('field: ' . print_r($field, true));  
    
    if ($field == '_page_builder_blocks' || $field == '_page_builder_frontend_settings') {
        error_log('passed in field is ' . $field);
        $current_data = maybe_unserialize($current_revision->$field);
        $previous_data = $previous_revision ? maybe_unserialize($previous_revision->$field) : '';

        // Verwende wp_text_diff für einen Vergleich der Daten
        $diff = wp_text_diff(print_r($previous_data, true), print_r($current_data, true), array('show_split_view' => true));
    } else {
        error_log('if not passed in grenzlotsen_revision_ui_diff');
    }
    return $diff;
}

*/








/***** NOCHMAL NEU  */
/*
error_log('NOCHMAL NEU');
add_action('save_post', 'grenzlotsen_save_post_meta_to_revision');

function grenzlotsen_save_post_meta_to_revision($post_id) {
    // log
    error_log('save_post -> : grenzlotsen_save_post_meta_to_revision($post_id) ' . $post_id);

    // Sicherheitschecks: Autosave und Berechtigungen
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) return;
    if (!current_user_can('edit_post', $post_id)) return;

    // Nur auf Beiträge und Seiten anwenden
    if (get_post_type($post_id) !== 'post' && get_post_type($post_id) !== 'page') {
        return;
    }

    // Holen der Meta-Daten
    $blocks = get_post_meta($post_id, '_page_builder_blocks', true);
    $frontend_settings = get_post_meta($post_id, '_page_builder_frontend_settings', true);

    // Revision erstellen und Meta-Daten speichern
    if ($blocks || $frontend_settings) {
        // Speichern einer neuen Revision und Speichern der Meta-Daten als Teil der Revision
        $revision_id = wp_save_post_revision($post_id);
        error_log('Neue Revision erstellt und ID: ' . $revision_id);

        // Prüfen, ob eine Revision erstellt wurde
        if ($revision_id) {
            // Speichern der Meta-Daten in der Revision
            if ($blocks !== false) {
                update_metadata('post', $revision_id, '_page_builder_blocks', $blocks);
            }
            if ($frontend_settings !== false) {
                update_metadata('post', $revision_id, '_page_builder_frontend_settings', $frontend_settings);
            }
            error_log('Revision erstellt und Meta-Daten gespeichert für Revision ID: ' . $revision_id);
        } else {
            error_log('Konnte keine Revision erstellen.');
        }
    }
}
*/



/*** REVISIONEN NEU 19:57 */
// error_log('Revisionen neu 20:01');
// add_action('save_post', function($post_id) {
//     error_log('save_post Hook Triggered for Post ID: ' . $post_id);
    
//     // Überprüfen, ob der Post eine Revision ist
//     $revision_id = wp_is_post_revision($post_id);

//     // Wenn es eine Revision ist, führe den Rest des Codes aus
//     if ($revision_id) {
//         error_log('save_post Hook Triggered for Revision ID: ' . $revision_id);
//         grenzlotsen_save_page_builder_revision_meta($revision_id);
//     } else {
//         error_log('there is no revision id: ' . $revision_id);
//     }
// });



// function grenzlotsen_save_page_builder_revision_meta($revision_id) {
//     error_log('grenzlotsen_save_page_builder_revision_meta called');
//     error_log('Revision ID: ' . $revision_id);
//     $parent_id = wp_is_post_revision($revision_id);

//     error_log('Parent ID: ' . $parent_id);
    
//     if ($parent_id) {
//         error_log('Parent ID found');
//         // Meta-Daten aus dem Elternpost abrufen
//         $blocks = get_post_meta($parent_id, '_page_builder_blocks', true);
//         $frontend_settings = get_post_meta($parent_id, '_page_builder_frontend_settings', true);

//         // Meta-Daten in der Revision speichern
//         if ($blocks !== false) {
//             error_log('Blocks found');
//             update_metadata('post', $revision_id, '_page_builder_blocks', $blocks);
//         }
//         if ($frontend_settings !== false) {
//             error_log('Frontend settings found');
//             update_metadata('post', $revision_id, '_page_builder_frontend_settings', $frontend_settings);
//         }
//     }
// }





/******** TESTING REVISION MANUELL ********/
// Hook to save post meta in revisions
// error_log('Hook to save post meta in revisions');
// add_action('wp_save_post_revision', 'grenzlotsen_save_page_builder_revision', 10);

// function grenzlotsen_save_page_builder_revision($revision_id) {
//     // Logging the revision ID
//     error_log("grenzlotsen_save_page_builder_revision triggered for revision ID: " . $revision_id);

//     // Check if the post is a revision and get the parent post ID
//     $parent_id = wp_is_post_revision($revision_id);

//     // Logging the parent post ID
//     if ($parent_id) {
//         error_log("Parent post ID: " . $parent_id);
//     } else {
//         error_log("No parent post found for revision ID: " . $revision_id);
//     }

//     // If there is a parent post, retrieve and save the meta data
//     if ($parent_id) {
//         // Attempt to get the page builder blocks meta
//         $blocks = get_post_meta($parent_id, '_page_builder_blocks', true);
//         if ($blocks !== false) {
//             error_log("Found page builder blocks for parent post ID " . $parent_id . ": " . print_r($blocks, true));
//             // Save page builder blocks in revision
//             update_metadata('post', $revision_id, '_page_builder_blocks', $blocks);
//             error_log("Page builder blocks saved in revision ID " . $revision_id);
//         } else {
//             error_log("No page builder blocks found for parent post ID " . $parent_id);
//         }

//         // Attempt to get the frontend settings meta
//         $frontend_settings = get_post_meta($parent_id, '_page_builder_frontend_settings', true);
//         if ($frontend_settings !== false) {
//             error_log("Found frontend settings for parent post ID " . $parent_id . ": " . print_r($frontend_settings, true));
//             // Save frontend settings in revision
//             update_metadata('post', $revision_id, '_page_builder_frontend_settings', $frontend_settings);
//             error_log("Frontend settings saved in revision ID " . $revision_id);
//         } else {
//             error_log("No frontend settings found for parent post ID " . $parent_id);
//         }
//     }
// }







/**
 * Speichern der benutzerdefinierten Meta-Daten als Teil der Revision
 */
// function mein_revision_save_meta_data($post_id) {
//     // Sicherstellen, dass dies kein Autosave oder Revision ist
//     if (wp_is_post_autosave($post_id) || wp_is_post_revision($post_id)) {
//         return;
//     }

//     // Hole die Meta-Daten des Elternposts
//     $blocks = get_post_meta($post_id, '_page_builder_blocks', true);
//     $frontend_settings = get_post_meta($post_id, '_page_builder_frontend_settings', true);

//     // Logge die Meta-Daten für Debugging-Zwecke
//     error_log("Speichern der Meta-Daten für Post $post_id");
//     error_log("Page Builder Blocks: " . print_r($blocks, true));
//     error_log("Frontend Settings: " . print_r($frontend_settings, true));

//     // Prüfen, ob Revisionen für diesen Post existieren
//     $revision_id = wp_save_post_revision($post_id);
    
//     if ($revision_id && (!empty($blocks) || !empty($frontend_settings))) {
//         // Speichern der Meta-Daten in der Revision
//         add_metadata('post', $revision_id, '_page_builder_blocks', wp_slash($blocks), true);
//         add_metadata('post', $revision_id, '_page_builder_frontend_settings', wp_slash($frontend_settings), true);
//     }
// }
// add_action('save_post', 'mein_revision_save_meta_data');



// /**
//  * Zeige Unterschiede in den benutzerdefinierten Meta-Daten in der Revisionsansicht
//  */
// function mein_revision_show_meta_diff($return, $field, $compare_from, $compare_to) {
//     if ($field == '_page_builder_blocks' || $field == '_page_builder_frontend_settings') {
//         // Hole die alten und neuen Werte
//         $old_value = get_metadata('post', $compare_from->ID, $field, true);
//         $new_value = get_metadata('post', $compare_to->ID, $field, true);

//         error_log($old_value . " " . $new_value);

//         // Vergleiche die Werte und gib die Unterschiede aus
//         if ($old_value !== $new_value) {
//             $return = '<strong>Vorher:</strong><br>' . wp_kses_post($old_value) . '<br><strong>Nachher:</strong><br>' . wp_kses_post($new_value);
//         }
//     }
//     return $return;
// }
// add_filter('wp_get_revision_ui_diff', 'mein_revision_show_meta_diff', 10, 4);


// /**
//  * Wiederherstellen der benutzerdefinierten Meta-Daten, wenn eine Revision wiederhergestellt wird
//  */
// function mein_restore_meta_from_revision($post_id, $revision_id) {
//     // Hole die benutzerdefinierten Meta-Daten aus der Revision
//     $blocks = get_metadata('post', $revision_id, '_page_builder_blocks', true);
//     $frontend_settings = get_metadata('post', $revision_id, '_page_builder_frontend_settings', true);

//     // Logge die Meta-Daten für Debugging-Zwecke
//     error_log("Wiederherstellen der Meta-Daten für Post $post_id aus Revision $revision_id");
//     error_log("Page Builder Blocks: " . print_r($blocks, true));
//     error_log("Frontend Settings: " . print_r($frontend_settings, true));

//     // Stelle die Meta-Daten wieder her
//     if (!empty($blocks)) {
//         update_post_meta($post_id, '_page_builder_blocks', wp_slash($blocks));
//     }
//     if (!empty($frontend_settings)) {
//         update_post_meta($post_id, '_page_builder_frontend_settings', wp_slash($frontend_settings));
//     }
// }
// add_action('wp_restore_post_revision', 'mein_restore_meta_from_revision', 10, 2);

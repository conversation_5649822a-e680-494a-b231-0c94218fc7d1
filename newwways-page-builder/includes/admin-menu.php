<?php
add_action('admin_menu', function() {
    add_menu_page('Grenzlotsen Page Builder', 'Page Builder', 'manage_options', 'grenzlotsen-page-builder', 'grenzlotsen_page_builder_admin_page', 'dashicons-layout');
});

/* CodeMirror für die Page Builder Admin Seite einbinden */
add_action('admin_enqueue_scripts', 'enqueue_codemirror_for_page_builder');

function enqueue_codemirror_for_page_builder($hook_suffix) {
    // Nur für die "Grenzlotsen Page Builder" Seite
    if ($hook_suffix === 'toplevel_page_grenzlotsen-page-builder') {
        // CodeMirror CSS und JS einbinden
        wp_enqueue_style('codemirror-css', 'https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.9/codemirror.min.css');
        wp_enqueue_script('codemirror-js', 'https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.9/codemirror.min.js', array(), null, true);
        // Mixed Mode für HTML mit eingebettetem JavaScript und CSS
        wp_enqueue_script('codemirror-mode-htmlmixed', 'https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.9/mode/htmlmixed/htmlmixed.min.js', array(), null, true);
        wp_enqueue_script('codemirror-mode-xml', 'https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.9/mode/xml/xml.min.js', array(), null, true);
        wp_enqueue_script('codemirror-mode-javascript', 'https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.9/mode/javascript/javascript.min.js', array(), null, true);
        wp_enqueue_script('codemirror-mode-css', 'https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.9/mode/css/css.min.js', array(), null, true);
    }
}

function grenzlotsen_page_builder_admin_page() {
    $saved_settings = wp_unslash(get_option('grenzlotsen_page_builder_content'));

    ?>

    <script src="/wp-content/plugins/newwways-page-builder/includes/js/vue/vue.global.js"></script>
    <script src="/wp-content/plugins/newwways-page-builder/includes/js/vue/Sortable.min.js"></script>
    <script src="/wp-content/plugins/newwways-page-builder/includes/js/vue/vuedraggable.umd.js"></script>


    <!-- <form method="post" action="<?php echo esc_url(admin_url('admin-post.php')); ?>"> -->
        <!-- <input type="hidden" name="action" value="save_page_builder_content"> -->

    <!-- <form method="post" action="options.php" id="settings-form"> -->

    <div id="page-builder-app">
        <h1>Zollwärts Settings</h1>

        <!-- Vue -->
        <div id="app">

            <div class="app-inner-wrapper">

                <!-- Left Sidebar -->
                <div class="app-left-sidebar">
                    <!-- <p>Left Sidebar</p> -->
                    <ul>
                        <!-- <li :class="{active: appBodyTab == 'count'}" @click="appBodyTab = 'count'">count for testing</li> -->
                        <!-- <li :class="{active: appBodyTab == 'global'}" @click="appBodyTab = 'global'">global Settings</li> -->
                        <li :class="{active: appBodyTab == 'header'}" @click="appBodyTab = 'header'">Header</li>
                        <!-- <li :class="{active: appBodyTab == 'content'}" @click="appBodyTab = 'content'">Content</li> -->

                        <!-- <li :class="{active: appBodyTab == 'footer'}" @click="appBodyTab = 'footer'">Footer</li> -->
                        <li :class="{active: appBodyTab == 'htmlForms'}" @click="appBodyTab = 'htmlForms'">HTML-Forms</li>
                        <li :class="{active: appBodyTab == 'modals'}" @click="appBodyTab = 'modals'">Global Modals</li>
                    </ul>
                </div>
        

                <!-- Body -->
                <div class="app-body">
                    <!-- <p>Body</p> -->
                    
                    <!-- <div class="app-body-inner" v-if="appBodyTab == 'count'">
                        <p>Hier gibt es nichts zu sehen</p>
                    </div> -->

                    <!-- Gobal Settings -->
                    <div class="app-body-inner" v-if="appBodyTab == 'global'">
                        <p>Global Settings</p>
                        <input type="text" v-model="pageBuilderSettings.global.test">
                        <!-- <p>{{pageBuilderSettings.global.test}}</p> -->

                        <!-- #faf7f9 -->
                        <p>Site background color</p>
                        <input type="text" v-model="pageBuilderSettings.global.backgroundColor">
                        <!-- <p>{{pageBuilderSettings.global.backgroundColor}}</p> -->
                    </div>


                    <div class="app-body-inner" v-if="appBodyTab == 'header'">
                        <p>Header</p>

                        <input type="text" v-model="pageBuilderSettings.header.test">
                        <!-- <p>{{pageBuilderSettings.header.test}}</p> -->

                        <!-- Logo -->
                        <span class="button" @click="openMediaLibrary('header', 'defaultLogo')">Inverted Logo wählen</span>
                        <div v-if="pageBuilderSettings.header.defaultLogo">
                            <img :src="pageBuilderSettings.header.defaultLogo.url" alt="Logo" />
                            <!-- <p>{{pageBuilderSettings.header.defaultLogo}}</p> -->
                        </div>
                        
                        <span class="button" @click="openMediaLibrary('header', 'scrolledLogo')">Dark Logo wählen</span>
                        <div v-if="pageBuilderSettings.header.scrolledLogo">
                            <img :src="pageBuilderSettings.header.scrolledLogo.url" alt="Logo" />
                            <!-- <p>{{pageBuilderSettings.header.scrolledLogo}}</p> -->
                        </div>
                    
                    </div>


                    <!-- Content -->
                    <div class="app-body-inner" v-if="appBodyTab == 'content'">
                        <p>Content</p>
                    </div>


                    <!-- Footer -->
                    <div class="app-body-inner" v-if="appBodyTab == 'footer'">
                        <p>Footer</p>
                    </div>


                    <!-- Html Forms -->
                    <div class="app-body-inner" v-if="appBodyTab == 'htmlForms'">
                        <p>Html Forms</p>

                        <!-- <span class="nw-button" @click="createFormsArray()">create Forms</span> -->

                        <div v-if="pageBuilderSettings.htmlForms">
                            <span class="nw-button" @click="createNewForm()">create new form</span>

                            <div v-for="(form, index) in pageBuilderSettings.htmlForms" :key="index">
                                <span class="nw-button" style="padding: 10px; color: #fff; background: #000;" @click="deleteForm(form.id)">X</span>
                                <input type="text" v-model="form.title">
                                <textarea v-model="form.description"></textarea>
                                
                                <textarea 
                                    v-model="form.html"
                                    ref="textarea"
                                    @input="textareaAutoResize"
                                    :style="{ height: textareaHeight + 'px' }"
                                    :id="'code-editor-' + index"
                                    name="code-editor" 
                                    rows="10"
                                ></textarea>
                                <p>{{form.id}}</p>
                                <hr>
                            </div>
                        </div>
                    </div>
                    
                    
                    <!-- Global Modals -->
                    <div class="app-body-inner global-modals" v-if="appBodyTab == 'modals'">
                        <p>Gobal Modals</p>

                        <!-- <span class="nw-button" @click="createFormsArray()">create Forms</span> -->


                            <span class="nw-button" @click="createNewModal()">Create new Modal</span>

                            <div v-for="modal in pageBuilderSettings.modals" class="instance-wrapper">

                                <div class="instance-header">
                                    <h3>{{modal.title}}</h3>
                                    <div class="actions">
                                        <span class="nw-button" style="padding: 10px; color: #fff; background: #000;" @click="copyToClipboard(modal.id)">{{modal.id}}</span>
                                        <span class="nw-button" style="padding: 10px; color: #fff; background: #000;" @click="toggleModal(modal.id)">toggle</span>
                                        <span class="nw-button" style="padding: 10px; color: #fff; background: #000;" @click="deleteModal(modal.id)">X</span>
                                    </div>
                                </div>

                                <div class="inner-wrapper" :class="modal.isOpenInUi ? 'expanded' : 'collapsed' ">
                                    <div class="content-wrapper">
                                        <input type="text" v-model="modal.title">
                                        <textarea v-model="modal.description"></textarea>

                                        <div v-for="element in modal.content">
                                            <div v-if="element.type === 'form'" class="form-wrapper">
                                                <div class="form-header" style="display: flex; justify-content: space-between;">
                                                    <h2>Formular</h2>
                                                    <span class="nw-button" style="padding: 10px; color: #fff; background: #000;" @click="removeElementFromModal(modal, element.referenceId)">X</span>
                                                </div>

                                                <h3>{{getReferenceObject('form', element.referenceId).title}}</h3>
                                                <p>{{getReferenceObject('form', element.referenceId).description}}</p>
                                                <p>UID: {{element.referenceId}}</p>
                                                <!-- {{element}} -->
                                            </div>
                                            
                                        </div>

                                        <!-- Add Form to Modal -->
                                        <button @click="selectFormForModal = true" class="button">Add HTML-Content to modal</button>
                                        <div v-if="selectFormForModal">
                                            <ul>
                                                <li v-for="form in pageBuilderSettings.htmlForms">
                                                    <span v-if="!modal.content.find(element => element.type === 'form' && element.referenceId === form.id)" @click="addElementToModal(modal, 'form', form)" class="button">{{form.title}}</span>
                                                </li>
                                            </ul>
                                        </div>

                                        <!-- <p>{{modal}}</p> -->
                                    </div>
                                </div>
                            </div>
                            
                    </div>
                </div>
            </div>
            <!-- End app-inner-wrapper -->
            
            <!-- TODO: implement UI Feedback for saving data -->
            <button @click="takeJSON()" style="position: relative; margin: 100px 0; padding: 10px 20px;">Save Speichern</button>
            
            <!-- fake input to save JSON in Wordpress -->
            <!-- <input type="text" id="page-builder-settings" name="page-builder-settings" :value="pageBuilderSettings" style="width: 100%;"/> -->
            <input type="text" id="page-builder-settings" name="page-builder-settings" style="width: 100%; position: relative;"/>

            <!-- <pre>
                {{ pageBuilderSettings }}
            </pre> -->
        </div>
        <!-- end #app -->
            

        <script type="module">
        const { createApp, ref, onMounted, computed, watch, nextTick } = Vue

        // import ParagraphComponent from '/wp-content/plugins/newwways-page-builder/components/ParagraphComponent.js';

        const app = createApp({
            // components: {
            //     'paragraph-component': ParagraphComponent,
            // },

            setup() {


                /**
                 * Real vue logic
                 */ 

                 // Utilities
                const generateUniqueId = () => {
                    let randomString = Math.random().toString(36).substr(2, 9);
                    return `nw-${randomString}`;
                }

                const copyToClipboard = (text) => {
                    navigator.clipboard.writeText(text);
                }

                function addKeyValuePair(key, value) {
                    pageBuilderSettings.value[key] = value;
                    console.log('pageBuilderSettings.value: ', pageBuilderSettings.value)
                }


                // UI Values 
                const appBodyTab = ref('count'); // Active Tab

                // Textarea auto height 
                const textareaHeight = ref(32); // Start height of the textarea
                const textarea = ref(null);

                const textareaAutoResize = (event) => {
                    console.log('event: ', event)
                    console.log('event.target: ', event?.target)

                    const el = event?.target;
                    console.log('el: ', el);
                    
                    if (el) {
                        console.log('if true')
                        // el.style.height = 'auto'; // Reset the height to auto to calculate the scrollHeight
                        const newHeight = el.scrollHeight;
                        const oldHeight = textareaHeight.value >= el.style.height ? textareaHeight.value : el.style.height;
                        console.log('oldHeight: ', oldHeight, 'newHeight: ', newHeight);
                        // textareaHeight.value = oldHeight < newHeight ? newHeight > 500 ? 500 : newHeight : oldHeight;
                        textareaHeight.value = newHeight > 500 ? 500 : newHeight;
                    }
                };

                // Data 
                const emptyHtmlForm = {
                    title: '',
                    description: '',
                    type: 'raw-html',
                    code: '',
                    id: 'here should be a unique id',
                }
                const createNewForm = () => {
                    pageBuilderSettings.value.htmlForms.push({...emptyHtmlForm, id: generateUniqueId()});
                }
                const deleteForm = (uid) => {
                    console.log('uid: ', uid);
                    pageBuilderSettings.value.htmlForms = pageBuilderSettings.value.htmlForms.filter((form) => form.id !== uid);
                }

                // Modals
                // const addModalSectioninJSON = () => {
                //     // defaultPageBuilderSeetings.modals = []; // TODO: just for development purposes
                //     console.log('defaultPageBuilderSeetings: ', defaultPageBuilderSeetings);
                // }
                const selectFormForModal = ref(false);
                const createNewModal = () => {
                    // TODO: add "isOpenInUi" property to each array in page settings and remove it bevore saving or set it to false before saving
                    pageBuilderSettings.value.modals.push({title: '', description: '', content: [], isOpenInUi: false, id: generateUniqueId()}); 
                }
                const toggleModal = (uid) => {
                    console.log('uid: ', uid);
                    pageBuilderSettings.value.modals.map((modal) => {
                        if (modal.id === uid) {
                            modal.isOpenInUi = !modal.isOpenInUi;
                        }
                    })
                }
                const deleteModal = (uid) => {
                    console.log('uid: ', uid);
                    pageBuilderSettings.value.modals = pageBuilderSettings.value.modals.filter((modal) => modal.id !== uid);
                }
                const addElementToModal = (modal, type, element) => {
                    console.log('element: ', element);
                    if(type === 'form') {
                        modal.content.push({ type: type, referenceId: element.id, id: generateUniqueId()});
                    }

                    if(type === 'html') {
                        modal.content.push({ type: type, referenceId: element.id, id: generateUniqueId()});
                    }
                }

                const removeElementFromModal = (modal, referenceId) => {
                    console.log('modal: ', modal);
                    console.log('referenceId: ', referenceId);
                    modal.content = modal.content.filter((element) => element.referenceId !== referenceId); // TODO: make sure echt modal element has an referenceId
                }

                // Get Information from reference 
                const getReferenceObject = (type, uid) => {
                    console.log('type: ', type);
                    console.log('uid: ', uid);

                    // find form
                    const matchingForm = pageBuilderSettings.value.htmlForms.find((form) => form.id === uid);
                    console.log('matchingForm: ', matchingForm);
                    return matchingForm;
 
                }

                const defaultPageBuilderSeetings = {
                    global: {
                        test: 'empty',
                    }, 
                    header: {
                        test: 'empty',
                        defaultLogo: '',
                        scrolledLogo: '',
                    },
                    content: {
                        test: 'empty',
                    },
                    htmlForms: [],
                    modals: [],
                }

                const loadedPageBuilderSettings = JSON.parse(<?php echo json_encode($saved_settings); ?>);

                console.log('loadedPageBuilderSettings: ', loadedPageBuilderSettings)

                const pageBuilderSettings = ref(loadedPageBuilderSettings ? loadedPageBuilderSettings : defaultPageBuilderSeetings);

                // const editor = document.querySelector('.wp-editor-area');
                // const updateInputForSave = computed(() => {
                //     hier computed value
                // })

                const openMediaLibrary = (block, assetKey) => {
                    console.log('openMediaLibrary called');
                    console.log('pageBuilderSettings:', pageBuilderSettings);
                    console.log('block – assetKey:', block, assetKey);

                    let frame = wp.media({
                        title: 'Bild auswählen',
                        button: {
                            text: 'Bild verwenden'
                        },
                        multiple: false
                    });

                    frame.on('select', () => {
                        const attachment = frame.state().get('selection').first().toJSON();
                        console.log('!index block[assetKey]:', pageBuilderSettings.value[block][assetKey]);
                        pageBuilderSettings.value[block][assetKey] = attachment;
                    });

                    frame.open();
                };


                /**
                 * 
                 * Just save Site Settings (JSON) in input
                 */
                function takeJSON() {
                    console.log('takeJSON called!!!!!')
                    const input = document.getElementById('page-builder-settings');
                    input.value = JSON.stringify(pageBuilderSettings.value);
                    saveContent();
                }

                function saveContent() {
                    // var content = document.getElementById('editor').innerHTML;
                    var content = document.getElementById('page-builder-settings').value;
                    // var jsonContent = JSON.stringify(content);
                    var xhr = new XMLHttpRequest();
                    // var ajaxurl = '<?php echo admin_url('admin-ajax.php'); ?>';
                    xhr.open('POST', ajaxurl, true);

                    xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded; charset=UTF-8');
                    xhr.onreadystatechange = function() {
                        if (xhr.readyState == 4 && xhr.status == 200) {
                            console.log('Gespeicherter Inhalt:', content);
                        }
                    };
                    xhr.send('action=save_page_builder_content&content=' + encodeURIComponent(content));
                    
                    // xhr.setRequestHeader('Content-Type', 'application/json; charset=UTF-8');
                    // xhr.onreadystatechange = function() {
                    //     if (xhr.readyState == 4) {
                    //         if (xhr.status == 200) {
                    //             console.log('Gespeicherter Inhalt:', content);
                    //         } else {
                    //             console.error('Fehler beim Speichern des Inhalts:', xhr.status);
                    //         }
                    //     }
                    // };
                    // var data = {
                    //     action: 'save_page_builder_content',
                    //     content: content
                    // };
                    // xhr.send(JSON.stringify(data));
                }


                // TODO: Just for development 
                // const createFormsArray = () => {
                //     pageBuilderSettings.value.htmlForms = [];
                // }



                // Initialize Code Mirror
                const initializeCodeMirror = () => {
                    nextTick(() => {
                        pageBuilderSettings.value.htmlForms.forEach((form, index) => {
                        const textarea = document.getElementById(`code-editor-${index}`);
                        if (textarea && !textarea.codeMirrorInstance) {
                            const editor = CodeMirror.fromTextArea(textarea, {
                                lineNumbers: true,
                                mode: 'htmlmixed', // 'text/html',
                                theme: 'default',
                                matchBrackets: true,
                                autoCloseTags: true,
                                lineWrapping: true,
                            });

                            // Editor-Höhe manuell anpassen, indem der Container resizable gemacht wird
                            const editorWrapper = editor.getWrapperElement();
                            editorWrapper.style.resize = 'vertical';
                            editorWrapper.style.minHeight = '200px'; // Mindesthöhe
                            editorWrapper.style.maxHeight = '800px'; // Maximale Höhe

                            textarea.codeMirrorInstance = editor; // CodeMirror-Instanz speichern, um mehrfaches Initialisieren zu verhindern
                                editor.on('change', () => {
                                form.html = editor.getValue(); // Den HTML-Wert zurück ins form-Objekt schreiben
                            });
                        }
                        });
                    });
                };

                watch(appBodyTab, (newValue) => {
                    if (newValue === 'htmlForms') {
                        initializeCodeMirror();
                    }
                });

                onMounted(() => {
                    if (appBodyTab.value === 'htmlForms') {
                        initializeCodeMirror();
                    }
                });

                return {
                    // utilities 
                    copyToClipboard, 
                    addKeyValuePair, // TODO: just for development purposes
                    getReferenceObject, 

                    // UI
                    appBodyTab,
                    // auto textarea height 
                    textarea,
                    textareaHeight,
                    textareaAutoResize,

                    // Data
                    pageBuilderSettings,

                    // Data functions 
                    openMediaLibrary,
                    // forms
                    createNewForm,
                    deleteForm,
                    // modals
                    // addModalSectioninJSON, // TODO: just for development purposes
                    createNewModal,
                    toggleModal, // TODO: make just one function to toggle each Object in an array – no matter wich type of PageSettings it is
                    deleteModal,
                    addElementToModal,
                    selectFormForModal,

                    removeElementFromModal,


                    // TODO: just for development 
                    // createFormsArray,
                    


                    // save 
                    takeJSON,
                    saveContent,


                }
            }
        })
        app.component('draggable', vuedraggable);
        app.mount('#app')

        </script>


        <style>
            body {
                overflow-x: hidden;
            }
            :root, html *, body * {
                box-sizing: border-box;
            }

            .app-inner-wrapper {
                display: flex;
                padding: 0 0 20px 0;
            }
            .app-left-sidebar {
                width: 200px;
                min-width: 200px;
                /* background-color: #eee; */
                padding: 0px 10px 0px 0px;
            }
            .app-left-sidebar ul {
                width: 100%;
                list-style: none;
                padding: 0;
                margin: 0;
            }
            .app-left-sidebar ul li {
                width: 100%;
                padding: 10px;
                border-radius: 5px;
            }
            .app-left-sidebar ul li:hover {
                background-color: #eee;
            }
            .app-left-sidebar ul li.active {
                background-color: #eee;
            }

            .app-body {
                padding: 0 20px;
                margin-right: 20px;
                background-color: #eee;
                flex-grow: 1;
            }

            .app-body img {
                max-width: 100%;
            }

            .app-body input[type="text"], 
            .app-body textarea {
                width: 100%;
                margin: 10px 0 20px 0px;
            }


            /* ELEMENTS */ 
            .instance-wrapper {
                border: solid 2px #3387a0;
                padding: 0;
                border-radius: 6px;
                overflow: hidden;
                background: white;
                margin-bottom: 2rem;
            }

            .instance-wrapper .instance-header {
                padding: 0 1rem;
                background: #3387a0;
                color: white;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }
            .instance-wrapper .instance-header h3 {
                color: #fff;
            }
            .instance-wrapper .inner-wrapper {
                height: 0;
                overflow: hidden;
            }
            .instance-wrapper .inner-wrapper.expanded {
                height: auto;
            }
            .instance-wrapper .inner-wrapper .content-wrapper {
                padding: 1rem;
            }



            /* 
             * Global Modals
             */
            .global-modals .form-wrapper {
                border: solid 2px #3387a0;
                border-radius: 1rem; 
                padding: 1rem;
            }


        </style>

    </div>
    
    <?php
}

add_action('wp_ajax_save_page_builder_content', 'save_page_builder_content');


/**
 * FUnktionierte aber ohne JSOn FOmatting
 */
function save_page_builder_content() {
    if (isset($_POST['content']) && current_user_can('manage_options')) {
        // $content = wp_slash($_POST['content']);
        $content = $_POST['content'];
        update_option('grenzlotsen_page_builder_content', $content);
        wp_send_json_success();
    } else {
        error_log('Fehler: Ungültige Daten oder fehlende Berechtigungen');
        wp_send_json_error();
    }
}

?>

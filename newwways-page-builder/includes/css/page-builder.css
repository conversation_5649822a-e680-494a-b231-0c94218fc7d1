body {
	/* background: #f00; */
	/* background-image: linear-gradient(to right, #00547b 0, #bf1692 100%); */
	background: rgba(0, 0, 0, 0) !important;
	--acent-color: hsl(315.98deg 79.34% 41.76%);
	--acent-color-inactive: hsl(316deg 40% 42% / 75%);

	--gray-20: #f3f6fa;
	--gray-40: #606081;

	--acent-color-secondary: hsl(238.97deg 64.44% 17.65%);
	--acent-color-secondary-light: hsl(238.97deg 27.42% 29.64%);
	/* --acent-color-secondary-inactive: hsl(316deg 40% 42% / 75%); */

	--effect-transition-timing-function: cubic-bezier(0.645, 0.045, 0.355, 1);
}

/** UTILITIES */ 
.flex-center {
	display: flex;
    justify-content: center;
    align-items: center;
}
.absolute {
	position: absolute;
}

textarea {
	display: block;
}

#page-builder-app {
	padding: 0;
	background-color: none;
	border: none;
}

/* block-content */

.text-image .block-content {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 30px;
}

.text-image .block-content .col {
	flex-grow: 1;
}

.text-image .block-content .col-1.reverseOrder {
	order: 2;
}
.text-image .block-content .col-2.reverseOrder {
	order: 1;
}

/* Toggle switches */
/* The switch - the box around the slider */
.toggle-switch {
	font-size: 12px;
	position: relative;
	display: inline-block;
	width: 3.5em;
	height: 2em;
}

/* Hide default HTML checkbox */
.toggle-switch input {
	opacity: 0;
	width: 0;
	height: 0;
}

/* The slider */
.toggle-switch .slider {
	position: absolute;
	cursor: pointer;
	inset: 0;
	background: #d4acfb;
	background: var(--acent-color-inactive);
	border-radius: 50px;
	transition: all 0.4s cubic-bezier(0.23, 1, 0.32, 1);
}

.toggle-switch .slider:before {
	position: absolute;
	content: "";
	height: 1.4em;
	width: 1.4em;
	left: 0.3em;
	bottom: 0.3em;
	background-color: white;
	border-radius: 50px;
	box-shadow: 0 0px 20px rgba(0, 0, 0, 0.4);
	transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.toggle-switch input:checked + .slider {
	background: #b84fce;
	background: var(--acent-color);
}

.toggle-switch input:focus + .slider {
	box-shadow: 0 0 1px #b84fce;
}

.toggle-switch input:checked + .slider:before {
	transform: translateX(1.6em);
	width: 2em;
	height: 2em;
	bottom: 0;
}

/**
 * Elements in rows 
 */

/* Primary elements in "text-image-module" */
ul.element-wrapper li {
	background: rgb(0 0 0 / 20%);
	margin-top: 1rem;
	padding: 1rem;
	border-radius: 6px;
}

/**
 * Style from "newwways-page-builder.php"
 */
body {
	--content-width: 1400px;
	--default-block-padding: 4rem;
	--content-padding: 4rem;
	--accent-color: #bf1692;
	--accent-color-secondary: #11114a;
	--accent-color-tertiary: #606081;
	--light-gray: #afafc7;
	--super-light-gray: #eeeef4;
	--effect-transition-timing-function: cubic-bezier(0.645, 0.045, 0.355, 1);

	/* Default WP color */
	--wp--color--dark: #1d2327;
	--wp--color-medium-gray: #4f5a62;
}
body * {
	box-sizing: border-box;
}

.fullscreen {
	position: fixed;
	top: 32px;
	left: 0;
	z-index: 9999;
	height: 100vh;
	width: 100vw;
	overflow-y: scroll;
}

/** Page Builder Header */

.page-builder-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 1rem;
}

.element-header-wrapper {
	background: var(--acent-color-secondary);
	background: var(--wp--color--dark);
}

.element-header-wrapper .element-header-row h2 {
	font-size: 1.5rem !important;
	font-weight: 600;
	color: #fff;
	padding: 0 !important;
}

.element-header-row {
	display: flex;
	justify-content: space-between;
}

.element-header-row:first-child {
	padding-bottom: 1rem;
}

.element-nav {
	display: flex;
	/* justify-content: space-between; */
}

.actions.flex {
	display: flex;
	justify-content: flex-end;
}

.btn:first-child {
	margin-left: 0;
}
.btn {
	display: flex;
	justify-content: center;
	cursor: pointer;
	align-items: center;
	margin-left: 10px;
	padding: 6px 10px;
	background: rgb(204, 204, 204);
	/* background: rgb(255, 255, 255);  */
	/* background: var(--acent-color-secondary); */
	background: var(--acent-color-secondary-light);
	background: var(--wp--color-medium-gray);
	color: #fff;
	border-radius: 6px;
	/* border: solid 1px var(--acent-color-secondary-light); */
	transition: all 0.3s var(--effect-transition-timing-function);
}

.btn:hover,
.btn.active {
	/* color: #fff; */
	color: var(--acent-color-secondary);

	/* background: var(--acent-color); */
	/* background: var(--super-light-gray); */
	background: var(--gray-20);
}

.btn.bottom-aligned {
	border-radius: 6px 6px 0 0;
}

.content-wrapper,
.appearance-wrapper,
.advanced-settings-wrapper {
	/* background: var(--super-light-gray); */
	background: var(--gray-20);
	heigth: auto;
	/* max-heigth: 5000px; */
	transition: all 0.5s var(--effect-transition-timing-function);
}

.content-wrapper .btn {
	border: solid 1px var(--acent-color-secondary);
	margin: 0;
}

/* DND Modules */
body .draggable-elements-wrapper h2 {
	padding: 0 !important;
	font-size: 20px !important;
}

body .draggable-elements-wrapper video {
	width: 100%;
	aspect-ratio: 16 / 9;
	background: #000;
}
body .draggable-elements-wrapper .hero video {
	max-width: 100% !important;
    max-height: unset !important;
}

#page-builder-app input {
	display: block;
}
#page-builder-app input[type="text"],
#page-builder-app input[type="number"],
#page-builder-app textarea {
	width: 100%;
}

.p-4 {
	padding: 1rem;
}

.py-4 {
	padding-top: 1rem;
	padding-bottom: 1rem;
}

.pb-0 {
	padding-bottom: 0;
}

.dnd {
	/* padding: 12px;  */
	margin-bottom: 12px;
	/* border: solid #333 2px;  */
	/* border-radius: 12px; */
	background-color: #fff;
	/* background-color: var(--super-light-gray); */
	background-color: var(--gray-20);
	border: solid 1px var(--acent-color-secondary);
	/* background-color: #F4F5F8; */
	border-radius: 6px;
	overflow: hidden;

	transition: all 0.3s var(--effect-transition-timing-function);
}

.empty-dashed {
	border: 2px solid #333;
	opacity: 0.66;
	/* background-color: #eee; */
}
.ghost {
	opacity: 0.8;
	border: 2px dashed #333;
	background: transparent;
}

.empty-dashed * {
	opacity: 1;
}
.ghost * {
	opacity: 0;
}

/** 
	* CSS Reset 
	*/
ul {
	padding: 0;
	margin: 0;
}

/** 
	* Utility classes 
	*/
.grid {
	display: grid;
	gap: 1rem;
	margin: 1rem 0;
}
.grid.col-2 {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 30px;
}

/* TODO: Be carefull with this – this dont show the whole line in textareas */
.grid-item {
	max-width: 100%;
	overflow: hidden;
}

/**
	* Feature Grid 
	*/
.feature-grid .grid-item {
	max-width: 100%;
	overflow: hidden;
	border: solid 1px var(--acent-color-secondary);
	border-radius: 5px;
	/* padding: 12px; */
	background: #fff;
}

.feature-grid .grid-item-header {
	background: var(--acent-color-secondary);
	display: flex;
	justify-content: end;
	margin-bottom: 1rem;
}

.feature-grid .grid-item-header button {
	background: none;
	color: #fff;
	border: none;
	padding: 0.5rem;
	cursor: pointer;
	margin-left: 0.25rem;
}

.feature-grid .grid-item-content {
	padding: 0.5rem;
}

.feature-grid .grid-item-content textarea,
.feature-grid .grid-item-content > div,
.feature-grid .grid-item-content .form-select select {
	width: 100%;
}

/** stciky text-image sections */
.sticky-text-image .block-content .grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 30px;
	border-top: 2px solid #333;
	padding: 20px 0;
}

.sticky-text-image .block-content .grid:last-child {
	border-bottom: 2px solid #333;
}

/**
	* primär für imageGrid / image-grid
*/
.image-grid .grid-item {
	background: rgb(17 17 74 / 20%);
	/* border: solid 1px #11114a; */
	border-radius: 6px;
	/* aspect-ratio: 16 / 9; */
	min-height: 240px;
	/* height: 56%; */
	width: 100%;
	display: flex;

	background: rgb(0 0 0 / 66%);
	width: 100%;
	height: 100%;
	z-index: 9;
	align-items: center;
	justify-content: center;
	position: relative;
}

.grid-item .new-item {
	color: #fff;
}

.grid-item .button-wrapper {
	display: flex;
	position: absolute;
	flex-direction: column;
	color: #fff;
	width: 100%;
	height: 100%;
	z-index: 9;
	align-items: center;
	justify-content: center;

	opacity: 0;
	transition: all 0.3s ease-in-out;
}
.image-grid .grid-item:hover .button-wrapper,
.image-grid .grid-item.show-add-image .button-wrapper {
	opacity: 1;
	background: rgb(0 0 0 / 66%);
}

.inner-button-wrapper {
	display: flex;
	flex-direction: column;
}

.button-wrapper button {
	cursor: pointer;
	background: #f4f5f8;
	border: solid 2px #f4f5f8;
	border-radius: 4px;
	padding: 0.5rem 1rem;
	margin: 0.5rem;
	transform: translate(0, 50px);
	opacity: 0;
	transition: all 300ms ease;
}
.image-grid .grid-item:hover .button-wrapper button,
.image-grid .grid-item.show-add-image .button-wrapper button {
	opacity: 1;
	transform: translate(0, 0px);
}
.button-wrapper button:hover {
	color: #fff;
	background: #bf1692;
	border: solid 2px #bf1692;
}

.grid-item-content {
	width: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	flex-direction: column;
}
.grid-item-content .image-wrapper {
	width: 100%;
}

/* Hero Video Preview */
.hero .grid-item .grid-item-inner-wrapper {
	position: relative;
}
.hero .grid-item .grid-item-inner-wrapper:hover .button-wrapper,
.hero .grid-item.show-add-image .button-wrapper {
	opacity: 1;
	background: rgb(0 0 0 / 66%);
}
.hero .grid-item .grid-item-inner-wrapper:hover .button-wrapper button,
.hero .grid-item.show-add-image .button-wrapper button {
	opacity: 1;
	transform: translate(0, 0px);
}

.play-btn-wrapper {
	position: absolute;
	border: 3px solid rgb(255, 255, 255);
	border-radius: 50%;
	width: 80px;
	height: 80px;
	display: flex;
	justify-content: center;
	align-items: center;
}
.play-btn-wrapper svg {
	margin-left: 9%;
}

.hero .grid-item-content .image-wrapper img {
	width: 100%;
}

.grid-item img {
	/* width: 100%; */
	/* min-width: 100%; */
	margin-bottom: -4px;
}
.grid-icon-preview-wrapper {
	display: flex;
	justify-content: center;
}

/** Hero */
.hero .secondary-tab {
    display: flex;
    padding: 0.5rem;
    background: var(--gray-40);
    border-radius: 0.75rem;
    gap: 0.5rem;
}
.hero ul.secondary-tab li {
    padding: 0.5rem 1rem;
    margin: 0;
    cursor: pointer;
}
.hero ul.secondary-tab li.active {
    background: #fff;
    border-radius: 0.25rem;
}
.hero ul.secondary-tab li.active p {
    color: var(--gray-40);
}

.hero ul.secondary-tab li p {
    padding: 0;
    margin: 0;
    color: #fff;
}


.hero .grid-item {
	height: auto !important;
	/* aspect-ratio: 1 / 1; */
	max-width: 240px;
	margin-bottom: 18px;
	position: relative;
	overflow: visible;
}

.bubble-data {
	display: grid;
	grid-template-columns: 1fr 1fr;
	border: solid 1px;
	border-radius: 5px;
	padding: 1rem;
}

/** Feature Grid */
.feature-grid .grid-item img {
	max-width: 100%;
}

/** Image Grid */
.image-grid .grid-item img {
	min-width: 100%;
}

/**
	* Flexible Content Block 
	*/

/* Columns buttons */
.flexible-content ul.grid-button-wrapper {
	display: flex;
	display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
	gap: 0.5rem;
}
.flexible-content ul.grid-button-wrapper li {
	list-style: none;
	padding: 10px 20px;
	border-radius: 12px;
	cursor: pointer;
	background: #fafafa;
	/* margin-right: 1rem; */
	transition: all 240ms ease;
}
.flexible-content ul.grid-button-wrapper li.active,
.flexible-content ul.grid-button-wrapper li:hover {
	background: var(--acent-color);
	color: #fff;
}

.flexible-content .content-grid-wrapper {
	/* padding: 1rem 1rem; */
}
.flexible-content .content-grid {
	/* display: grid;
	gap: 1rem;
	padding: 1rem 0;
	max-width: 100%;
	overflow: auto; */
	display: grid;

	/* gap: 1rem; */
	max-width: calc(100% + 2rem);
	overflow: auto;
	/* margin-left: -1rem; */
	/* margin-right: -1rem; */

	/* border: solid 1px var(--wp--color--dark); */
	background: var(--wp--color-medium-gray);
	border-radius: 5px;
}

/** 
 * Sortable status 
 */
.content-grid .grid-col {
	transition: transform 240ms ease-in;
}
.content-grid .sortable-ghost {
	opacity: 0;
	transform: scale(0.99);
	/* background: #ff0 !important; */
}
.content-grid .chosen {
	transform: scale(0.99);
}
/* .content-grid .dagging {
	transform: scale(1);
	background: #73b740;
} */
.content-grid .choosen.sortable-ghost {
	background: #f00 !important;
}

.flexible-content .content-grid div.grid-col {
	/* border: solid 1px var(--acent-color-secondary); */
	border: solid 1px var(--wp--color--dark);
	border-right: none;
	/* padding: 1rem; */
	/* border-radius: 8px; */
	background: #fafafa;
}

.flexible-content .content-grid div.grid-col .grid-col-header {
	padding: 0.5rem 1rem;
	max-height: 3rem;
}

.flexible-content .content-grid div.grid-col .grid-content {
	padding: 1rem;
	padding-bottom: 0;

	height: calc(100% - 6rem);
	position: relative;
}



.flexible-content .content-grid div.grid-col ul {
	/* display: flex; */
	/* align-items: center; */
	/* height: calc(100% - 2rem); */
	width: 100%;
	box-sizing: border-box;
	height: 100%;
	position: relative;

}
.flexible-content .content-grid div.grid-col ul > div {
	width: 100%;
	height: 100%;
}

.flexible-content .content-grid div.grid-col:first-child {
	border-radius: 5px 0 0 5px !important;
}
.flexible-content .content-grid div.grid-col:last-child {
	border-right: solid 1px var(--acent-color-secondary);
	border-radius: 0 5px 5px 0;
}

.content-item-header.flex {
	display: flex;
	justify-content: space-between;
}

/*** Col Footer ***/
.add-content-item-wrapper {
	width: 100%;
	/* background: var(--wp--color--dark); */
	display: flex;
	justify-content: center;
	padding: 0;
	padding-bottom: 1rem;
}

/** Single content item */
.flexible-content-item-wrapper {
	margin: 1rem 0;
}
li.flexible-content-item-wrapper:first-child {
	margin-top: 0;
}

/* Content Item preview box */
.content-item-preview-wrapper {
	border: solid 1px var(--acent-color-secondary-light);

	background: #fff;
	/* background: var(--super-light-gray); */
	background: var(--gray-20);

	border-radius: 6px;

	position: relative;
	overflow: hidden;

	/* display: flex; */
	/* justify-content: space-between; */
}
.content-item-preview-wrapper .content-item-header {
	display: flex;
	align-items: center;
	justify-content: space-between;

	padding: 0 0 0 1rem;
}

.content-item-preview-wrapper .content-item-preview-body {
	padding: 0;
}

.content-item-preview-body ol,
.content-item-preview-body ul {
	padding-left: 0px;
	margin-left: 20px;
}

.content-item-preview-body ul li {
	list-style: disc;
}

.content-item-preview-body ul.check_bullist li {
	list-style: none;
	position: relative;
}

.content-item-preview-body ul.check_bullist li::before {
	content: "";
	position: absolute;
	left: -20px;
	top: 10px;
	transform: translateY(-50%);
	width: 14px;
	height: 15px;
	background-size: contain;
	background-repeat: no-repeat;
	background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><circle cx="10" cy="10" r="10" fill="red"/></svg>'); /* Dein SVG-Code hier */
	background-image: url("data:image/svg+xml;utf8,%3Csvg%20width%3D%2220px%22%20height%3D%2220px%22%20viewBox%3D%220%200%2020%2020%22%20version%3D%221.1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%3E%3Ctitle%3Eicons%2Fchecker%3C%2Ftitle%3E%3Cg%20id%3D%22wissen---blog%22%20stroke%3D%22none%22%20stroke-width%3D%221%22%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20id%3D%22blog-detail%22%20transform%3D%22translate(-294%2C%20-2959)%22%20fill%3D%22%23BF1692%22%20fill-rule%3D%22nonzero%22%3E%3Cg%20id%3D%22section%22%20transform%3D%22translate(294%2C%20226)%22%3E%3Cg%20id%3D%22item%22%20transform%3D%22translate(0%2C%201912)%22%3E%3Cg%20id%3D%22icons%2Fchecker%22%20transform%3D%22translate(0%2C%20823.1875)%22%3E%3Cpolygon%20points%3D%220%208.52273215%202.85714205%205.68183038%207.14285647%209.94319646%2017.1428577%200%2020%202.84090177%207.14285647%2015.625%22%3E%3C%2Fpolygon%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}

.content-item-preview-wrapper .content-item-preview-body .preview-info {
	padding: 1rem;
}


/**
 * Text Editor 
 */
.text-editor .lightbox-body {
    padding: 1rem 1rem 0rem 1rem;
}
.text-editor .lightbox-footer {
    padding: 0rem 1rem 1rem 1rem;
}

/** 
 * Image Item 
 */
.image-editor .content-item-preview-body ul {
	margin-left: 0;
	padding-left: 0;
}
.image-editor .content-item-preview-body ul li {
	list-style: none;
}

/*** Image ***/
.image-controls p:first-child {
	margin-top: 0;
}
.image-controls p {
	margin-top: 1rem;
}

/** Image Preview Wrapper */ 
.lightbox-body .image-preview-wrapper {
	padding: 0.5rem;
    background: var(--gray-40);
    border-radius: 0.5rem;
	margin-bottom: 1rem;
}
.lightbox-body ul.image-composition {
    border-radius: 4px;
    overflow: hidden;
    background: #fff;
    border-radius: 0.25rem;
}

ul.image-composition li {
    position: absolute;
    width: 100%;
	pointer-events: none;
	padding: 3px;
}

ul.image-composition li > div {
    display: flex;
    align-items: center;
    /* justify-content: center; */
}

ul.image-composition li svg {
    pointer-events: all;
}

/* Disable active stroke for preview in Block-Overview */
.preview-info.media-wrapper ul li svg path {
    stroke: none !important;
}

.preview-info.media-wrapper img.no-mask-image {
    border: none !important;
	outline: none !important;
}




/* Planes */ 
.image-editor .planes {
	display: grid; 
	gap: 0.5rem;
}

.image-editor .plane-grid {
	display: grid;
	gap: 0.5rem;
	padding: 0.5rem;
	background: var(--gray-40);
	border-radius: 0.5rem;
	margin-bottom: 1rem;
}

.image-composition-plane {
	display: flex; 
	align-items: center; 
	justify-content: space-between; 
	width: 100%; 
	/* border: solid 1px var(--gray-40);  */
	border-radius: 0.5rem;
	padding: 0.25rem;
	gap: 1rem;
	opacity: 0.66;
	background: #fff;
}
.image-composition-plane.active {
	/* border: solid 1px var(--accent-color);  */
	opacity: 1;
}

.image-editor .image-composition-plane {
	border: solid 1px var(--gray-40);
}

.image-editor .image-preview-and-name,
.image-editor .plane-actions {
	display: flex;
	align-items: center;
}
.image-editor .image-preview-and-name {
	justify-content: left;
	gap: 1rem;
}
.image-editor .plane-actions {
	justify-content: right;
	gap: 0.5rem;
}


/*** 
* Tabel *
***/

/** Tabel Preview **/
/* .tabel-editor .preview-info .table-grid {
	display: grid;
}

.tabel-editor .preview-info .flex-row {
	display: flex;
	justify-content: stretch;
}

.tabel-editor .preview-info .flex-cell {
	padding: 10px;
	border: 1px solid #ccc;
	background-color: #f9f9f9;
	text-align: left;
	flex-grow: 1;
	flex-shrink: 1;
	min-width: 100px;
	word-wrap: break-word; 
}

.tabel-editor .preview-info .cell-content {
	white-space: pre-wrap; 
	text-align: left;
} */

/*** Tabel Preview as table **/
.tabel-editor .preview-info table {
	border: solid 1px var(--wp--color--dark);
	border-radius: 5px;
	overflow: hidden;
}
.tabel-editor .preview-info tr:nth-child(odd) {
	background: #fafafa;
}
.tabel-editor .preview-info td {
	vertical-align: top;
}
.tabel-editor .preview-info table .cell-content {
	padding: 0.5rem;
}

/** Tabel Lightbox **/
.edit-tabel .lightbox-body {
	overflow-x: auto;
}

.edit-tabel .lightbox-body {
	padding-bottom: 1rem;
	margin-bottom: 1rem;
}

.edit-tabel .lightbox-body .tabel-controls .btn,
.edit-tabel .lightbox-body .tabel-column {
	min-width: 200px;
}

.edit-tabel .lightbox-body .tabel-controls {
	width: fit-content;
	min-width: 100%;
	padding: 0.5rem;
	border-left: solid 1px var(--wp--color--dark);
	border-right: solid 1px var(--wp--color--dark);
	border-top: solid 1px var(--wp--color--dark);
	border-top-left-radius: 5px;
	border-top-right-radius: 5px;
}

.edit-tabel .lightbox-body .tabel-row {
	display: flex;
	gap: 1rem;
	width: fit-content;
	min-width: 100%;

	padding: 0.5rem;
	border-left: solid 1px var(--wp--color--dark);
	border-right: solid 1px var(--wp--color--dark);
}

.edit-tabel .lightbox-body .tabel-row .remove-row-btn-wrapper {
	width: 54px;
	max-width: 54px;
}

.edit-tabel .lightbox-body .tabel-row:last-child {
	border-bottom: solid 1px var(--wp--color--dark);
	border-bottom-left-radius: 5px;
	border-bottom-right-radius: 5px;
}

.edit-tabel .lightbox-body .tabel-row:nth-child(even) {
	background: #eaeaea;
}
.edit-tabel .lightbox-body .tabel-row:nth-child(odd) {
	background: #f4f4f4;
}

/** ACCORDION CONTENT ITEM **/
.preview-info ul li.single-accordion-item:first-child {
	margin-top: 0;
}
.preview-info ul li.single-accordion-item:last-child {
	margin-bottom: 0;
}

/* Accordion Preview */ 
.accordion .content-item-preview-wrapper ul {
    margin-left: 0;
}
.accordion .content-item-preview-wrapper ul li {
    list-style: none;
}

.preview-info ul li.single-accordion-item h4 {
	margin-top: 0;
}
.preview-info ul li.single-accordion-item p {
	margin-bottom: 0;
}

.content-item-preview-body,
.content-item-preview-body * {
	max-width: 100%;
}

.content-item-header .item-icon-wrapper {
	margin-right: 1rem;
}

.content-item-preview-wrapper .content-type {
	display: flex;
	align-items: center;
}
.content-item-preview-wrapper .content-type svg {
	margin-right: 1rem;
}
.content-item-preview-wrapper .content-type p {
	line-heigth: 1.2;
}

.content-item-preview-wrapper .content-item-header {
	background: var(--accent-color-secondary);
	background: var(--wp--color--dark);
	color: #fff;
}
.content-item-preview-wrapper .content-item-actions-wrapper {
	/* new style */
	position: absolute;
	z-index: 9;
	margin: auto;
	/* left: auto; */
	/* left: 41px; */
	height: 100%;
	width: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	background: rgb(16 17 74 / 50%);
	background: rgb(29 35 39 / 66%);
	overflow: hidden;

	transition: opacity 240ms var(--effect-transition-timing-function);
	opacity: 0;
}
.content-item-preview-wrapper:hover .content-item-actions-wrapper {
	opacity: 1;
}

.content-item-preview-wrapper .content-item-actions-wrapper .content-item-actions {
	/* display: grid; */
	/* grid-template-columns: 1fr 1fr; */

	/* display: flex;
	flex-wrap: wrap; */

	display: grid;
	grid-template-columns: repeat(auto-fit, 40px);
	max-width: 90%;

	border: solid 1px var(--accent-color-secondary);
	border: solid 1px var(--wp--color--dark);
	background: var(--accent-color-secondary);
	background: var(--wp--color--dark);
	border-radius: 5px;
	padding: 0.25rem;
	height: fit-content;

	transition: all 240ms var(--effect-transition-timing-function);
	transform: translateY(24px);
}
.content-item-preview-wrapper .content-item-actions-wrapper:hover .content-item-actions {
	transform: translateY(0px);
}

.content-item-actions-wrapper .btn {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 0.5rem;
	/* margin-right: 0.25rem; */
	margin-right: 0;
}
.content-item-actions-wrapper .btn:last-child {
	margin-right: 0;
}

.content-item-actions-wrapper .btn svg {
	max-width: 22px;
	max-height: 22px;
	width: 22px;
	height: 22px;
}

/* /////////////////////////////// */
/* SPECIFIC FLEXIBLE CONTENT ITEMS */
/* /////////////////////////////// */

/* Accordion */
li.single-accordion-item {
	border: solid 1px;
	border-radius: 5px;
	padding: 1rem;
	margin: 1rem 0;
}
li.single-accordion-item .btn {
	width: fit-content;
	margin-left: auto;
}

/* ///////////////////////////////////////// */

/**
	* Advanced Settings 
	*/
.advanced-settings .tabs {
	display: flex;
}
.advanced-settings ul.tabs li {
	list-style: none;
	padding: 10px 20px;
	cursor: pointer;
	background: #333;
	border-radius: 4px;
}
.advanced-settings ul.tabs li.active {
	background: #bf1692;
}

/**
	* Lightbox styles
	*/
.lightbox {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background: rgba(0, 0, 0, 0.5);
	z-index: 99999;
	display: flex;
	align-items: center;
	justify-content: center;
}

.lightbox-content {
	background: #fff;
	width: 80vw;
	height: auto;
	/* height: 80vh; */
	max-height: 90vh;
	margin: auto;
	padding: 1rem;
	border: solid 2px var(--acent-color-secondary);
	border-radius: 12px;
	position: relative;
	overflow-y: auto;
}
.lightbox-content.edit-text {
	height: 80vh;
}
.lightbox-header {
	display: flex;
	justify-content: space-between;
	margin-bottom: 1rem;
}

.lightbox-header h3 {
	padding: 0;
	margin: 0;
}

.lightbox-body {
	/* height: calc(100% - 82px - 2rem); */
}

.lightbox .btn {
	margin-left: 0;
}

.lightbox .btn:hover,
.lightbox .btn.active {
	color: #fff;
	background: var(--acent-color);
}

.lightbox img {
	max-width: 100%;
	width: 100%;
	margin: 1rem 0;
	/* max-height: 240px; */
}

/* ".add-content-item" specific */
.lightbox.add-content-item .btn {
	gap: 1rem;
}

/**
	* Slider Module 
	*/
.slider-item {
	border: solid 1px var(--acent-color-secondary);
	border-radius: 5px;
	margin-top: 1rem;
}
.slider-item .slider-item-header {
	display: flex;
	/* flex-direction: column; */
	justify-content: end;
	padding: 0.5rem;
}
.slider-item .slider-item-content {
	padding: 0.5rem;
	display: grid;
	grid-template-columns: 1fr 1fr;
}
.slider-item .slider-item-content img {
	max-height: 200px;
}

/**
	* Pricing Table Module
	*/
.pricing-table .actual-pricing-table {
	margin-top: 2rem;
}
.pricing-header,
.pricing-feature-rows,
.pricing-footer {
	display: grid;
	gap: 1rem;
	border-bottom: solid 1px var(--light-gray);
}

.pricing-header > div,
.pricing-feature-rows .grid-item-content,
.pricing-footer .grid-item {
	background: #eeeeee;
	padding: 0.5rem;
}
.pricing-header > div:first-child,
.pricing-feature-rows .grid-item-content:first-child,
.pricing-footer .grid-item:first-child {
	background: none;
}

.pricing-header > div {
	border-top-left-radius: 1rem;
	border-top-right-radius: 1rem;
}
.pricing-footer .grid-item {
	border-bottom-left-radius: 1rem;
	border-bottom-right-radius: 1rem;
}

.pricing-table .grid-item-content {
	flex-direction: row;
	padding: 0.5rem;
}

.pricing-feature-rows .grid-item .grid-item-content > div {
	width: 100%;
}
.feature-text-wrapper {
	width: 100%;
}

.pricing-table .cta-row-wrapper,
.pricing-table .cta-btn-wrapper {
	border: solid 1px;
	padding: 0.5rem;
	border-radius: 5px;
	margin: 0.5rem 0;
	background-color: rgb(255 255 255 / 50%);
}
.pricing-table .cta-row-wrapper .btn {
	width: fit-content;
	left: auto;
	margin-left: auto;
}
.pricing-table .cta-row-wrapper .content {
	display: flex;
	justify-content: space-between;
	flex-direction: row;
}

/** Animating page builder tabs */
.flexible-content {
	position: relative;
}
.flexible-content > div {
	width: 100%;
}

/* tabs animation */
/*
.tabsfromright-enter-active,
.tabsfrom-right-leave-active {
	transition: all 0.5s ease;
}

.tabsfromright-enter-from {
	opacity: 0;
	background: #ff0;
}
.tabsfromright-leave-to {
	opacity: 0;
	transform: translateX(-150px);
}
*/

/* ////////////// */
/* from left animation */
/* ////////////// */
.tabsfromleft-enter-active,
.tabsfromleft-leave-active {
	transition: all 0.5s ease;
}

.tabsfromleft-enter-from {
	/* position: absolute; */
	opacity: 0 !important;
	/* height: 0; */
	/* background: #f00; */
	transform: translateX(-100%);
}
.tabsfromleft-leave-to {
	/* position: absolute; */
	opacity: 0 !important;
	/* height: 0; */
	/* background: #f00; */
	transform: translateX(100%);
}

/* ////////////// */
/* from right animation */
/* ////////////// */
.tabsfromright-enter-active,
.tabsfromright-leave-active {
	transition: all 0.5s ease;
}

.tabsfromright-enter-from {
	/* position: absolute; */
	opacity: 0 !important;
	/* height: 0; */
	transform: translateX(100%);
}
.tabsfromright-leave-to {
	/* position: absolute; */
	opacity: 0 !important;
	/* height: 0; */
	transform: translateX(-100%);
}

/* from right to left */
/* .tabsfromright-enter-active, 
.tabsfromright-leave-active {
	transition: all 0.5s ease;
}

.tabsfromright-enter-from {
	opacity: 0;
	transform: translateX(150px);
}
.tabsfromright-leave-to {
	opacity: 0;
	transform: translateX(-150px);
} */

/*
 * 
 * 
 * ORGANISIERTER STYLE 
 *
 * 
 */

/** Colum Settings **/
.lightbox-content {
	padding: 0;
	overflow-x: hidden;
	/* background: var(--gray-20); */
	background: #fff;
}

/* lightbox header */
.lightbox-header {
	margin: 0;
	padding: 1rem;
	background: var(--wp--color--dark);
	color: #fff;
}
.lightbox-header h3 {
	color: #fff;
}

/* Lightbox Nav */
.lightbox-nav {
	display: flex;
	padding: 0 1rem 0 1rem;
	background: var(--wp--color--dark);
	color: #fff;
}
.lightbox .lightbox-nav .btn,
.lightbox .lightbox-nav .btn {
	border: none;
	margin-right: 1rem;
}
.lightbox .lightbox-nav .btn:hover,
.lightbox .lightbox-nav .btn.active {
	color: #000;
	background: var(--gray-20);
}

/* Lightbox body */
.lightbox-body {
	padding: 1rem 1rem 2rem 1rem;
}
.lightbox-body h3 {
	margin: 1.5rem 0 0.25rem 0;
}

.flex-row {
	display: flex;
	justify-content: space-between;
	gap: 2rem;
}
.flex-row .input-group {
	flex-grow: 1;
}


.full-row {
    display: flex;
    justify-content: space-between;
}

.input-group.full-row  {
    /* border-bottom: 1px solid #e3d6ea; */
    padding: 1rem 0;
    display: flex;
    align-items: center;
    gap: 2rem;
}

.input-group.full-row input, .input-group.full-row select {
    width: 6rem !important;!I;!;
}

.input-group.full-row label {
    font-weight: 600;
}

/* Lightbox Footer */
.lightbox-footer {
	padding: 1rem 1rem 1rem 1rem;
}






/** SVG Stuff IMAGES */ 
svg image {
    transform-origin: center center;
}



/** Add new Module */ 
.lightbox.add-content-module ul {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1rem;
	padding-bottom: 1rem;
}

.lightbox.add-content-module ul li .button.add-module-button {
    width: 100%;
    height: 100%;
    padding: 0 !important;
	overflow: hidden;
    border-radius: 0.5rem;
}
.lightbox.add-content-module ul li .button.add-module-button:hover {
    border-color: var(--accent-color);
}

.lightbox.add-content-module ul li .component-preview-image img {
    display: block;
    aspect-ratio: 16 / 9;
    object-fit: contain;
    background: #fff;
    min-height: 150px;
    min-width: 280px;
}

.lightbox.add-content-module ul li .button.add-module-button .module-name {
	width: 100%;
	display: flex; 
	justify-content: center;
	/* padding-bottom: 1rem; */
	font-size: 1.5rem;
}
.lightbox.add-content-module ul li .button.add-module-button:hover .module-name {
	color: var(--accent-color);
}







/***
 * testimonial-section Module 
 */

.tab-component {
    padding: 0.5rem;
    background: var(--gray-40);
    border-radius: 0.75rem;
	display: grid;
	gap: 0.5rem;
}

/* Tab Buttons */
ul.tabs {
    transform: translateY(1px);
    display: flex;
    justify-content: start;
    flex-wrap: wrap;
    background: var(--gray-20);
    padding: 0.5rem;
    border-radius: 0.5rem;
    gap: 0.5rem;
}

.tabs-wrapper {
    display: flex;
    width: fit-content;
    /* flex-wrap: wrap; */
    min-width: 111px;
    margin-bottom: 0;
    gap: 0.5rem;
}

li.tab-wrapper {
    display: flex;
    width: fit-content;
    flex-wrap: wrap;
    min-width: 111px;
    margin-bottom: 0;
	gap: 0.5rem;
}

ul.tabs .tab-wrapper  .inner-tab {
    cursor: pointer;
    border-radius: 0.5rem;
    border-bottom: 0;
    padding: 0.5rem 0.75rem;
    display: block;
    margin: 0;
    background: var(--gray-20);
    color: #1d2327;

	display: flex;
    align-items: center;
    gap: 0.5rem;

	transition: all 240ms var(--effect-transition-timing-function);
}

ul.tabs .add-new .inner-tab {

}

li.tab-wrapper .inner-tab.active,
li.tab-wrapper .inner-tab:hover {
    background: var(--gray-40);
    color: #fff;
}



/* Content */
li.item-tab-wrapper {
    display: block;
    padding: 0;
    margin: 0;
}

li.item-tab-wrapper .active-tab {
    border-radius: 0.5rem;
    background-color: var(--gray-20);
    padding: 1rem;
}


/* var(--accent-color-secondary) */




/**
 * Slider Section 
 */
.slider-item-content .image-col {
	max-width: 320px;
    margin: auto;
}
.slider-item-content img {
    max-width: 100%;
    border-radius: 0.5rem;
	object-fit: cover;
}
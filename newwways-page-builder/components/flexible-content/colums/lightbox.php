<div v-if="element.colSettings[index].editing" class="lightbox add-content-item">
    <div class="lightbox-content">
        <!-- Header -->
        <div class="lightbox-header">
            <h3> Settings für {{index + 1}}. Spalte</h3>
            <span class="btn" @click="element.colSettings[index].editing = false">X</span>
        </div>

        <div class="lightbox-nav">
            <span 
                @click="element.colSettings[index].activeTabNumber = 1" 
                :class="element.colSettings[index].activeTabNumber === 1 ? 'active' : ''" 
                class="btn bottom-aligned" 
            >
                Appearance
            </span>
            <span 
                @click="element.colSettings[index].activeTabNumber = 2" 
                :class="element.colSettings[index].activeTabNumber === 2 ? 'active' : ''" 
                class="btn bottom-aligned" 
            >
                Spacing
            </span>
            <span 
                @click="element.colSettings[index].activeTabNumber = 3" 
                :class="element.colSettings[index].activeTabNumber === 3 ? 'active' : ''"
                class="btn bottom-aligned" 
            >
                Background
            </span>
            <span 
                @click="element.colSettings[index].activeTabNumber = 4" 
                :class="element.colSettings[index].activeTabNumber === 4 ? 'active' : ''"
                class="btn bottom-aligned" 
            >
                Border
            </span>
        </div>

        <div class="lightbox-body">
            <tab-transition-component :active-tab-number="element.colSettings[index].activeTabNumber">
                <!-- Appearance -->
                <div v-if="element.colSettings[index].activeTabNumber === 1" class="tab">
                    <h2>Appearance</h2>

                    <div class="input-group">
                        <label>Column Style</label>
                        <select v-model="element.colSettings[index].style">
                            <option v-for="columnStyle in availableColumnStyles" :value="columnStyle">{{columnStyle}}</option>
                        </select>
                    </div>

                    <div class="input-group">
                        <p>Center Content</p>
                        <label class="toggle-switch">
                            <input type="checkbox" v-model="element.colSettings[index].isContentCentered">
                            <span class="slider"></span>
                        </label>
                    </div>
                </div>
                
                <!-- Spacing -->
                <div v-if="element.colSettings[index].activeTabNumber === 2" class="tab">
                    <!-- Margin Settings -->
                    <div class="section">
                        <h3>Margin</h3>
                        <div class="flex-row">
                            <div class="input-group">
                                <label>Top:</label>
                                <input v-model="element.colSettings[index].margin.top" type="text" placeholder="0px" />
                            </div>
                            <div class="input-group">
                                <label>Right:</label>
                                <input v-model="element.colSettings[index].margin.right" type="text" placeholder="0px" />
                            </div>
                            <div class="input-group">
                                <label>Bottom:</label>
                                <input v-model="element.colSettings[index].margin.bottom" type="text" placeholder="0px" />
                            </div>
                            <div class="input-group">
                                <label>Left:</label>
                                <input v-model="element.colSettings[index].margin.left" type="text" placeholder="0px" />
                            </div>
                        </div>
                    </div>

                    <!-- Padding Settings -->
                    <div class="section">
                        <h3>Padding</h3>
                        <div class="flex-row">
                            <div class="input-group">
                                <label>Top:</label>
                                <input v-model="element.colSettings[index].padding.top" type="text" placeholder="0px" />
                            </div>

                            <div class="input-group">
                                <label>Right:</label>
                                <input v-model="element.colSettings[index].padding.right" type="text" placeholder="0px" />
                            </div>

                            <div class="input-group">
                                <label>Bottom:</label>
                                <input v-model="element.colSettings[index].padding.bottom" type="text" placeholder="0px" />
                            </div>

                            <div class="input-group">
                                <label>Left:</label>
                                <input v-model="element.colSettings[index].padding.left" type="text" placeholder="0px" />
                            </div>
                        </div>
                    </div>

                    <!-- translate -->
                    <div class="section" v-if="element.colSettings[index].translate">
                        <h3>Translate</h3>
                        <div class="flex-row">
                            <div class="input-group">
                                <label>X</label>
                                <input v-model="element.colSettings[index].translate.x" type="text" placeholder="0px" />
                            </div>

                            <div class="input-group">
                                <label>Y</label>
                                <input v-model="element.colSettings[index].translate.y" type="text" placeholder="0px" />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Background -->
                <div v-if="element.colSettings[index].activeTabNumber === 3" class="tab">
                    <!-- Background Settings -->
                    <div class="section">
                        <h3>Background</h3>

                        <label>Use Background</label>
                        <input v-model="element.colSettings[index].background.useBackground" type="checkbox" />

                        <div v-if="element.colSettings[index].background.useBackground">
                            <div v-if="!element.colSettings[index].background.useGradient" class="input-group">
                                <label>Background Color:</label>
                                <input v-model="element.colSettings[index].background.color" type="color" />
                                
                                <!-- <label>Background Image URL:</label>
                                <input v-model="element.colSettings[index].background.backgroundImage" type="text" placeholder="Image URL" />

                                <label>Background Position:</label>
                                <input v-model="element.colSettings[index].background.backgroundPosition" type="text" placeholder="e.g., center, top" /> -->
                            </div>


                            <label>Use Gradient:</label>
                            <input v-model="element.colSettings[index].background.useGradient" type="checkbox" />

                            <!-- Gradient Settings -->
                            <div v-if="element.colSettings[index].background.useGradient" class="gradient-settings">
                                <h4>Gradient Settings</h4>
                                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr;">
                                    <div class="input-group">
                                        <label>Gradient Color 1:</label>
                                        <input v-model="element.colSettings[index].background.gradient.color1" type="color" />

                                        <br>

                                        <label>Gradient Position 1:</label>
                                        <input v-model="element.colSettings[index].background.gradient.position1" type="number" placeholder="0" />
                                    </div>
                                    
                                    <div class="input-group">
                                        <label>Gradient Color 2:</label>
                                        <input v-model="element.colSettings[index].background.gradient.color2" type="color" />

                                        <br>

                                        <label>Gradient Position 2:</label>
                                        <input v-model="element.colSettings[index].background.gradient.position2" type="number" placeholder="100" />
                                    </div>

                                    <div class="gradient-preview">
                                        <h4>Gradient Preview</h4>
                                        <div 
                                            v-if="element.colSettings[index].background.gradient.gradientType === 'linear'"
                                            class="gradient-container" 
                                            style="width: 100px; height: 100px; border-radius: 50%;" 
                                            :style="`background: linear-gradient(
                                                ${element.colSettings[index].background.gradient.angle}deg, 
                                                ${element.colSettings[index].background.gradient.color1} ${element.colSettings[index].background.gradient.position1}%, 
                                                ${element.colSettings[index].background.gradient.color2} ${element.colSettings[index].background.gradient.position2}%
                                                );`"
                                        ></div>
                                        <div 
                                            v-if="element.colSettings && element.colSettings[index] && element.colSettings[index].background && element.colSettings[index].background.gradient && element.colSettings[index].background.gradient.gradientType === 'radial'"
                                            class="gradient-container" 
                                            style="width: 100px; height: 100px; border-radius: 50%;"
                                            :style="`background: radial-gradient(
                                                circle at ${element.colSettings[index].background.gradient.circlePositionX ?? '50'}% ${element.colSettings[index].background.gradient.circlePositionY ?? '50'}%, 
                                                ${element.colSettings[index].background.gradient.color1} ${element.colSettings[index].background.gradient.position1}%, 
                                                ${element.colSettings[index].background.gradient.color2} ${element.colSettings[index].background.gradient.position2}%
                                            );`"
                                        ></div>
                                    </div>
                                </div>
                                
                                <div style="display: grid; grid-template-columns: 1fr">
                                    <div class="input-group">
                                        <label>Angle (degrees):</label>
                                        <input v-model="element.colSettings[index].background.gradient.angle" type="number" placeholder="0" />
                                    </div>

                                    <div v-if="element.colSettings[index].background.gradient.gradientType === 'radial'">
                                        <div class="input-group">
                                            <label>Circle Start X:</label>
                                            <input v-model="element.colSettings[index].background.gradient.circlePositionX" type="number" placeholder="0" />
                                        </div>

                                        <div class="input-group">
                                            <label>Circle Start Y:</label>
                                            <input v-model="element.colSettings[index].background.gradient.circlePositionY" type="number" placeholder="0" />
                                        </div>
                                    </div>

                                    <div class="input-group">
                                        <label>Gradient Type:</label>
                                        <select v-model="element.colSettings[index].background.gradient.gradientType">
                                            <option value="linear">linear</option>
                                            <option value="radial">radial</option>
                                        </select>
                                    </div>
                                </div>
                                
                            </div>
                        </div>

                        <label>Force Text Color</label>
                        <input v-model="element.colSettings[index].background.forceTextColor" type="checkbox" />

                        <div v-if="element.colSettings[index].background.forceTextColor" class="input-group full-row">
                            <label>Text Color</label>
                            <input v-model="element.colSettings[index].background.textColor" type="color" />
                        </div>
                    </div>
                </div>

                <!-- border -->
                <div v-if="element.colSettings[index].activeTabNumber === 4" class="tab">
                    <!-- border -->
                    <div class="section" v-if="element.colSettings[index].border">
                        <h3>Border</h3>
                        <div class="flex-col">
                            <div class="input-group full-row">
                                <label>Style</label>
                                <select v-model="element.colSettings[index].border.style">
                                    <option value="none">none</option>
                                    <option value="solid">solid</option>
                                    <option value="dashed">dashed</option>
                                    <option value="dotted">dotted</option>
                                    <option value="double">double</option>
                                    <option value="groove">groove</option>
                                    <option value="ridge">ridge</option>
                                    <option value="inset">inset</option>
                                    <option value="outset">outset</option>
                                </select>
                            </div>
                            
                            <div v-if="element.colSettings[index].border.style !== 'none'" class="input-group full-row">
                                <label>Width</label>
                                <input v-model="element.colSettings[index].border.width" type="text" placeholder="0px" />
                            </div>

                            <div v-if="element.colSettings[index].border.style !== 'none'" class="input-group full-row">
                                <label>Color</label>
                                <input v-model="element.colSettings[index].border.color" type="color" />
                            </div>
                        </div>
                    </div>


                    <!-- border-radius -->
                    <div class="section" v-if="element.colSettings[index].borderRadius">
                        <h3>Border Radius</h3>
                        <div class="flex-col">
                            <div class="input-group full-row">
                                <label>
                                    Sync Border Radius to all corners
                                </label>
                            
                                <input type="checkbox" v-model="element.colSettings[index].borderRadius.isBorderRadiusSynced" />
                            </div>
                        </div>

                        <div class="flex-row">
                            <div class="input-group">
                                <label>Top Left</label>
                                <input v-model="element.colSettings[index].borderRadius.topLeft" @input="syncBorderRadius(element.colSettings[index], 'topLeft')" type="text" placeholder="0px" />
                            </div>

                            <div class="input-group">
                                <label>Top Right</label>
                                <input v-model="element.colSettings[index].borderRadius.topRight" @input="syncBorderRadius(element.colSettings[index], 'topRight')" type="text" placeholder="0px" />
                            </div>

                            <div class="input-group">
                                <label>Bottom Left</label>
                                <input v-model="element.colSettings[index].borderRadius.bottomLeft" @input="syncBorderRadius(element.colSettings[index], 'bottomLeft')" type="text" placeholder="0px" />
                            </div>

                            <div class="input-group">
                                <label>Bottom Right</label>
                                <input v-model="element.colSettings[index].borderRadius.bottomRight" @input="syncBorderRadius(element.colSettings[index], 'bottomRight')" type="text" placeholder="0px" />
                            </div>
                        </div>
                    </div>
                </div>

            </tab-transition-conponent>

        </div>
    </div>
</div>
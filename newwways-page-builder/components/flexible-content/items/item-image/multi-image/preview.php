<!-- Multi Image Preview -->
<div class="image-preview-wrapper">
    <!-- <p>item.imageComposition: {{item.imageComposition}}</p> -->
    <ul class="image-composition">
    <!-- :style="atLeastOneValidImageCompositionItem(item.imageComposition) ? 'display: none;' : ''" -->
        <!-- <li>item.imageComposition: {{item.imageComposition}}</p> -->
        <li v-for="(imageCompositionItem, index) in item.imageComposition" :key="index"> 
            <!-- <p>imageCompositionItem.name: {{imageCompositionItem.name}}</p> -->
            <!-- No Mask -->
            <div v-if="!imageCompositionItem.styles.useImageMask">
                <div 
                    v-if="imageCompositionItem.isVideoTeaser"
                    class="flex-center absolute"
                    :style="`
                        z-index: ${imageCompositionItem.styles.zIndex};
                        width: ${imageCompositionItem.styles.width}%;
                        height: ${imageCompositionItem.styles.height}%;
                        aspect-ratio: ${imageCompositionItem.styles.aspectRatio};
                        margin-top: ${imageCompositionItem.styles.marginTop}%;
                        margin-right: ${imageCompositionItem.styles.marginRight}%;
                        margin-bottom: -${imageCompositionItem.styles.marginTop}%;
                        margin-left: ${imageCompositionItem.styles.marginLeft}%;
                    `"
                > 
                    <?php include PLUGIN_ROOT_PATH . 'components/app/play-button.php'; ?>
                </div>
                <img 
                    v-if="imageCompositionItem.type === 'singleImage'"
                    class="no-mask-image"
                    :src="imageCompositionItem.mediaUrl ? imageCompositionItem.mediaUrl : '<?php echo PLUGIN_RELATIVE_PATH . 'placeholder/image-placeholder.png';?>'" 
                    @click="item.activeImageCompositionSettings = index"
                    :style="`
                        z-index: ${imageCompositionItem.styles.zIndex};
                        opacity: ${imageCompositionItem.styles.opacity};
                        width: ${imageCompositionItem.styles.width}%;
                        height: ${imageCompositionItem.styles.height}%;
                        aspect-ratio: ${imageCompositionItem.styles.aspectRatio};
                        margin-top: ${imageCompositionItem.styles.marginTop}%;
                        margin-right: ${imageCompositionItem.styles.marginRight}%;
                        margin-bottom: -${imageCompositionItem.styles.marginTop}%;
                        margin-left: ${imageCompositionItem.styles.marginLeft}%;
                        outline: ${item.activeImageCompositionSettings === index ? '2px solid yellow' : ''};
                    `"
                >

                <div
                    v-if="imageCompositionItem.type === 'singleColoredSvg'" 
                    :style="`
                        z-index: ${imageCompositionItem.styles.zIndex};
                        background-color: ${imageCompositionItem.styles.color}; 
                        opacity: ${imageCompositionItem.styles.opacity};
                        width: ${imageCompositionItem.styles.width};
                        height: ${imageCompositionItem.styles.height};
                        aspect-ratio: ${imageCompositionItem.styles.aspectRatio};
                        margin-top: ${imageCompositionItem.styles.marginTop};
                        margin-right: ${imageCompositionItem.styles.marginRight};
                        margin-bottom: ${imageCompositionItem.styles.marginBottom};
                        margin-left: ${imageCompositionItem.styles.marginLeft};
                        `"
                ></div>
            </div>

            <!-- use mask -->
            <div v-if="imageCompositionItem.styles.useImageMask" class="image-item">
                <svg 
                    v-if="imageCompositionItem.type === 'singleImage'"
                    class="active image-mask-svg" 
                    xmlns="http://www.w3.org/2000/svg" 
                    xmlns:xlink="http://www.w3.org/1999/xlink" 
                    viewBox="0 0 116 112"
                    style="max-width: 100%; position: relative;"
                    :style="`
                        z-index: ${imageCompositionItem.styles.zIndex};
                        opacity: ${imageCompositionItem.styles.opacity};
                        width: ${imageCompositionItem.styles.width}%;
                        height: ${imageCompositionItem.styles.height}%;
                        aspect-ratio: ${imageCompositionItem.styles.aspectRatio};
                        margin-top: ${imageCompositionItem.styles.marginTop}%;
                        margin-right: ${imageCompositionItem.styles.marginRight}%;
                        margin-bottom: -${imageCompositionItem.styles.marginTop}%;
                        margin-left: ${imageCompositionItem.styles.marginLeft}%;
                        transform: rotate(${imageCompositionItem.styles.imageMaskRotate}deg);
                    `"
                    :data-rotation-degrees="`${imageCompositionItem.styles.imageMaskRotate}`"
                >
                    <!-- TODO: unbedingt mask id einzigartig machen -->
                    <defs>
                        <mask :id="`nw-${element.id}-${index}-mask-1`">
                            <path 
                                class="popup-anim path-anim" 
                                d="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" 
                                data-path-to="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" 
                                data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                                fill="#fff" 
                                style="translate: none; rotate: none; scale: none; transform-origin: 0px 0px;" 
                                data-svg-origin="51 45.5" 
                                transform="matrix(1,0,0,1,0,0)">
                            </path>
                        </mask>
                    </defs>
                    <!-- Das Bild in ein eigenes SVG-Element wrappen -->
                    <svg 
                        xmlns="http://www.w3.org/2000/svg" 
                        viewBox="0 0 116 100" 
                        :mask="`url(#nw-${element.id}-${index}-mask-1)`" 
                        style="width: 100%; height: auto; position: absolute; top: 0; left: 0;"
                        @click="item.activeImageCompositionSettings = index"
                    >
                        <rect 
                            width="100%" 
                            height="100%" 
                            fill="#ccc" 
                            :mask="`url(#nw-${element.id}-${index}-mask-1)`" 
                            :style="`fill: ${imageCompositionItem.styles.color};`"
                        ></rect>
                        <image 
                            :xlink:href="imageCompositionItem.mediaUrl ? imageCompositionItem.mediaUrl : '<?php echo PLUGIN_RELATIVE_PATH . 'placeholder/image-placeholder.png';?>'" 
                            :style="`
                                y: ${imageCompositionItem.styles.imageMaskTranslateY}%; 
                                x: ${imageCompositionItem.styles.imageMaskTranslateX}%; 
                                scale: ${imageCompositionItem.styles.imageMaskScale};
                                transform: rotate(-${imageCompositionItem.styles.imageMaskRotate}deg);
                                `"
                            style="width: 100%;" 
                        />
                    </svg>

                    <svg 
                        v-if="item.activeImageCompositionSettings === index"
                        xmlns="http://www.w3.org/2000/svg" 
                        viewBox="0 0 116 100" 
                        style="width: 100%; height: auto; position: absolute; top: 0; left: 0;"
                    >
                        <path 
                            class="popup-anim path-anim" 
                            d="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" 
                            data-path-to="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" 
                            data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                            fill="none" 
                            stroke="yellow"
                            stroke-width="1"
                            style="translate: none; rotate: none; scale: none; transform-origin: 0px 0px;" 
                            data-svg-origin="51 45.5" 
                            transform="matrix(1,0,0,1,0,0)">
                        </path>
                    </svg>
                </svg>

                <!-- colored SVG -->
                <svg 
                    v-if="imageCompositionItem.type === 'singleColoredSvg'"
                    class="image-mask-svg active" 
                    xmlns="http://www.w3.org/2000/svg" 
                    xmlns:xlink="http://www.w3.org/1999/xlink" 
                    viewBox="0 0 116 100"
                    style="max-width: 100%; position: relative;"
                    :style="`
                        z-index: ${imageCompositionItem.styles.zIndex};
                        opacity: ${imageCompositionItem.styles.opacity};
                        width: ${imageCompositionItem.styles.width}%;
                        height: ${imageCompositionItem.styles.height}%;
                        aspect-ratio: ${imageCompositionItem.styles.aspectRatio};
                        margin-top: ${imageCompositionItem.styles.marginTop}%;
                        margin-right: ${imageCompositionItem.styles.marginRight}%;
                        margin-bottom: -${imageCompositionItem.styles.marginTop}%;
                        margin-left: ${imageCompositionItem.styles.marginLeft}%;
                        transform: rotate(${imageCompositionItem.styles.imageMaskRotate}deg);
                    `"
                    :data-rotation-degrees="`${imageCompositionItem.styles.imageMaskRotate}`"
                >
                    <defs>
                        <mask :id="`nw-${element.id}-${index}-mask-1`">
                            <path 
                                class="popup-anim path-anim" 
                                d="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" 
                                data-path-to="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" 
                                data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                                fill="#fff" 
                                style="translate: none; rotate: none; scale: none; transform-origin: 0px 0px;" 
                                data-svg-origin="51 45.5" 
                                transform="matrix(1,0,0,1,0,0)">
                            </path>
                        </mask>
                    </defs>
                    <!-- Das Bild in ein eigenes SVG-Element wrappen -->
                    <svg 
                        xmlns="http://www.w3.org/2000/svg" 
                        viewBox="0 0 116 100" 
                        :mask="`url(#nw-${element.id}-${index}-mask-1)`" 
                        style="width: 100%; height: auto; position: absolute; top: 0; left: 0;"
                        @click="item.activeImageCompositionSettings = index"
                    >
                        <rect 
                            width="100%" 
                            height="100%" 
                            fill="#ccc" 
                            :mask="`url(#nw-${element.id}-${index}-mask-1)`" 
                            :style="`fill: ${imageCompositionItem.styles.color};`"
                        ></rect>
                    </svg>

                    <svg 
                        v-if="item.activeImageCompositionSettings === index"
                        xmlns="http://www.w3.org/2000/svg" 
                        viewBox="0 0 116 100"
                        style="width: 100%; height: auto; position: absolute; top: 0; left: 0;"
                    >
                        <path 
                            class="popup-anim path-anim" 
                            d="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" 
                            data-path-to="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" 
                            data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                            fill="none" 
                            stroke="yellow"
                            stroke-width="3"
                            style="translate: none; rotate: none; scale: none; transform-origin: 0px 0px;" 
                            data-svg-origin="51 45.5" 
                            transform="matrix(1,0,0,1,0,0)">
                        </path>
                    </svg>
                </svg>
            </div>
        </li>
    </ul>
</div>
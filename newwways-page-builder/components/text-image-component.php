<!-- Text Image Modul -->
<div v-if="module.type === 'text-image'" class="text-image">
    <div class="block-content">
        <div class="col col-1" :class="{reverseOrder: module.reverseOrder}">
            
            <div>
                <label :for="`nw-input-tagline-${element.id}-${module.id}`">tagline</label><br/>
                <input type="text" :id="`nw-input-tagline-${element.id}-${module.id}`" v-model="module.tagline" />
            </div>

            <label :for="`nw-input-${element.id}-${module.id}`">Headline</label><br/>
            <input type="text" :id="`nw-input-${element.id}-${module.id}`" v-model="module.text" />

            <ul v-if="module.textContent?.length" class="element-wrapper"> 
                <li v-for="(textContent, index) in module.textContent">
                    <div class="element-header flex">
                        <!-- up button -->
                        <span @click="changeOrder(module, index, 'prev')" class="btn">
                            <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m5 15 7-7 7 7"/>
                            </svg>
                        </span>
                        <!-- down button -->
                        <span @click="changeOrder(module, index, 'next')" class="btn">
                            <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 9-7 7-7-7"/>
                            </svg>
                        </span>
                        <!-- Delete button -->
                        <span @click="deleteTextContent(module, index, itemIndex)" class="btn">
                            <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                <path fill-rule="evenodd" d="M8.586 2.586A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4a2 2 0 0 1 .586-1.414ZM10 6h4V4h-4v2Zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Z" clip-rule="evenodd"/>
                            </svg>
                        </span>
                    </div>
                    

                    <!-- if type is headline, or paragraph -->
                    <div v-if="textContent.type === 'headline' || textContent.type === 'paragraph'" class="inner-element">
                        <label :for="`nw-input-${element.id}-${module.id}-${index}`">{{textContent.type}}</label><br/>
                        <input type="text" :id="`nw-input-${element.id}-${module.id}-${index}`" v-model="textContent.content" />
                    </div>

                    <!-- if type is headline, or paragraph -->
                    <div v-if="textContent.type === 'list'" class="inner-element">
                        <ul>
                            <li v-for="(item, itemIndex) in textContent.listItems">
                                <div class="item-list-header flex">
                                    <label :for="`nw-input-${element.id}-${module.id}-${index}-${itemIndex}`">Item</label><br/>
                                    <span @click="deleteListItem(module, index, itemIndex)" class="btn">X</span>
                                </div>
                                <input type="text" :id="`nw-input-${element.id}-${module.id}-${index}-${itemIndex}`" v-model="item.content" />
                            </li>
                            <li v-if="textContent.listItems?.length === 0">No List Items</li>
                        </ul>
                        <span @click="addListItem(textContent.listItems)" class="btn">add list item</span>
                    </div>
            
                    <!-- button -->
                    <div v-if="textContent.type === 'button'" class="inner-element">
                        <!-- Button text -->
                        <label :for="`nw-input-${element.id}-${index}`">{{textContent.type}} text</label><br/>
                        <input type="text" :id="`nw-input-${element.id}-${index}`" v-model="textContent.content" />
                        
                        <!-- Button Link -->
                        <label :for="`nw-input-${element.id}-${index}`">{{textContent.type}} Link</label><br/>
                        <input type="text" :id="`nw-input-${element.id}-${index}`" v-model="textContent.link" />

                        <!-- useModal Switch -->
                        <p>useModal: {{textContent.useModal}}</p>
                        <label class="toggle-switch">
                            <input type="checkbox" v-model="element.useImage">
                            <span class="slider"></span>
                        </label>
                    </div>
                </li>
            </ul>

            <div class="grid" style="grid-template-columns: 1fr 1fr;">
                <span @click="addFieldToBlock(module, 'headline')" class="btn">add headline</span>
                <span @click="addFieldToBlock(module, 'paragraph')" class="btn">add paragraph</span>
                <span @click="addFieldToBlock(module, 'list')" class="btn">add list</span>
                <span @click="addFieldToBlock(module, 'button')" class="btn">add button</span>
            </div>

            <!-- <p>raw module: {{ module }}</p> -->

        </div>
    
        <div class="col col-2">
            <label :for="`nw-image-${element.id}-${module.id}`">Bild hochladen:</label>
            <button type="button" @click="openMediaLibrary(module, 'image')">Bild auswählen</button>
            <div v-if="module.image">
                <img :src="module.image" alt="Uploaded Image" style="max-width: 100%; height: auto;" />
            </div>
        </div>
    </div>

    <!-- Switch sides -->
    <p>Reverse Order: {{element.reverseOrder}}</p>
    <label class="toggle-switch">
        <input type="checkbox" v-model="element.reverseOrder">
        <span class="slider"></span>
    </label>
</div>
<div v-if="element.type === 'slider-section'" class="slider-section">
    <!-- Content -->
    <div v-if="element.uiValues.activeTab === 'content'" class="content-wrapper p-4">
        <div class="block-content">
            <div class="grid">
                <div>
                    <label :for="`nw-input-tagline-${element.id}`">Tagline</label><br/>
                    <input type="text" :id="`nw-input-tagline-${element.id}`" v-model="element.tagline" />
                </div>

                <div>
                    <label :for="`nw-input-heading-${element.id}`">Heading</label><br/>
                    <input type="text" :id="`nw-input-heading-${element.id}`" v-model="element.heading" />
                </div>

                <div>
                    <label :for="`nw-input-paragraph-${element.id}`">Absatz</label><br/>
                    <textarea :id="`nw-input-paragraph-${element.id}`" v-model="element.paragraph" /></textarea>
                </div>
            </div>

            <item-tab-component 
                v-model="element.sliderItems" 
                :element="element" 
                @add-new-item="addSliderItem(element)" 
                @delete-item="removeSliderItem(element, $event)">
                <template #default="{ item, index }">
                    <!-- Variabler Inhalt für Testimonials -->
                    <div>
                        <div class="slider-item-content">
                            <!-- <label :for="`nw-image-${element.id}-${index}`">Icon wählen:</label> -->

                            <!-- <div v-if="slideItem.isIconImage">
                                <button :id="`nw-image-${element.id}-${index}`" type="button" @click="openMediaLibrary(slideItem, 'iconUrl')">Icon auswählen</button>
                                <div v-if="slideItem.iconUrl">
                                    <img :src="slideItem.iconUrl" alt="Icon" style="max-width: 100px; height: auto;" />
                                </div>
                            </div>
                            <div v-if="!slideItem.isIconImage">
                                <textarea :id="`nw-image-${element.id}-${index}`" v-model="slideItem.icon"></textarea>
                            </div> -->
                            <div class="image-col">
                                <label :for="`nw-image-${element.id}-${index}`">Bild hochladen:</label>
                                <span class="btn" @click="openMediaLibraryAndSaveUrlAndImage(item.image)">Bild auswählen</span>
                                <div v-if="item.image?.url">
                                    <img 
                                        :src="item.image?.url" 
                                        alt="Uploaded Image" 
                                        :class="{'preserve-aspect-ratio': element.preserveAspectRatio}" 
                                        :style="`aspect-ratio: ${element.imageAspectRatioWidth} / ${element.imageAspectRatioHeight}`" 
                                    />
                                </div>
                            </div>

                            <!-- <p>item.image: {{item.image}}</p> -->
                            
                            <div class="text-col">
                                <div v-if="element.sliderStyle === 'team'">
                                    <label :for="`nw-input-title-${element.id}-${index}`">Tagline</label><br/>
                                    <input type="text" :id="`nw-input-title-${element.id}-${index}`" v-model="item.title" />

                                    <label :for="`nw-input-name-${element.id}-${index}`">Headline</label><br/>
                                    <input type="text" :id="`nw-input-name-${element.id}-${index}`" v-model="item.name" />
                                </div>
                            
                                <div class="input-group">
                                    <p>Use Link on Slider Item</p>
                                    <label class="toggle-switch">
                                        <input type="checkbox" v-model="item.useLink">
                                        <span class="slider"></span>
                                    </label>
                                </div>

                                <div v-if="item.useLink" class="input-group">
                                    <label :for="`nw-input-link-${element.id}-${index}`">Link</label><br/>
                                    <input type="text" :id="`nw-input-link-${element.id}-${index}`" v-model="item.linkHref" />

                                    <label :for="`nw-input-open-in-new-tab-${element.id}-${index}`">Open in new Tab</label><br/>
                                    <input type="checkbox" :id="`nw-input-open-in-new-tab-${element.id}-${index}`" v-model="item.openLinkInNewTab" />
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
            </item-tab-component>
        </div>
    </div>

    <!-- grid template -->
    <!-- <div class="additional-settings">
        <label>Grid Template / Anzahl der Spalten</label>
        <select v-model="element.gridTemplate">
            <option value="1">1</option>
            <option value="2">2</option>
            <option value="3">3</option>
            <option value="4">4</option>
        </select>
    </div> -->

    <!-- Appearance -->
    <div v-if="element.uiValues.activeTab === 'appearance'" class="content-wrapper p-4">
        <!-- 
        // Inputs for following settings:
        cta: {
            useCta: false,
            ctaText: '',
            ctaLink: '',
            openLinkInNewTab: false,
            ctaStyle: 'primary',
            useModal: false,
            modalId: '',
        },
        -->
        <div class="input-group">
            <p>use CTA</p>
            <label class="toggle-switch">
                <input type="checkbox" v-model="element.cta.useCta">
                <span class="slider"></span>
            </label>
        </div>
        
        <div class="input-group">
            <p>use Modal as CTA Target</p>
            <label class="toggle-switch">
                <input type="checkbox" v-model="element.cta.useModal">
                <span class="slider"></span>
            </label>
        </div>


        <div class="input-group">
            <p>Style</p>
            <select v-model="element.sliderStyle">
                <option v-for="(style, index) in availableSliderAppearances" :value="style" :key="index">{{style}}</option>
            </select>
        </div>

        <!-- <div class="input-group">
            <p>Preserve Aspect Ratio</p>
            <label class="toggle-switch">
                <input type="checkbox" v-model="element.preserveAspectRatio">
                <span class="slider"></span>
            </label>
        </div> -->

        <!-- v-if="element.preserveAspectRatio" -->
        <div v-if="isUserAdmin">
            <h3>Aspect Ratio</h3>
            <div class="flex-row" style="display: flex; gap: 1rem;">
                <div class="input-group">
                    <p>Width</p>
                    <input type="number" :min="1" :max="100" v-model="element.imageAspectRatioWidth" />
                </div>
                
                <div class="input-group">
                    <p>Height</p>
                    <input type="number" :min="1" :max="100" v-model="element.imageAspectRatioHeight" />
                </div>
            </div>
            
            
            <h3>Image-Fit</h3>
            <div class="input-group">
                <p>Object Fit</p>
                <select v-model="element.imageObjectFit">
                    <option value="cover">Cover</option>
                    <option value="contain">Contain full image</option>
                </select>
            </div>
        </div>
            


        <hr>

        <div v-if="!element.cta.useModal && element.cta.useCta">
            <div class="input-group">
                <label :for="`nw-input-cta-text-${element.id}`">CTA Text</label><br/>
                <input type="text" :id="`nw-input-cta-text-${element.id}`" v-model="element.cta.ctaText" />
            </div>

            <div class="input-group">
                <label :for="`nw-input-cta-link-${element.id}`">CTA Link</label><br/>
                <input type="text" :id="`nw-input-cta-link-${element.id}`" v-model="element.cta.ctaLink" />
            </div>

            <div class="input-group">
                <label :for="`nw-input-open-in-new-tab-${element.id}`">Open in new Tab</label><br/>
                <input type="checkbox" :id="`nw-input-open-in-new-tab-${element.id}`" v-model="element.cta.openLinkInNewTab" />
            </div>

            <div class="input-group">
                <label :for="`nw-input-cta-style-${element.id}`">CTA Style</label><br/>
                <select :id="`nw-input-cta-style-${element.id}`" v-model="element.cta.ctaStyle">
                    <option v-for="(ctaStyle, index) in availableCtaAppearances" :value="ctaStyle" :key="index">{{ctaStyle}}</option>
                </select>
            </div>
        </div>

        <div v-if="element.cta.useModal && element.cta.useCta">
            <div class="input-group">
                <label :for="`nw-input-modal-id-${element.id}`">Modal ID</label><br/>
                <input type="text" :id="`nw-input-modal-id-${element.id}`" v-model="element.cta.modalId" />
            </div>
        </div>

    </div>

    <!-- Spacing -->
    <div v-if="element.uiValues.activeTab === 'spacing'" class="content-wrapper p-4">
        <?php include PLUGIN_ROOT_PATH . 'components/advancedSettings/spacing-component.php'; ?>
    </div>
    
</div>
<!-- End slider -->
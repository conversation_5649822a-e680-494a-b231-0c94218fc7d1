<div v-if="element.type === 'sticky-text-image'" class="sticky-text-image">
    <tab-transition-component :active-tab-number="element.uiValues.activeTabNumber">
        <!-- Content -->
        <div v-if="element.uiValues.activeTab === 'content'" class="content-wrapper p-4">
            <div class="block-content">
                
                <div v-for="(section, index) in element.sections" :key="index">
                    <div class="sticky-header" style="display: flex; align-items: center; justify-content: space-between;">
                        <span></span>
                        <span class="btn" @click="removeStickySection(element, index)">X</span>
                    </div>
                    <div class="grid">
                        <!-- sticky-text -->
                        <div class="sticky-text">
                            <label :for="`nw-input-${element.id}-heading-${index}`">Heading</label><br/>
                            <input type="text" :id="`nw-input-${element.id}-heading-${index}`" v-model="section.heading" />
                            
                            <label :for="`nw-input-${element.id}-content-${index}`">Paragraph</label><br/>
                            <textarea :id="`nw-input-${element.id}-content-${index}`" v-model="section.paragraph"></textarea>
                        </div>
                        <!-- sticky image -->
                        <div class="sticky-image">
                            <!-- <label :for="`nw-input-${element.id}-heading-${index}`">svgCode</label><br/>
                            <textarea :id="`nw-input-${element.id}-heading-${index}`" v-model="section.svgCode" /></textarea> -->
                            <label :for="`nw-image-${element.id}-image-${index}`">Bild hochladen:</label>
                            <button type="button" @click="openMediaLibraryAndSaveUrlAndImage(section.imageData)" >Bild auswählen</button>
                            <div v-if="section.imageData?.url">
                                <img :src="section.imageData.url" alt="Uploaded Image" style="max-width: 100%; height: auto;" />
                            </div>
                            <!-- <p>section: {{section}}</p> -->
                        </div>
                    </div>
                </div>

                <span @click="addStickySection(element)" class="btn">Add Section</span>
            </div>
        </div>

        <!-- Spacing -->
        <div v-if="element.uiValues.activeTab === 'spacing'" class="spacing-wrapper p-4">
            <?php include PLUGIN_ROOT_PATH . 'components/advancedSettings/spacing-component.php'; ?>
        </div>
    </tab-transition-component>
</div>
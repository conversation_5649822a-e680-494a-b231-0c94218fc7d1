<div v-if="element.type === 'hero'" :class="element.type">
    <tab-transition-component :active-tab-number="element.uiValues.activeTabNumber">
        <!-- Content -->
        <div v-if="element.uiValues.activeTab === 'content'" class="content-wrapper p-4">
            <label :for="`nw-input-${element.id}-tagline`">Hero Tagline</label><br/>
            <textarea type="text" :id="`nw-input-${element.id}-tagline`" v-model="element.tagline">
            </textarea>
            
            
            <div v-if="!element.useTypingAnimation">
                <label :for="`nw-input-${element.id}-headline`">Hero Headline</label><br/>
            </div>
            <div v-if="element.useTypingAnimation">
                <label :for="`nw-input-${element.id}-headline`">Hero Headline before typing Animation</label><br/>
            </div>
            <textarea type="text" :id="`nw-input-${element.id}-headline`" v-model="element.headline">
            </textarea>
            
            <div v-if="element.useTypingAnimation">
                <label :for="`nw-input-${element.id}-typingAnimationContent`">Animation Text (seperate with pipes (|))</label><br/>
                <textarea type="text" :id="`nw-input-${element.id}-typingAnimationContent`" v-model="element.typingAnimationContent">
                </textarea>
            </div>

            <div v-if="element.useTypingAnimation">
                <label :for="`nw-input-${element.id}-headlineAfterTyping`">Hero Headline before typing Animation</label><br/>
                <textarea type="text" :id="`nw-input-${element.id}-headlineAfterTyping`" v-model="element.headlineAfterTyping">
                </textarea>
            </div>

            <label :for="`nw-input-${element.id}-content`">Hero Content</label><br/>
            <textarea type="text" :id="`nw-input-${element.id}-content`" v-model="element.content">
            </textarea>
            <label :for="`nw-input-${element.id}-ctaText`">Hero Teaser cta Text</label><br/>
            <textarea type="text" :id="`nw-input-${element.id}-ctaText`" v-model="element.ctaText">
            </textarea>
            <label :for="`nw-input-${element.id}-ctaLink`">Hero Teaser ctaLink</label><br/>
            <textarea type="text" :id="`nw-input-${element.id}-ctaLink`" v-model="element.ctaLink">
            </textarea>

            <label :for="`nw-input-${element.id}-ctaUseModal`">Open Modal statt Link</label><br />
            <label class="toggle-switch">
                <input type="checkbox" :id="`nw-input-${element.id}-ctaUseModal`" v-model="element.ctaUseModal">
                <span class="slider"></span>
            </label>
        </div>

        <!-- Appearance -->
        <div v-if="element.uiValues.activeTab === 'appearance'" class="appearance-wrapper p-4">
            <h2>Appearance</h2>

            <label :for="`nw-input-${element.id}-useImageVideoBackground`">Use Image/Vieo-Background (Startseiten Hero)</label><br />
            <label class="toggle-switch">
                <input type="checkbox" :id="`nw-input-${element.id}-useImageVideoBackground`" v-model="element.useImageVideoBackground">
                <span class="slider"></span>
            </label>

            <!-- <p>Center content (text, button etc.)</p>
            <label class="toggle-switch">
                <input type="checkbox" v-model="element.isCentered">
                <span class="slider"></span>
            </label> -->

            <!-- disableModule -->
            <p>Use Typing Animation:</p>
            <label class="toggle-switch">
                <input type="checkbox" v-model="element.useTypingAnimation">
                <span class="slider"></span>
            </label>
            
            <!-- disableModule -->
            <p>Disable Module: {{element.disableModule}}</p>
            <label class="toggle-switch">
                <input type="checkbox" v-model="element.disableModule">
                <span class="slider"></span>
            </label>

            <hr>
        
            <!-- Full size background image or video -->
            <!-- <p>element.useImageVideoBackground: {{element.useImageVideoBackground}}</p> -->
            <div v-if="element.useImageVideoBackground">
                <label :for="`nw-background-${element.id}`">Background Type:</label>

                <!-- Background Type Selection -->
                <div class="input-group">
                    <select v-model="element.backgroundType">
                        <option value="image">Image</option>
                        <option value="video">Video</option>
                    </select>
                </div>

                <!-- Image Background -->
                <div v-if="element.backgroundType === 'image'">
                    <button type="button" @click="openMediaLibraryAndSaveUrlAndImage(element.background, 'image')">Choose Image</button>
                    <div v-if="element.background?.url">
                        <img :src="element.background?.url" alt="Background Image" style="max-width: 200px; max-height: 150px; object-fit: cover;" />
                        <!-- <p>{{element.background}}</p> -->
                    </div>
                </div>

                <!-- Video Background -->
                <div v-if="element.backgroundType === 'video'">
                    <button type="button" @click="openMediaLibraryAndSaveUrlAndImage(element.background, 'video')">Choose Video</button>
                    <div v-if="element.background?.url">
                        <video controls :src="element.background?.url" style="max-width: 200px; max-height: 150px;">
                            Your browser does not support the video tag.
                            <source :src="element.background?.url" type="video/mp4">
                        </video>
                        <!-- <p>{{element.background}}</p> -->
                    </div>
                </div>
            </div>


            <!-- Bubble Backgrounds -->
            <div v-if="!element.useImageVideoBackground">
                <h3>upload background images for Bubbles</h3>
                <!-- <p>bubbleBackgrounds: {{element.bubbleBackgrounds}}</p> -->

                <ul class="secondary-tab">
                    <li 
                        v-for="(bubble, index) in [1, 2, 3, 4]" 
                        @click="element.uiValues.secondaryTabNumber = index"
                        :class="{ active: element.uiValues.secondaryTabNumber === index}"
                    >
                        <p v-if="index === 0">
                            Left Bubble
                        </p>
                        <p v-if="index >= 1">
                            Right Bubble {{index}}
                        </p>
                    </li>
                </ul>

                <div class="grid">
                    <!-- left bubble -->
                    <div class="left-bubble" v-if="element.uiValues.secondaryTabNumber === 0">
                        <div class="bubble-data">
                            <!-- Content -->
                            <div class="grid-item" :class="{ 'show-add-image': !element.bubbleBackgrounds?.leftBubble?.first }">
                                <div class="grid-item-inner-wrapper">
                                    <div class="button-wrapper">
                                        <button 
                                            :id="`nw-background-${element.id}`" 
                                            type="button" 
                                            @click="openMediaLibraryAndSaveUrlAndImage(element.bubbleBackgrounds.leftBubble.first)"
                                        >
                                            Bild auswählen
                                        </button>
                                        <button 
                                            v-if="element.bubbleBackgrounds?.leftBubble?.first" 
                                            :id="`nw-background-${element.id}-delete`" 
                                            type="button" 
                                            @click="removeImageUrlAndIdFromElement(element.bubbleBackgrounds.leftBubble.first)"
                                        >
                                            <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                <path fill-rule="evenodd" d="M8.586 2.586A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4a2 2 0 0 1 .586-1.414ZM10 6h4V4h-4v2Zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Z" clip-rule="evenodd"/>
                                            </svg>
                                        </button>
                                    </div>
                                    <div class="grid-item-content">
                                        <div v-if="element.bubbleBackgrounds.leftBubble.first" class="image-wrapper">
                                            <!-- <p>element.bubbleBackgrounds.leftBubble.first: {{element.bubbleBackgrounds.leftBubble.first}}</p> -->
                                            <!-- <img :src="element.bubbleBackgrounds.leftBubble.first" alt="Icon" style="height: auto;" /> -->
                                            
                                            <svg class="active" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 116 116">
                                                <defs>
                                                    <mask :id="`nw-element-${element.id}-left-bubble-first-mask`">
                                                        <!-- d="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z"  -->
                                                        <path 
                                                            class="popup-anim path-anim" 
                                                            d="M 8.5668 84.6681 c 9.7064 19.2027 24.9249 27.4879 45.6544 24.8556 c 20.7295 -2.6312 37.312 -12.232 49.7508 -28.8035 c 12.4377 -16.5704 12.4377 -33.1892 0 -49.8575 C 91.5343 14.1944 74.7065 4.1547 53.4897 0.7436 C 32.2718 -2.6686 17.0544 5.665 7.8353 25.7455 c -9.218 20.0794 -8.9738 39.721 0.7315 58.9226 Z"
                                                            data-path-to="M 8.5668 84.6681 c 9.7064 19.2027 24.9249 27.4879 45.6544 24.8556 c 20.7295 -2.6312 37.312 -12.232 49.7508 -28.8035 c 12.4377 -16.5704 12.4377 -33.1892 0 -49.8575 C 91.5343 14.1944 74.7065 4.1547 53.4897 0.7436 C 32.2718 -2.6686 17.0544 5.665 7.8353 25.7455 c -9.218 20.0794 -8.9738 39.721 0.7315 58.9226 Z" 
                                                            data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                                                            fill="#fff"
                                                        >
                                                        </path>
                                                    </mask>
                                                </defs>
                                                <!-- Das Bild in ein eigenes SVG-Element wrappen -->
                                                <svg 
                                                    xmlns="http://www.w3.org/2000/svg" 
                                                    viewBox="0 0 116 116" 
                                                    :mask="`url(#nw-element-${element.id}-left-bubble-first-mask)`" 
                                                    style="width: 100%; height: auto; position: absolute; top: 0; left: 0;"
                                                >
                                                    <image 
                                                        :xlink:href="element.bubbleBackgrounds.leftBubble.first.url" 
                                                        :y="`${element.bubbleBackgrounds.leftBubble.first.styles.imageMaskTranslateY}`" 
                                                        :x="`${element.bubbleBackgrounds.leftBubble.first.styles.imageMaskTranslateX}`" 
                                                        :transform="`scale(${element.bubbleBackgrounds.leftBubble.first.styles.imageMaskScale})`"
                                                        style="width: 100%;"
                                                    >
                                                    </image>
                                                </svg>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Settings -->
                            <div class="grid-item">
                                <div class="grid-item-settings" v-if="element.bubbleBackgrounds.leftBubble.first?.styles">
                                    <h2>Image Positioning</p>

                                    <p>translateY (in px)</p>
                                    <input type="number" v-model="element.bubbleBackgrounds.leftBubble.first.styles.imageMaskTranslateY">

                                    <p>translateX (in px)</p>
                                    <input type="number" v-model="element.bubbleBackgrounds.leftBubble.first.styles.imageMaskTranslateX">
                                    
                                    <p>scale (in % | 1 = 100%)</p>
                                    <input type="number" step="0.1" v-model="element.bubbleBackgrounds.leftBubble.first.styles.imageMaskScale">
                                </div>
                            </div>

                        </div>
                    </div>

                    <!-- right bubbles -->
                    <!-- Right Bubbles > First -->
                    <div class="bubble-data"  v-if="element.uiValues.secondaryTabNumber === 1">
                        <!-- Content -->
                        <div class="grid-item" :class="{ 'show-add-image': !element.bubbleBackgrounds?.rightBubbles?.first }">
                            <div class="grid-item-inner-wrapper">
                                <div class="button-wrapper">
                                    <!-- Just an image -->
                                    <div v-if="!element.bubbleBackgrounds.rightBubbles.first.isVideoLightbox">
                                        <button 
                                            :id="`nw-background-${element.id}`" 
                                            type="button" 
                                            @click="openMediaLibraryAndSaveUrlAndImage(element.bubbleBackgrounds.rightBubbles.first)"
                                        >
                                            Bild auswählen
                                        </button>
                                        <button 
                                            v-if="element.bubbleBackgrounds?.rightBubbles?.first" 
                                            :id="`nw-background-${element.id}-delete`" 
                                            type="button" 
                                            @click="removeImageUrlAndIdFromElement(element.bubbleBackgrounds.rightBubbles.first)"
                                        >
                                            <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                <path fill-rule="evenodd" d="M8.586 2.586A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4a2 2 0 0 1 .586-1.414ZM10 6h4V4h-4v2Zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Z" clip-rule="evenodd"/>
                                            </svg>
                                        </button>
                                    </div>
                                    <!-- Video URL -->
                                    <div v-if="element.bubbleBackgrounds.rightBubbles.first.isVideoLightbox">
                                        <button 
                                            :id="`nw-background-${element.id}`" 
                                            type="button" 
                                            @click="openMediaLibraryAndSaveUrlAndImage(element.bubbleBackgrounds.rightBubbles.first.videoSettings.poster)"
                                        >
                                            Thumbnail auswählen
                                        </button>
                                        <!-- TODO: !!!!!!!! Das muss ich unbedingt ändern !!!!!!!!!!!!!! Hier muss die ID gespeichert werden !!!!!! -->
                                        <button 
                                            :id="`nw-background-${element.id}`" 
                                            type="button" 
                                            @click="openMediaLibraryAndSaveUrlAndImage(element.bubbleBackgrounds.rightBubbles.first.videoSettings, 'video')"
                                        >
                                            Video auswählen
                                        </button>
                                        <button 
                                            v-if="element.bubbleBackgrounds?.rightBubbles?.first" 
                                            :id="`nw-background-${element.id}-delete`" 
                                            type="button" 
                                            @click="removeImageUrlAndIdFromElement(element.bubbleBackgrounds.rightBubbles.first.videoSettings.poster)"
                                        >
                                            <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                <path fill-rule="evenodd" d="M8.586 2.586A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4a2 2 0 0 1 .586-1.414ZM10 6h4V4h-4v2Zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Z" clip-rule="evenodd"/>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            
                                <div class="grid-item-content">
                                    <!-- Play Button -->
                                    <div v-if="element.bubbleBackgrounds.rightBubbles.first.isVideoLightbox" class="flex-center absolute"> 
                                        <?php include PLUGIN_ROOT_PATH . 'components/app/play-button.php'; ?>
                                    </div>

                                    <div v-if="element.bubbleBackgrounds.rightBubbles.first" class="image-wrapper">
                                        <!-- <p>element.bubbleBackgrounds.rightBubbles.first: {{element.bubbleBackgrounds.rightBubbles.first}}</p> -->
                                        <!-- <img :src="element.bubbleBackgrounds.rightBubbles.first" alt="Icon" style="height: auto;" /> -->
                                        
                                        <svg class="active" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 116 112">
                                            <defs>
                                                <mask :id="`nw-element-${element.id}-left-bubble-first-mask`">
                                                    <!-- d="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z"  -->
                                                    <path 
                                                        class="popup-anim path-anim" 
                                                        d="M 8.5668 84.6681 c 9.7064 19.2027 24.9249 27.4879 45.6544 24.8556 c 20.7295 -2.6312 37.312 -12.232 49.7508 -28.8035 c 12.4377 -16.5704 12.4377 -33.1892 0 -49.8575 C 91.5343 14.1944 74.7065 4.1547 53.4897 0.7436 C 32.2718 -2.6686 17.0544 5.665 7.8353 25.7455 c -9.218 20.0794 -8.9738 39.721 0.7315 58.9226 Z"
                                                        data-path-to="M 8.5668 84.6681 c 9.7064 19.2027 24.9249 27.4879 45.6544 24.8556 c 20.7295 -2.6312 37.312 -12.232 49.7508 -28.8035 c 12.4377 -16.5704 12.4377 -33.1892 0 -49.8575 C 91.5343 14.1944 74.7065 4.1547 53.4897 0.7436 C 32.2718 -2.6686 17.0544 5.665 7.8353 25.7455 c -9.218 20.0794 -8.9738 39.721 0.7315 58.9226 Z" 
                                                        data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                                                        fill="#fff"
                                                    >
                                                    </path>
                                                </mask>
                                            </defs>
                                            <!-- Das Bild in ein eigenes SVG-Element wrappen -->
                                            <svg 
                                                xmlns="http://www.w3.org/2000/svg" 
                                                viewBox="0 0 116 112" 
                                                :mask="`url(#nw-element-${element.id}-left-bubble-first-mask)`" 
                                                style="width: 100%; height: auto; position: absolute; top: 0; left: 0;"
                                            >   
                                                <image 
                                                    :xlink:href="element.bubbleBackgrounds.rightBubbles.first.isVideoLightbox ? element.bubbleBackgrounds.rightBubbles.first.videoSettings?.poster?.url : element.bubbleBackgrounds.rightBubbles.first.url" 
                                                    :y="`${element.bubbleBackgrounds.rightBubbles.first.styles.imageMaskTranslateY}`" 
                                                    :x="`${element.bubbleBackgrounds.rightBubbles.first.styles.imageMaskTranslateX}`" 
                                                    :transform="`scale(${element.bubbleBackgrounds.rightBubbles.first.styles.imageMaskScale})`"
                                                    style="width: 100%;"
                                                >
                                                </image>
                                            </svg>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Settings -->
                        <div class="grid-item">
                            <div class="grid-item-settings" v-if="element.bubbleBackgrounds.rightBubbles.first?.styles">
                                <h3>Image Settings</h3>

                                <p>translateY (in %)</p>
                                <input type="number" v-model="element.bubbleBackgrounds.rightBubbles.first.styles.imageMaskTranslateY">

                                <p>translateX (in %)</p>
                                <input type="number" v-model="element.bubbleBackgrounds.rightBubbles.first.styles.imageMaskTranslateX">
                                
                                <p>scale (in %)</p>
                                <input type="number" step="0.1" v-model="element.bubbleBackgrounds.rightBubbles.first.styles.imageMaskScale">

                                <label :for="`nw-input-${element.id}-isVideoLightbox`">Use Video Lightbox</label><br />
                                <label class="toggle-switch">
                                    <input type="checkbox" :id="`nw-input-${element.id}-useImageVideoBackground`" v-model="element.bubbleBackgrounds.rightBubbles.first.isVideoLightbox">
                                    <span class="slider"></span>
                                </label>

                                <!-- <p><?php echo PLUGIN_ROOT_PATH ?> -->

                                <!-- <span  @click="resetHeroVideoData(element)">
                                    <p>resetHeroVideoData</p>
                                </span> -->

                                <div class="input-group" v-if="element.bubbleBackgrounds.rightBubbles.first.isVideoLightbox">
                                    <p>Choose Video</p>
                                    <span class="btn" @click="openMediaLibraryAndSaveUrlAndImage(element.bubbleBackgrounds.rightBubbles.first.videoSettings, 'video')">Video hinzufügen</span>
                                    <p>{{element.bubbleBackgrounds.rightBubbles.first.videoSettings?.url}}<p>
                                </div>
                                
                                <div class="input-group" v-if="element.bubbleBackgrounds.rightBubbles.first.isVideoLightbox">
                                    <p>Choose Thumbnail</p>
                                    <span class="btn" @click="openMediaLibraryAndSaveUrlAndImage(element.bubbleBackgrounds.rightBubbles.first.videoSettings.poster, 'image')">Thumbnail hinzufügen</span>
                                    <p>{{element.bubbleBackgrounds.rightBubbles.first.videoSettings?.poster?.url}}<p>
                                </div>

                                <!-- <p v-if="element.bubbleBackgrounds.rightBubbles.first.videoSettings">
                                    element.bubbleBackgrounds.rightBubbles.first.videoSettings: <br>
                                    {{element.bubbleBackgrounds.rightBubbles.first.videoSettings}}
                                </p> -->
                            </div>
                        </div>

                    </div>


                    <!-- Right Bubbles > second -->
                    <div class="bubble-data" v-if="element.uiValues.secondaryTabNumber === 2">
                        <!-- Content -->
                        <div class="grid-item" :class="{ 'show-add-image': !element.bubbleBackgrounds?.rightBubbles?.second }">
                            <div class="grid-item-inner-wrapper">
                                <div class="button-wrapper">
                                    <button 
                                        :id="`nw-background-${element.id}`" 
                                        type="button" 
                                        @click="openMediaLibraryAndSaveUrlAndImage(element.bubbleBackgrounds.rightBubbles.second)"
                                    >
                                        Bild auswählen
                                    </button>
                                    <button 
                                        v-if="element.bubbleBackgrounds?.rightBubbles?.second" 
                                        :id="`nw-background-${element.id}-delete`" 
                                        type="button" 
                                        @click="removeImageUrlAndIdFromElement(element.bubbleBackgrounds.rightBubbles.second)"
                                    >
                                        <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                            <path fill-rule="evenodd" d="M8.586 2.586A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4a2 2 0 0 1 .586-1.414ZM10 6h4V4h-4v2Zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Z" clip-rule="evenodd"/>
                                        </svg>
                                    </button>
                                </div>
                                <div class="grid-item-content">
                                    <div v-if="element.bubbleBackgrounds.rightBubbles.second" class="image-wrapper">
                                        <!-- <p>element.bubbleBackgrounds.rightBubbles.second: {{element.bubbleBackgrounds.rightBubbles.second}}</p> -->
                                        <!-- <img :src="element.bubbleBackgrounds.rightBubbles.second" alt="Icon" style="height: auto;" /> -->
                                        
                                        <svg class="active" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 116 116">
                                            <defs>
                                                <mask :id="`nw-element-${element.id}-left-bubble-second-mask`">
                                                    <!-- d="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z"  -->
                                                    <path 
                                                        class="popup-anim path-anim" 
                                                        d="M 8.5668 84.6681 c 9.7064 19.2027 24.9249 27.4879 45.6544 24.8556 c 20.7295 -2.6312 37.312 -12.232 49.7508 -28.8035 c 12.4377 -16.5704 12.4377 -33.1892 0 -49.8575 C 91.5343 14.1944 74.7065 4.1547 53.4897 0.7436 C 32.2718 -2.6686 17.0544 5.665 7.8353 25.7455 c -9.218 20.0794 -8.9738 39.721 0.7315 58.9226 Z"
                                                        data-path-to="M 8.5668 84.6681 c 9.7064 19.2027 24.9249 27.4879 45.6544 24.8556 c 20.7295 -2.6312 37.312 -12.232 49.7508 -28.8035 c 12.4377 -16.5704 12.4377 -33.1892 0 -49.8575 C 91.5343 14.1944 74.7065 4.1547 53.4897 0.7436 C 32.2718 -2.6686 17.0544 5.665 7.8353 25.7455 c -9.218 20.0794 -8.9738 39.721 0.7315 58.9226 Z" 
                                                        data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                                                        fill="#fff"
                                                    >
                                                    </path>
                                                </mask>
                                            </defs>
                                            <!-- Das Bild in ein eigenes SVG-Element wrappen -->
                                            <svg 
                                                xmlns="http://www.w3.org/2000/svg" 
                                                viewBox="0 0 116 116" 
                                                :mask="`url(#nw-element-${element.id}-left-bubble-second-mask)`" 
                                                style="width: 100%; height: auto; position: absolute; top: 0; left: 0;"
                                            >
                                                <image 
                                                    :xlink:href="element.bubbleBackgrounds.rightBubbles.second.url" 
                                                    :y="`${element.bubbleBackgrounds.rightBubbles.second.styles.imageMaskTranslateY}`" 
                                                    :x="`${element.bubbleBackgrounds.rightBubbles.second.styles.imageMaskTranslateX}`" 
                                                    :transform="`scale(${element.bubbleBackgrounds.rightBubbles.second.styles.imageMaskScale})`"
                                                    style="width: 100%;"
                                                >
                                                </image>
                                            </svg>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Settings -->
                        <div class="grid-item">
                            <div class="grid-item-settings" v-if="element.bubbleBackgrounds.rightBubbles.second?.styles">
                                <h2>Image Positioning</p>

                                <p>translateY (in %)</p>
                                <input type="number" v-model="element.bubbleBackgrounds.rightBubbles.second.styles.imageMaskTranslateY">

                                <p>translateX (in %)</p>
                                <input type="number" v-model="element.bubbleBackgrounds.rightBubbles.second.styles.imageMaskTranslateX">
                                

                                <p>scale (in %)</p>
                                <input type="number" step="0.1" v-model="element.bubbleBackgrounds.rightBubbles.second.styles.imageMaskScale">
                            </div>
                        </div>

                    </div>
                    

                    <!-- Right Bubbles > third -->
                    <div class="bubble-data" v-if="element.uiValues.secondaryTabNumber === 3">
                        <!-- Content -->
                        <div class="grid-item" :class="{ 'show-add-image': !element.bubbleBackgrounds?.rightBubbles?.third }">
                            <div class="grid-item-inner-wrapper">
                                <div class="button-wrapper">
                                    <button 
                                        :id="`nw-background-${element.id}`" 
                                        type="button" 
                                        @click="openMediaLibraryAndSaveUrlAndImage(element.bubbleBackgrounds.rightBubbles.third)"
                                    >
                                        Bild auswählen
                                    </button>
                                    <button 
                                        v-if="element.bubbleBackgrounds?.rightBubbles?.third" 
                                        :id="`nw-background-${element.id}-delete`" 
                                        type="button" 
                                        @click="removeImageUrlAndIdFromElement(element.bubbleBackgrounds.rightBubbles.third)"
                                    >
                                        <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                            <path fill-rule="evenodd" d="M8.586 2.586A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4a2 2 0 0 1 .586-1.414ZM10 6h4V4h-4v2Zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Z" clip-rule="evenodd"/>
                                        </svg>
                                    </button>
                                </div>
                                <div class="grid-item-content">
                                    <div v-if="element.bubbleBackgrounds.rightBubbles.third" class="image-wrapper">
                                        <!-- <p>element.bubbleBackgrounds.rightBubbles.third: {{element.bubbleBackgrounds.rightBubbles.third}}</p> -->
                                        <!-- <img :src="element.bubbleBackgrounds.rightBubbles.third" alt="Icon" style="height: auto;" /> -->
                                        
                                        <svg class="active" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 116 116">
                                            <defs>
                                                <mask :id="`nw-element-${element.id}-left-bubble-third-mask`">
                                                    <!-- d="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z"  -->
                                                    <path 
                                                        class="popup-anim path-anim" 
                                                        d="M 8.5668 84.6681 c 9.7064 19.2027 24.9249 27.4879 45.6544 24.8556 c 20.7295 -2.6312 37.312 -12.232 49.7508 -28.8035 c 12.4377 -16.5704 12.4377 -33.1892 0 -49.8575 C 91.5343 14.1944 74.7065 4.1547 53.4897 0.7436 C 32.2718 -2.6686 17.0544 5.665 7.8353 25.7455 c -9.218 20.0794 -8.9738 39.721 0.7315 58.9226 Z"
                                                        data-path-to="M 8.5668 84.6681 c 9.7064 19.2027 24.9249 27.4879 45.6544 24.8556 c 20.7295 -2.6312 37.312 -12.232 49.7508 -28.8035 c 12.4377 -16.5704 12.4377 -33.1892 0 -49.8575 C 91.5343 14.1944 74.7065 4.1547 53.4897 0.7436 C 32.2718 -2.6686 17.0544 5.665 7.8353 25.7455 c -9.218 20.0794 -8.9738 39.721 0.7315 58.9226 Z" 
                                                        data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                                                        fill="#fff"
                                                    >
                                                    </path>
                                                </mask>
                                            </defs>
                                            <!-- Das Bild in ein eigenes SVG-Element wrappen -->
                                            <svg 
                                                xmlns="http://www.w3.org/2000/svg" 
                                                viewBox="0 0 116 116" 
                                                :mask="`url(#nw-element-${element.id}-left-bubble-third-mask)`" 
                                                style="width: 100%; height: auto; position: absolute; top: 0; left: 0;"
                                            >
                                                <image 
                                                    :xlink:href="element.bubbleBackgrounds.rightBubbles.third.url" 
                                                    :y="`${element.bubbleBackgrounds.rightBubbles.third.styles.imageMaskTranslateY}`" 
                                                    :x="`${element.bubbleBackgrounds.rightBubbles.third.styles.imageMaskTranslateX}`" 
                                                    :transform="`scale(${element.bubbleBackgrounds.rightBubbles.third.styles.imageMaskScale})`"
                                                    style="width: 100%;"
                                                >
                                                </image>
                                            </svg>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Settings -->
                        <div class="grid-item">
                            <div class="grid-item-settings" v-if="element.bubbleBackgrounds.rightBubbles.third?.styles">
                                <h2>Image Positioning</p>

                                <p>translateY (in %)</p>
                                <input type="number" v-model="element.bubbleBackgrounds.rightBubbles.third.styles.imageMaskTranslateY">

                                <p>translateX (in %)</p>
                                <input type="number" v-model="element.bubbleBackgrounds.rightBubbles.third.styles.imageMaskTranslateX">
                                
                                <p>scale (in %)</p>
                                <input type="number" step="0.1" v-model="element.bubbleBackgrounds.rightBubbles.third.styles.imageMaskScale">
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>

        <!-- Spacing -->
        <div v-if="element.uiValues.activeTab === 'spacing'" class="advanced-settings-wrapper p-4">
            <!-- <p>element.uiValues: {{element.uiValues}}</p> -->
            <?php include PLUGIN_ROOT_PATH . 'components/advancedSettings/spacing-component.php'; ?>
        </div>
    </tab-transition-component>

</div>
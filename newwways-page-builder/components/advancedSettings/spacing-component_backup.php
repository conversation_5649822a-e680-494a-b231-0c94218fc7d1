<?php
echo "<p>Copyright &copy; 1999-" . date("Y") . " W3Schools.com</p>";
?>

<div v-if="element.uiValues.activeTab === 'spacing'">
    <p>spacing: {{element.advancedSettings}}</p>
    
    <hr>
    
    <!-- <p>element.advancedSettings.spacing: {{element.advancedSettings.spacing}}</p>
    <p>element.advancedSettings.spacing?.left: {{element.advancedSettings.spacing?.left}}</p> -->

    <hr>

    <div>
        <label for="`${element.id}-spacing-top`">Spacing top</label><br/>
        <select name="spacingTop" id="`${element.id}-spacing-top`" v-model="element.advancedSettings.spacing.top">
            <option v-for="(spacingTop, index) in advancedSettingsOptions.spacing.top" :value="spacingTop">{{spacingTop}}</option>
        </select>

        <div v-if="element.advancedSettings.spacing.top === 'custom'">
            <label for="`${element.id}-spacing-top`">Spacing top custom</label><br/>
            <input type="number" name="spacingTopCustom" id="`${element.id}-spacing-top-custom`" v-model="element.advancedSettings.spacing.topCustom">
            <!-- TODO: ggf. noch etwas für den Wert von top implementieren (py, %, rem, em, vm, vw) -->
        </div>
    </div>
    
    <div>
        <label for="`${element.id}-spacing-right`">Spacing right</label><br/>
        <select name="spacingRight" id="`${element.id}-spacing-right`" v-model="element.advancedSettings.spacing.right">
            <option v-for="(spacingRight, index) in advancedSettingsOptions.spacing.right" :value="spacingRight">{{spacingRight}}</option>
        </select>

        <div v-if="element.advancedSettings.spacing.right === 'custom'">
            <label for="`${element.id}-spacing-right`">Spacing right custom</label><br/>
            <input type="number" name="spacingRightCustom" id="`${element.id}-spacing-right-custom`" v-model="element.advancedSettings.spacing.rightCustom">
            <!-- TODO: ggf. noch etwas für den Wert von top implementieren (py, %, rem, em, vm, vw) -->
        </div>
    </div>
    
    <div>
        <label for="`${element.id}-spacing-bottom`">Spacing bottom</label><br/>
        <select name="spacingRight" id="`${element.id}-spacing-bottom`" v-model="element.advancedSettings.spacing.bottom">
            <option v-for="(spacingRight, index) in advancedSettingsOptions.spacing.bottom" :value="spacingRight">{{spacingRight}}</option>
        </select>

        <div v-if="element.advancedSettings.spacing.bottom === 'custom'">
            <label for="`${element.id}-spacing-bottom`">Spacing bottom custom</label><br/>
            <input type="number" name="spacingBottomCustom" id="`${element.id}-spacing-bottom-custom`" v-model="element.advancedSettings.spacing.bottomCustom">
            <!-- TODO: ggf. noch etwas für den Wert von top implementieren (py, %, rem, em, vm, vw) -->
        </div>
    </div>

    <div>
        <label for="`${element.id}-spacing-left`">Spacing left</label><br/>
        <select name="spacingLeft" id="`${element.id}-spacing-left`" v-model="element.advancedSettings.spacing.left">
            <option v-for="(spacingLeft, index) in advancedSettingsOptions.spacing.left" :value="spacingLeft">{{spacingLeft}}</option>
        </select>
    </div>

</div>
<div v-if="element.type === 'flexible-content'" class="flexible-content">
    <!-- <transition @enter="enter" @leave="leave"> -->
    <tab-transition-component 
        :active-tab-number="element.uiValues.activeTabNumber"
        @active-tab-changed="adjustItems"
    >
        <!-- Content -->
        <div v-if="element.uiValues.activeTabNumber === 1" class="content-wrapper p-4">
            <div class="block-content">
                <div class="col col-1" :class="{reverseOrder: element.reverseOrder}">
                    <div v-if="postType !== 'post'" class="default-fill-width-content">
                        <div>
                            <label :for="`nw-input-tagline-${element.id}`">tagline</label><br/>
                            <input type="text" :id="`nw-input-tagline-${element.id}`" v-model="element.tagline" />
                        </div>

                        <label :for="`nw-input-${element.id}`">Headline</label><br/>
                        <input type="text" :id="`nw-input-${element.id}`" v-model="element.headline" />

                        <!-- Include a tinyMCE editor -->
                        <label :for="`nw-input-${element.id}`">Absatz</label><br/>
                        <textarea :id="`nw-input-${element.id}`" v-model="element.paragraph"></textarea>
                        
                        <hr>
                    </div>

                    <!-- <div class="col-adjustment">
                        <select>
                            <option value="1" @click="adjustCols(element, 1, [12])">100%</option>
                            <option value="2" @click="adjustCols(element, 2, [6, 6])">50/50</option>
                            <option value="3" @click="adjustCols(element, 3, [4, 4, 4])">33/33/33</option>
                            <option value="4" @click="adjustCols(element, 4, [3, 3, 3, 3])">25/25/25/25</option>
                            <option value="5" @click="adjustCols(element, 5, [2, 2, 2, 2, 2])">20/20/20/20/20</option>
                            <option value="6" @click="adjustCols(element, 2, [8, 4])">66/33</option>
                            <option @click="adjustCols(element, 2, [4, 8])">33/66</option>
                            <option @click="adjustCols(element, 2, [9, 3])">75/25</option>
                            <option @click="adjustCols(element, 2, [3, 9])">25/75</option>
                            <option @click="adjustCols(element, 2, [5, 7])">40/60</option>
                            <option @click="adjustCols(element, 2, [7, 5])">60/40</option>
                        </select>
                    </div> -->

                    <div v-if="checkIfColSettingsExist(element)">
                        <div class="flex" style="justify-content: space-between; display: flex; margin-bottom: 1rem;">
                            <span class="btn" @click="element.uiValues.isColSelectOpen = !element.uiValues.isColSelectOpen">Adjust Columns</span>
                            <span v-if="arraysAreEqual(element.colSettings.map(col => col.width), [12])">100%</span>
                            <span v-if="arraysAreEqual(element.colSettings.map(col => col.width), [6, 6])">50/50</span>
                            <span v-if="arraysAreEqual(element.colSettings.map(col => col.width), [4, 4, 4])">33/33/33</span>
                            <span v-if="arraysAreEqual(element.colSettings.map(col => col.width), [3, 3, 3, 3])">25/25/25/25</span>
                            <span v-if="arraysAreEqual(element.colSettings.map(col => col.width), [3, 6, 3])">25/50/25</span>
                            <span v-if="arraysAreEqual(element.colSettings.map(col => col.width), [8, 4])">66/33</span>
                            <span v-if="arraysAreEqual(element.colSettings.map(col => col.width), [4, 8])">33/66</span>
                            <span v-if="arraysAreEqual(element.colSettings.map(col => col.width), [9, 3])">75/25</span>
                            <span v-if="arraysAreEqual(element.colSettings.map(col => col.width), [3, 9])">25/75</span>
                            <span v-if="arraysAreEqual(element.colSettings.map(col => col.width), [5, 7])">40/60</span>
                            <span v-if="arraysAreEqual(element.colSettings.map(col => col.width), [7, 5])">60/40</span>
                        </div>

                        <ul v-if="element.uiValues.isColSelectOpen" class="grid-button-wrapper">
                            <li @click="adjustCols(element, 1, [12])" :class="{active: arraysAreEqual(element.colSettings.map(col => col.width), [12])}">
                                <span>
                                    100%
                                </span>
                            </li>
                            <li @click="adjustCols(element, 2, [6, 6])" :class="{active: arraysAreEqual(element.colSettings.map(col => col.width), [6, 6])}">
                                <span>
                                    50/50
                                </span>
                            </li>
                            <li @click="adjustCols(element, 3, [4, 4, 4])" :class="{active: arraysAreEqual(element.colSettings.map(col => col.width), [4, 4, 4])}">
                                <span>
                                    33/33/33
                                </span>
                            </li>
                            <li @click="adjustCols(element, 4, [3, 3, 3, 3])" :class="{active: arraysAreEqual(element.colSettings.map(col => col.width), [3, 3, 3, 3])}">
                                <span>
                                    25/25/25/25
                                </span>
                            </li>
                            <li @click="adjustCols(element, 3, [3, 6, 3])" :class="{active: arraysAreEqual(element.colSettings.map(col => col.width), [3, 6, 3])}">
                                <span>
                                    25/50/25
                                </span>
                            </li>

                            <li @click="adjustCols(element, 2, [8, 4])" :class="{active: arraysAreEqual(element.colSettings.map(col => col.width), [8, 4])}">
                                <span>
                                    66/33
                                </span>
                            </li>

                            <li @click="adjustCols(element, 2, [4, 8])" :class="{active: arraysAreEqual(element.colSettings.map(col => col.width), [4, 8])}">
                                <span>
                                    33/66
                                </span>
                            </li>
                            
                            <li @click="adjustCols(element, 2, [9, 3])" :class="{active: arraysAreEqual(element.colSettings.map(col => col.width), [9, 3])}">
                                <span>
                                    3 zu 1
                                </span>
                            </li>
                            
                            <li @click="adjustCols(element, 2, [3, 9])" :class="{active: arraysAreEqual(element.colSettings.map(col => col.width), [3, 9])}">
                                <span>
                                    1 zu 3
                                </span>
                            </li>

                            <li @click="adjustCols(element, 2, [7, 5])" :class="{active: arraysAreEqual(element.colSettings.map(col => col.width), [7, 5])}">
                                <span>
                                    7 zu 5
                                </span>
                            </li>
                            
                            <li @click="adjustCols(element, 2, [5, 7])" :class="{active: arraysAreEqual(element.colSettings.map(col => col.width), [5, 7])}">
                                <span>
                                    5 zu 7
                                </span>
                            </li>
                        </ul>
                    </div>

                    <!-- <p>element.colSettings.map(col => col.width): {{element.colSettings.map(col => col.width)}}</p> -->

                    <!-- <p>element.flexibleContent: {{element.flexibleContent}}</p> -->

                    <!-- <ul v-if="element.content?.length" class="content-grid" :style="`grid-template-columns: repeat(${element.content?.length}, 1fr)`"> 
                        <li v-for="(content, index) in element.content"> -->
                    <!-- :group="{ name: 'flexibleContent', pull: 'clone', put: false }"  -->

                    <div class="content-grid-wrapper">
                    <!-- :style="`grid-template-columns: repeat(${element.flexibleContent?.length}, 1fr)`" -->
                        <!-- :group="{ name: 'flexibleContent', pull: 'clone', put: false }"  -->
                        <draggable 
                            v-if="element.flexibleContent?.length" 
                            v-model="element.flexibleContent" 
                            :group="{ name: 'flexibleContent', pull: false, put: false }" 
                            item-key="id" 
                            class="content-grid" 
                            :style="`grid-template-columns: ${element.colSettings ? element.colSettings.map(col => col.width ? `${col.width}fr` : '1fr').join(' ') : '1fr'}`"
                            @end="onDragEnd"
                            chosen-class="chosen"
                            handle=".col-drag-handle"
                        >
                            <template #item="{ element: flexibleContent, index }">
                                <div class="grid-col" >
                                    
                                    <div class="grid-col-header" style="display: flex; justify-content: space-between;">
                                        <div>
                                            <p style="padding: 0px; margin: 6px 0;">Spalte {{index + 1}}</p>
                                        </div>
                                        
                                        <div class="actions" style="display: flex;">
                                            <!-- <span>{{element.colSettings[index].width}}</span> -->
                                            <!-- <span v-if="element.flexibleContent.length > 1 && element.colSettings[index].width < 12" class="btn" @click="increaseColWidth(element, index)">{{element.colSettings[index].width}} +</span>
                                            <span v-if="element.flexibleContent.length > 1 && element.colSettings[index].width > 1" class="btn" @click="decreaseColWidth(element, index)">{{element.colSettings[index].width}} -</span> -->
                                            <span class="btn" @click="editCol(element.colSettings[index])">
                                                <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                    <path fill-rule="evenodd" d="M9.586 2.586A2 2 0 0 1 11 2h2a2 2 0 0 1 2 2v.089l.473.196.063-.063a2.002 2.002 0 0 1 2.828 0l1.414 1.414a2 2 0 0 1 0 2.827l-.063.064.196.473H20a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2h-.089l-.196.473.063.063a2.002 2.002 0 0 1 0 2.828l-1.414 1.414a2 2 0 0 1-2.828 0l-.063-.063-.473.196V20a2 2 0 0 1-2 2h-2a2 2 0 0 1-2-2v-.089l-.473-.196-.063.063a2.002 2.002 0 0 1-2.828 0l-1.414-1.414a2 2 0 0 1 0-2.827l.063-.064L4.089 15H4a2 2 0 0 1-2-2v-2a2 2 0 0 1 2-2h.09l.195-.473-.063-.063a2 2 0 0 1 0-2.828l1.414-1.414a2 2 0 0 1 2.827 0l.064.063L9 4.089V4a2 2 0 0 1 .586-1.414ZM8 12a4 4 0 1 1 8 0 4 4 0 0 1-8 0Z" clip-rule="evenodd"/>
                                                </svg>
                                            </span>
                                            <!-- <span v-if="element.flexibleContent.length > 1" class="btn col-drag-handle">☰</span> -->
                                            <span v-if="element.flexibleContent.length > 1" class="btn col-drag-handle">
                                                <!-- <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 4H4m0 0v4m0-4 5 5m7-5h4m0 0v4m0-4-5 5M8 20H4m0 0v-4m0 4 5-5m7 5h4m0 0v-4m0 4-5-5"/>
                                                </svg> -->
                                                <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                    <path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M5 7h14M5 12h14M5 17h14"/>
                                                </svg>
                                            </span>
                                        </div>
                                        <!-- <span class="btn" @click="removeCol(element, index)">X</span> -->
                                    </div>

                                    <!-- Include th col lightbox for col Settings -->
                                    <?php include PLUGIN_ROOT_PATH . 'components/flexible-content/colums/lightbox.php' ?>
                                    
                                    <!-- <p>
                                        element.colSettings[index]: <br>
                                        <span v-if="element.colSettings">{{element.colSettings[index]}}</span>
                                    </p> -->

                                    <div class="grid-content">
                                        <!-- <p>flexibleContent: {{flexibleContent}}</p> -->
                                        <ul :style="`align-items: ${element.verticalAlignment}`">
                                            <!-- :group="{ name: 'item', pull: 'clone', put: false }" -->
                                            <draggable 
                                                v-model="element.flexibleContent[index]" 
                                                :group="{ name: 'item', pull: true, put: true }"
                                                item-key="id" 
                                                @end="onDragEnd"
                                                handle=".item-drag-handle"
                                            >
                                                <template #item="{ element: item, index: itemIndex }">
                                                    <li class="flexible-content-item-wrapper">
                                                        <!-- <p>item: {{item}}</p>
                                                        <p>item.content: {{item.content}}</p> -->
                                                        
                                                        <!-- <div class="content-item-header flex">
                                                            <p>{{item.type}}</p>
                                                            <span @click="deleteContentItemFromBlock(element, index, itemIndex)" class="btn">X</span>
                                                        </div> -->


                                                        <!-- ////////////// -->
                                                        <!-- tinyMCE editor -->
                                                        <!-- ////////////// -->
                                                        <div v-if="item.type === 'textEditor'" class="text-editor">
                                                            <content-item-preview-component
                                                                :item="item"
                                                                :element="element"
                                                                :index="index"
                                                                :item-index="itemIndex"
                                                                :icon-svg="item.iconSvg"
                                                                :mce="true"
                                                                lightbox-title="Edit Text"
                                                                @delete-content-item="deleteContentItemFromBlock"
                                                            >
                                                                <template #default>
                                                                    <div v-if="item.content" class="preview-info">
                                                                        <div class="text-preview" v-html="item.content"></div>
                                                                    </div>
                                                                </template>
                                                            </content-item-preview-component>

                                                            <!-- Lightbox -->
                                                            <div v-if="element.uiValues.isAddNewItemLightboxOpen === `edit-content-item-${element.id}-${index}-${itemIndex}`" class="lightbox add-content-item">
                                                                <div class="lightbox-content edit-text">
                                                                    <div class="lightbox-header">
                                                                        <h3>Edit Text</h3>
                                                                        <span @click="element.uiValues.isAddNewItemLightboxOpen = false" class="btn">X</span>
                                                                    </div>
                                                                    <div class="lightbox-body">
                                                                        <!-- <p>index: {{index}} - itemIndex: {{itemIndex}}</p> -->
                                                                        <textarea 
                                                                            :id="'tinymce-' + element.id + '-' + index + '-' + itemIndex" 
                                                                            :class="'tinymce-' + element.id" 
                                                                            :data-flexible-content-index="index" 
                                                                            :data-item-index="itemIndex" 
                                                                            v-model="item.content">
                                                                        </textarea>
                                                                    </div>
                                                                    <div class="lightbox-footer">
                                                                        <button @click="element.uiValues.isAddNewItemLightboxOpen = false">Done</button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>


                                                        <!-- //// -->
                                                        <!-- CTAs -->
                                                        <!-- //// -->
                                                        <div v-if="item.type === 'CTAs'" class="ctas-editor">
                                                            <content-item-preview-component
                                                                :item="item"
                                                                :element="element"
                                                                :index="index"
                                                                :item-index="itemIndex"
                                                                :icon-svg="item.iconSvg"
                                                                lightbox-title="Edit CTAs"
                                                                @delete-content-item="deleteContentItemFromBlock"
                                                            >
                                                                <template #default>
                                                                    <div v-if="item.ctaArray" class="preview-info">
                                                                        <ul class="cta-list">
                                                                            <li v-for="(cta, ctaIndex) in item.ctaArray" :key="ctaIndex">
                                                                                <span class="cta">{{cta.content}}</span>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </template>
                                                            </content-item-preview-component>

                                                            <!-- Lightbox for editing the table -->
                                                            <div v-if="element.uiValues.isAddNewItemLightboxOpen === `edit-content-item-${element.id}-${index}-${itemIndex}`" class="lightbox edit-tabel">
                                                                <div class="lightbox-content">
                                                                    <div class="lightbox-header">
                                                                        <h3>Edit CTAs</h3>
                                                                        <span @click="element.uiValues.isAddNewItemLightboxOpen = false" class="btn">X</span>
                                                                    </div>
                                                                    <div class="lightbox-body">
                                                                        <label>Button Position</label>
                                                                        <select v-model="item.direction">
                                                                            <option v-for="direction in availableCtaDirections" :value="direction">{{direction}}</option>
                                                                        </select>
                                                                        
                                                                        <hr>

                                                                        <div v-for="(cta, index) in item.ctaArray" style="padding: 2rem; border: 1px solid #ccc; margin-bottom: 2rem; border-radius: 1rem;">
                                                                            <div class="flex-col">
                                                                                <div class="input-group">
                                                                                    <label>Text des Buttons</label>
                                                                                    <input type="text" v-model="item.ctaArray[index].content" />
                                                                                </div>
                                                    
                                                                                <div class="input-group">
                                                                                    <label>Use Modal</label>
                                                                                    <input type="checkbox" v-model="item.ctaArray[index].useModal" />
                                                                                </div>

                                                                                <div v-if="!item.ctaArray[index].useModal" class="input-group">
                                                                                    <label>Link Ziel</label>
                                                                                    <input type="text" v-model="item.ctaArray[index].link" />
                                                                                </div>

                                                                                <div  v-if="!item.ctaArray[index].useModal" class="input-group">
                                                                                    <label>In neuem Fenster öffnen</label>
                                                                                    <input type="checkbox" v-model="item.ctaArray[index].openInNewTab" />
                                                                                </div>

                                                                                <div v-if="item.ctaArray[index].useModal" class="input-group">
                                                                                    <label>Model ID</label>
                                                                                    <input type="text" v-model="item.ctaArray[index].modalId" />
                                                                                </div>

                                                                                <div class="input-group">
                                                                                    <label>Use Arrow</label>
                                                                                    <input type="checkbox" v-model="item.ctaArray[index].useArrow" />
                                                                                </div>

                                                                                <div class="input-group">
                                                                                    <label>Appearance</label>
                                                                                    <select v-model="item.ctaArray[index].appearance">
                                                                                        <option v-for="singleCtaAppearance in availableCtaAppearances" :value="singleCtaAppearance">{{singleCtaAppearance}}</option>
                                                                                    </select>
                                                                                </div>

                                                                                <span class="btn" @click="item.ctaArray.splice(index, 1)">X</span>

                                                                            </div>
                                                                        </div>

                                                                        <hr>

                                                                        <span class="btn" @click="addCta(item)">Add CTA</span>
                                                                        
                                                                            
                                                                    </div>


                                                                    <div class="lightbox-footer">
                                                                        <button @click="element.uiValues.isAddNewItemLightboxOpen = false">Done</button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>


                                                        <!-- //////////////// -->
                                                        <!-- Highlighted text -->
                                                        <!-- //////////////// -->
                                                        <div v-if="item.type === 'highlighted-text'" class="highlighted-text">
                                                            <content-item-preview-component
                                                                :item="item"
                                                                :element="element"
                                                                :index="index"
                                                                :item-index="itemIndex"
                                                                :icon-svg="item.iconSvg"
                                                                :mce="true"
                                                                lightbox-title="Edit highlighted Text"
                                                                @delete-content-item="deleteContentItemFromBlock"
                                                            >
                                                                <template #default>
                                                                    <div v-if="item.content" class="preview-info">
                                                                        <div 
                                                                            class="text-preview" 
                                                                            :style="`font-size: ${item.styleSettings.fontSize}; font-weight: ${item.styleSettings.fontWeight}; color: ${item.styleSettings.color}; text-align: ${item.styleSettings.textAlign};`"
                                                                            v-html="item.content"
                                                                        >
                                                                        </div>
                                                                    </div>
                                                                </template>
                                                            </content-item-preview-component>

                                                            <!-- Lightbox -->
                                                            <div v-if="element.uiValues.isAddNewItemLightboxOpen === `edit-content-item-${element.id}-${index}-${itemIndex}`" class="lightbox add-content-item">
                                                                <div class="lightbox-content">
                                                                    <div class="lightbox-header">
                                                                        <h3>Edit highlighted Text</h3>
                                                                        <span @click="element.uiValues.isAddNewItemLightboxOpen = false" class="btn">X</span>
                                                                    </div>

                                                                    <div class="lightbox-body">
                                                                        <!-- <textarea 
                                                                            :id="'tinymce-' + element.id + '-' + index + '-' + itemIndex" 
                                                                            :class="'tinymce-' + element.id" 
                                                                            :data-flexible-content-index="index" 
                                                                            :data-item-index="itemIndex" 
                                                                            v-model="item.content">
                                                                        </textarea> -->
                                                                        <h3>Content</h3>
                                                                        <label>Text</label>
                                                                        <input type="text" v-model="item.content">

                                                                        <div class="flex-row">
                                                                            <h3>Styles</h3>

                                                                            <div class="input-group">
                                                                                <label>Font Size</label>
                                                                                <input type="text" v-model="item.styleSettings.fontSize">
                                                                            </div>
                                                                            
                                                                            <div class="input-group">
                                                                                <label>Font Weight</label>
                                                                                <input type="text" v-model="item.styleSettings.fontWeight">
                                                                            </div>
                                                                            
                                                                            <div class="input-group">
                                                                                <label>Font Color</label>
                                                                                <input type="text" v-model="item.styleSettings.color">
                                                                            </div>

                                                                            <div class="input-group">
                                                                                <label>Text Alignment</label>
                                                                                <input type="text" v-model="item.styleSettings.textAlign">
                                                                            </div>
                                                                            
                                                                            <!-- <div class="input-group">
                                                                                <label>Use Gradient</label>
                                                                                <input type="checkbox" v-model="item.styleSettings.useGradient">
                                                                            </div> -->
                                                                        </div>


                                                                        <!-- Item Animation -->
                                                                        <div class="setting-section">
                                                                            <h3>Animation Setting</h3>
                                                                            
                                                                            <div class="flex-row">
                                                                                <div class="input-group">
                                                                                    <label>Use Animation</label>
                                                                                    <input type="checkbox" v-model="item.itemAnimationSettings.useAnimation">
                                                                                </div>
                                                                            </div>

                                                                            <div class="flex-row">
                                                                                <div v-if="item.itemAnimationSettings.useAnimation" class="input-group">
                                                                                    <label>animation Type</label>
                                                                                    <select v-model="item.itemAnimationSettings.animationType">
                                                                                        <option value="fadeIn">fade In</option>
                                                                                        <option value="fadeInUp">fade In Up</option>
                                                                                        <!-- <option value="fadeOut">fade Out</option>
                                                                                        <option value="zoomIn">zoom In</option>
                                                                                        <option value="zoomOut">zoom Out</option> -->
                                                                                    </select>
                                                                                </div>
                                                                            </div>
                                                                        </div>


                                                                        <!-- 
                                                                        <div class="flex-row">
                                                                            <h3>Text Effects</h3>

                                                                            <div class="input-group">
                                                                                <label>Use Text Effects</label>
                                                                                <input type="checkbox" v-model="item.textEffectSettings.useEffect">
                                                                            </div>

                                                                            <div v-if="item.textEffectSettings.useEffect" class="input-group">
                                                                                <label>Text Effect Type</label>
                                                                                <select v-model="item.textEffectSettings.effectType">
                                                                                    <option value="none">none</option>
                                                                                    <option value="blur">blur</option>
                                                                                    <option value="dropShadow">dropShadow</option>
                                                                                </select>
                                                                            </div>
                                                                        </div> 
                                                                        -->

                                                                        <div class="setting-section">
                                                                            <h3>Text Animation</h3>
                                                                            
                                                                            <div class="flex-row">
                                                                                <div class="input-group">
                                                                                    <label>Use Text Animation</label>
                                                                                    <input type="checkbox" v-model="item.textAnimationSettings.useAnimation">
                                                                                </div>
                                                                            </div>
                                                                            
                                                                            <div class="flex-row" v-if="item.textAnimationSettings.useAnimation">
                                                                                <div class="input-group">
                                                                                    <label>Text Animation Type</label>
                                                                                    <select v-model="item.textAnimationSettings.animationType">
                                                                                        <option value="none">none</option>
                                                                                        <option value="count">count</option>
                                                                                    </select>
                                                                                </div>

                                                                                <div class="input-group" v-if="item.textAnimationSettings.animationType === 'count'">
                                                                                    <label>animationStartValue</label>
                                                                                    <input type="number" v-model="item.textAnimationSettings.animationStartValue">
                                                                                </div>
                                                                    
                                                                            
                                                                                <div class="input-group">
                                                                                    <label>Text Animation Duration</label>
                                                                                    <input type="number" v-model="item.textAnimationSettings.animationDuration">
                                                                                </div>

                                                                                <div class="input-group">
                                                                                    <label>Text Animation Delay</label>
                                                                                    <input type="number" v-model="item.textAnimationSettings.animationDelay">
                                                                                </div>
                                                                            </div>


                                                                        </div>


                                                                        <div v-if="checkIfItemHasSpacingSettings(item)">
                                                                            <h2>Item Spacing</h2>
                                                                            
                                                                            <h3>Padding</h3>
                                                                            <div class="flex-row">
                                                                                <div class="input-group">
                                                                                    <label>Top:</label>
                                                                                    <input v-model="item.spacingSettings.padding.top" type="text" placeholder="0px" />
                                                                                </div>
                                                                                <div class="input-group">
                                                                                    <label>Right:</label>
                                                                                    <input v-model="item.spacingSettings.padding.right" type="text" placeholder="0px" />
                                                                                </div>
                                                                                <div class="input-group">
                                                                                    <label>Bottom:</label>
                                                                                    <input v-model="item.spacingSettings.padding.bottom" type="text" placeholder="0px" />
                                                                                </div>
                                                                                <div class="input-group">
                                                                                    <label>Left:</label>
                                                                                    <input v-model="item.spacingSettings.padding.left" type="text" placeholder="0px" />
                                                                                </div>
                                                                            </div>
                                                                        </div>


                                                                    </div>

                                                                    <div class="lightbox-footer">
                                                                        <button @click="element.uiValues.isAddNewItemLightboxOpen = false">Done</button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>


                                                        
                                                        <!-- ///// -->
                                                        <!-- image -->
                                                        <!-- ///// -->
                                                        <div v-if="item.type === 'image'" class="image-editor">
                                                            <content-item-preview-component
                                                                :item="item"
                                                                :element="element"
                                                                :index="index"
                                                                :item-index="itemIndex"
                                                                :icon-svg="item.iconSvg"
                                                                :mce="false"
                                                                lightbox-title="Edit Image Settings"
                                                                @delete-content-item="deleteContentItemFromBlock"
                                                            >
                                                                <template #default>
                                                                    <div v-if="!item.useImageComposition && item.mediaId && item.mediaUrl" class="preview-info media-wrapper">
                                                                        <div v-if="!item.styles.useImageMask" class="media-wrapper" :style="`text-align: ${item.styles.imageAlignment ? item.styles.imageAlignment : 'left'}`">
                                                                            <img 
                                                                                :src="item.mediaUrl" 
                                                                                :style="`
                                                                                    width: ${item.styles.width ? item.styles.width : '100%'}; 
                                                                                    height: ${item.styles.height ? item.styles.height : '100%'};
                                                                                    object-fit: ${item.styles.objectFit};
                                                                                `"
                                                                            />
                                                                            <p>{{ item.mediaId }}</p>
                                                                        </div>
                                                                        <svg 
                                                                            v-if="item.styles.useImageMask" 
                                                                            class="active" 
                                                                            xmlns="http://www.w3.org/2000/svg" 
                                                                            xmlns:xlink="http://www.w3.org/1999/xlink" 
                                                                            viewBox="0 0 116 116"
                                                                            style="max-width: 100%; max-height: unset; position: relative; z-index: 1;"
                                                                        >
                                                                            <defs>
                                                                                <mask id="mask-1">
                                                                                    <path 
                                                                                        class="popup-anim path-anim" 
                                                                                        d="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" 
                                                                                        data-path-to="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" 
                                                                                        data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                                                                                        fill="#fff" 
                                                                                        style="translate: none; rotate: none; scale: none; transform-origin: 0px 0px;" 
                                                                                        data-svg-origin="51 45.5" 
                                                                                        transform="matrix(1,0,0,1,0,0)">
                                                                                    </path>
                                                                                </mask>
                                                                            </defs>
                                                                            <!-- Das Bild in ein eigenes SVG-Element wrappen -->
                                                                            <svg 
                                                                                xmlns="http://www.w3.org/2000/svg" 
                                                                                viewBox="0 0 116 116" 
                                                                                mask="url(#mask-1)" 
                                                                                style="width: 100%; height: auto; position: absolute; top: 0; left: 0;"
                                                                            >
                                                                                <image 
                                                                                    :xlink:href="item.mediaUrl" 
                                                                                    :y="`${item.styles.imageMaskTranslateY}`" 
                                                                                    :x="`${item.styles.imageMaskTranslateX}`" 
                                                                                    :transform="`scale(${item.styles.imageMaskScale})`"
                                                                                    style="width: 100%;" 
                                                                                />
                                                                            </svg>
                                                                        </svg>
                                                                    </div>

                                                                    <div v-if="item.useImageComposition"  class="preview-info media-wrapper">
                                                                        <!-- include components/flexible-content/items/item-image/multi-image/preview.php -->
                                                                        <?php include PLUGIN_ROOT_PATH . 'components/flexible-content/items/item-image/multi-image/preview.php'; ?>
                                                                    </div>
                                                                </template>
                                                            </content-item-preview-component>

                                                            <!-- Lightbox -->
                                                            <div v-if="element.uiValues.isAddNewItemLightboxOpen === `edit-content-item-${element.id}-${index}-${itemIndex}`" class="lightbox add-content-item">
                                                                <div class="lightbox-content">
                                                                    <!-- Header -->
                                                                    <div class="lightbox-header">
                                                                        <h3>Edit Image Settings</h3>
                                                                        <span @click="element.uiValues.isAddNewItemLightboxOpen = false" class="btn">X</span>
                                                                    </div>
                                                                    <!-- Image Body -->
                                                                    <div class="lightbox-body">
                                                                        <!-- Pre-Settings -->
                                                                        <div class="lightbox-pre-settings" v-if="postType === 'page' || postType === 'erfolgsgeschichten'">
                                                                            <!-- use image mask -->
                                                                            <p>Use Image Composition</p>
                                                                            <label class="toggle-switch">
                                                                                <input type="checkbox" v-model="item.useImageComposition">
                                                                                <span class="slider"></span>
                                                                            </label>
                                                                        </div>

                                                                        <div v-if="!item.useImageComposition" class="image-editor grid" style="display: grid; grid-template-columns: 1fr 1fr; grid-gap: 2rem;">
                                                                            <!-- Single image preview -->
                                                                            <div class="image-preview">
                                                                                <!-- <label :for="`nw-input-asset-${element.id}-${index}`">asset</label><br/> -->
                                                                                <span class="btn" @click="attachMediaAsstes(item, index, itemIndex)">Choose Image</span>
                                                                                <div v-if="item.mediaId && item.mediaUrl" class="media-wrapper" :style="`text-align: ${item.styles.imageAlignment ? item.styles.imageAlignment : 'left'}`">
                                                                                    <img 
                                                                                        v-if="!item.styles.useImageMask" 
                                                                                        :src="item.mediaUrl" 
                                                                                        :style="`
                                                                                            width: ${item.styles.width ? item.styles.width : '100%'}; 
                                                                                            height: ${item.styles.height ? item.styles.height : '100%'};
                                                                                            object-fit: ${item.styles.objectFit};
                                                                                        `"
                                                                                    />
                                                                                    <svg 
                                                                                        v-if="item.styles.useImageMask" 
                                                                                        class="active" 
                                                                                        xmlns="http://www.w3.org/2000/svg" 
                                                                                        xmlns:xlink="http://www.w3.org/1999/xlink" 
                                                                                        viewBox="0 0 116 116"
                                                                                        style="max-width: 100%; position: relative; z-index: 1;"
                                                                                    >
                                                                                        <defs>
                                                                                            <mask id="mask-1">
                                                                                                <path 
                                                                                                    class="popup-anim path-anim" 
                                                                                                    d="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" 
                                                                                                    data-path-to="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" 
                                                                                                    data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                                                                                                    fill="#fff" 
                                                                                                    style="translate: none; rotate: none; scale: none; transform-origin: 0px 0px;" 
                                                                                                    data-svg-origin="51 45.5" 
                                                                                                    transform="matrix(1,0,0,1,0,0)">
                                                                                                </path>
                                                                                            </mask>
                                                                                        </defs>
                                                                                        <!-- Das Bild in ein eigenes SVG-Element wrappen -->
                                                                                        <svg 
                                                                                            xmlns="http://www.w3.org/2000/svg" 
                                                                                            viewBox="0 0 116 116" 
                                                                                            mask="url(#mask-1)" 
                                                                                            style="width: 100%; height: auto; position: absolute; top: 0; left: 0;"
                                                                                        >
                                                                                            <image 
                                                                                                :xlink:href="item.mediaUrl" 
                                                                                                :y="`${item.styles.imageMaskTranslateY}`" 
                                                                                                :x="`${item.styles.imageMaskTranslateX}`" 
                                                                                                :transform="`scale(${item.styles.imageMaskScale})`"
                                                                                                style="width: 100%;" 
                                                                                            />
                                                                                        </svg>
                                                                                    </svg>

                                                                                </div>
                                                                                <!-- <p>item.mediaId: {{ item.mediaId }}</p> -->

                                                                                <label :for="`nw-input-alt-${element.id}-${index}`">alt</label><br/>
                                                                                <input type="text" :id="`nw-input-alt-${element.id}-${index}`" v-model="item.alt" />

                                                                                <br>
                                                                                <br>
                                                                            
                                                                                <label :for="`nw-input-caption-${element.id}-${index}`">Caption</label><br/>
                                                                                <input type="text" :id="`nw-input-caption-${element.id}-${index}`" v-model="item.caption" />
                                                                            </div>

                                                                            <div class="image-controls">
                                                                                <!-- use image mask -->
                                                                                <p>use image mask</p>
                                                                                <label class="toggle-switch">
                                                                                    <input type="checkbox" v-model="item.styles.useImageMask">
                                                                                    <span class="slider"></span>
                                                                                </label>

                                                                                <div class="input-group">
                                                                                    <p>Width (include unit – e.g. "px" or "%")</p>
                                                                                    <input type="text" v-model="item.styles.width">
                                                                                </div>

                                                                                <div class="input-group">
                                                                                    <p>Height (include unit – e.g. "px" or "%")</p>
                                                                                    <input type="text" v-model="item.styles.height">
                                                                                </div>
                                                                                
                                                                                <div class="input-group">
                                                                                    <p>Object Fit</p>
                                                                                    <select v-model="item.styles.objectFit">
                                                                                        <option value="cover">cover</option>
                                                                                        <option value="contain">contain</option>
                                                                                    </select>
                                                                                </div>

                                                                                <!-- <div class="input-group">
                                                                                    <p>Center image</p>
                                                                                    <label class="toggle-switch">
                                                                                        <input type="checkbox" v-model="item.styles.centerImage">
                                                                                        <span class="slider"></span>
                                                                                    </label>
                                                                                </div> -->

                                                                                <div class="input-group">
                                                                                    <p>Image alignment</p>
                                                                                    <select v-model="item.styles.imageAlignment">
                                                                                        <option value="left">left</option>
                                                                                        <option value="center">center</option>
                                                                                        <option value="right">right</option>
                                                                                    </select>
                                                                                </div>


                                                                                <div v-if="item.styles.useImageMask">
                                                                                    <!-- TODO: implement mask selection -->
                                                                                    <!-- <p>choose mask</p>
                                                                                    <select v-model="item.styles.imageMask">
                                                                                        <option value="mask-1">mask 1</option>
                                                                                        <option value="mask-2">mask 2</option>
                                                                                        <option value="mask-3">mask 3</option>
                                                                                    </select> -->

                                                                                    <br>
                                                                                    <p>translateY (in %)</p>
                                                                                    <input type="number" v-model="item.styles.imageMaskTranslateY">

                                                                                    <br>
                                                                                    <p>translateX (in %)</p>
                                                                                    <input type="number" v-model="item.styles.imageMaskTranslateX">
                                                                                    
                                                                                    <br>
                                                                                    <p>scale (in %)</p>
                                                                                    <input type="number" step="0.1" v-model="item.styles.imageMaskScale">

                                                                                    <p>use image mask animation</p>
                                                                                    <label class="toggle-switch">
                                                                                        <input type="checkbox" v-model="item.styles.useImageMaskAnimation">
                                                                                        <span class="slider"></span>
                                                                                    </label>
                                                                                </div>


                                                                            </div>
                                                                        </div>


                                                                        <div v-if="item.useImageComposition">
                                                                            <h2>Images Composition</h2>

                                                                            <div class="image-editor grid" style="display: flex; grid-template-columns: 1fr 1fr; gap: 2rem;">

                                                                                <div class="left" style="transition: all 240ms ease; width: calc(50% - 1rem);">
                                                                                    
                                                                                    <!-- <div class="flex-row">
                                                                                        <span 
                                                                                            v-if="item.activeImageCompositionSettings > 0" 
                                                                                            @click="item.activeImageCompositionSettings -= 1" 
                                                                                            class="btn"
                                                                                        >
                                                                                            prev Item
                                                                                        </span>

                                                                                        <span 
                                                                                            v-if="item.activeImageCompositionSettings < item.imageComposition.length - 1" 
                                                                                            @click="item.activeImageCompositionSettings += 1"
                                                                                            class="btn"
                                                                                        >
                                                                                            next Item
                                                                                        </span>
                                                                                    </div> -->

                                                                                    <!-- <p> {{item.activeImageCompositionSettings}} – {{item.imageComposition.length}}</p> -->

                                                                                    <?php include PLUGIN_ROOT_PATH . 'components/flexible-content/items/item-image/multi-image/preview.php'; ?>

                                                                                    <div class="planes">
                                                                                        <!-- <div 
                                                                                            v-for="(imageCompositionItem, index) in item.imageComposition"
                                                                                            @click="item.activeImageCompositionSettings = index" 
                                                                                            class="image-composition-plane"
                                                                                            :class="{active: item.activeImageCompositionSettings === index}"
                                                                                        > -->
                                                                                        <draggable 
                                                                                            v-model="item.imageComposition"
                                                                                            :group="{ name: 'image-composition-group', pull: false, put: false }"
                                                                                            @end="onDragEnd"
                                                                                            :animation="200"
                                                                                            handle=".drag-handle"
                                                                                            tag="div"
                                                                                            :item-key="item => item.id || index"
                                                                                            class="plane-grid"
                                                                                        >
                                                                                            <template #item="{ element: imageCompositionItem, index }">
                                                                                                <div 
                                                                                                    @mouseup="item.activeImageCompositionSettings = index" 
                                                                                                    class="image-composition-plane"
                                                                                                    :class="{ active: item.activeImageCompositionSettings === index }"
                                                                                                >
                                                                                                    <div class="image-preview-and-name">
                                                                                                        <img 
                                                                                                            v-if="imageCompositionItem.type === 'singleImage'"
                                                                                                            :src="imageCompositionItem.mediaUrl ? imageCompositionItem.mediaUrl : '<?php echo PLUGIN_RELATIVE_PATH . 'placeholder/image-placeholder.png';?>'" 
                                                                                                            style="width: 64px; height: 64px; object-fit: contain; padding: 0; margin: 0; display: block"
                                                                                                        />
                                                                                                        <div 
                                                                                                            v-if="imageCompositionItem.type === 'singleColoredSvg'"
                                                                                                            style="width: 64px; height: 64px;"
                                                                                                        >
                                                                                                            <svg
                                                                                                                class="image-mask-svg active" 
                                                                                                                xmlns="http://www.w3.org/2000/svg" 
                                                                                                                xmlns:xlink="http://www.w3.org/1999/xlink" 
                                                                                                                viewBox="0 0 116 100"
                                                                                                                style="max-width: 100%; position: relative;"
                                                                                                                :style="`
                                                                                                                    z-index: ${imageCompositionItem.styles.zIndex};
                                                                                                                    opacity: ${imageCompositionItem.styles.opacity};
                                                                                                                    width: ${imageCompositionItem.styles.width}%;
                                                                                                                    height: ${imageCompositionItem.styles.height}%;
                                                                                                                    aspect-ratio: ${imageCompositionItem.styles.aspectRatio};
                                                                                                                    margin-top: ${imageCompositionItem.styles.marginTop}%;
                                                                                                                    margin-right: ${imageCompositionItem.styles.marginRight}%;
                                                                                                                    margin-bottom: -${imageCompositionItem.styles.marginTop}%;
                                                                                                                    margin-left: ${imageCompositionItem.styles.marginLeft}%;
                                                                                                                    transform: rotate(${imageCompositionItem.styles.imageMaskRotate}deg);
                                                                                                                `"
                                                                                                                :data-rotation-degrees="`${imageCompositionItem.styles.imageMaskRotate}`"
                                                                                                            >
                                                                                                                <defs>
                                                                                                                    <mask :id="`nw-${element.id}-${index}-mask-1`">
                                                                                                                        <path 
                                                                                                                            class="popup-anim path-anim" 
                                                                                                                            d="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" 
                                                                                                                            data-path-to="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" 
                                                                                                                            data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                                                                                                                            fill="#fff" 
                                                                                                                            style="translate: none; rotate: none; scale: none; transform-origin: 0px 0px;" 
                                                                                                                            data-svg-origin="51 45.5" 
                                                                                                                            transform="matrix(1,0,0,1,0,0)">
                                                                                                                        </path>
                                                                                                                    </mask>
                                                                                                                </defs>
                                                                                                                <!-- Das Bild in ein eigenes SVG-Element wrappen -->
                                                                                                                <svg 
                                                                                                                    xmlns="http://www.w3.org/2000/svg" 
                                                                                                                    viewBox="0 0 116 100" 
                                                                                                                    :mask="`url(#nw-${element.id}-${index}-mask-1)`" 
                                                                                                                    style="width: 100%; height: auto; position: absolute; top: 0; left: 0;"
                                                                                                                    @click="item.activeImageCompositionSettings = index"
                                                                                                                >
                                                                                                                    <rect 
                                                                                                                        width="100%" 
                                                                                                                        height="100%" 
                                                                                                                        fill="#ccc" 
                                                                                                                        :mask="`url(#nw-${element.id}-${index}-mask-1)`" 
                                                                                                                        :style="`fill: ${imageCompositionItem.styles.color};`"
                                                                                                                    ></rect>
                                                                                                                </svg>

                                                                                                                <svg 
                                                                                                                    v-if="item.activeImageCompositionSettings === index"
                                                                                                                    xmlns="http://www.w3.org/2000/svg" 
                                                                                                                    viewBox="0 0 116 100"
                                                                                                                    style="width: 100%; height: auto; position: absolute; top: 0; left: 0;"
                                                                                                                >
                                                                                                                    <path 
                                                                                                                        class="popup-anim path-anim" 
                                                                                                                        d="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" 
                                                                                                                        data-path-to="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" 
                                                                                                                        data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                                                                                                                        fill="none" 
                                                                                                                        stroke="yellow"
                                                                                                                        stroke-width="3"
                                                                                                                        style="translate: none; rotate: none; scale: none; transform-origin: 0px 0px;" 
                                                                                                                        data-svg-origin="51 45.5" 
                                                                                                                        transform="matrix(1,0,0,1,0,0)">
                                                                                                                    </path>
                                                                                                                </svg>
                                                                                                            </svg>
                                                                                                        </div>

                                                                                                        <div class="plane-name" @mouseover="imageCompositionItem.changeName = true" @mouseleave="imageCompositionItem.changeName = false">
                                                                                                            <p v-if="!imageCompositionItem.changeName">
                                                                                                                {{imageCompositionItem.name}}
                                                                                                            </p>
                                                                                                            <input 
                                                                                                                v-if="imageCompositionItem.changeName"
                                                                                                                type="text" v-model="imageCompositionItem.name" 
                                                                                                            />
                                                                                                        </div>
                                                                                                    </div>
                                                                                                        
                                                                                                    <div class="plane-actions">
                                                                                                        <span class="btn drag-handle">
                                                                                                            <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                                                                                <path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M5 7h14M5 12h14M5 17h14"/>
                                                                                                            </svg>
                                                                                                        </span>
                                                                                                        <span @click="removeItemFromImageComposition(item, index)" class="btn">
                                                                                                            <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                                                                                <path fill-rule="evenodd" d="M8.586 2.586A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4a2 2 0 0 1 .586-1.414ZM10 6h4V4h-4v2Zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Z" clip-rule="evenodd"/>
                                                                                                            </svg>
                                                                                                        </span>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </template>
                                                                                        </draggable>
                                                                                    </div>


                                                                                    <div style="display: flex; justify-content: space-between;">
                                                                                        <div>
                                                                                            <span @click="addItemToImageComposition(item, 'singleImage')" class="btn">Bild hinzufügen</span>
                                                                                        </div>
                                                                                        <div>
                                                                                            <span @click="addItemToImageComposition(item, 'coloredSvg')" class="btn">Farbige Fläche hinzufügen</span>
                                                                                        </div>
                                                                                    </div>

                                                                                </div>


                                                                                <div class="right" style="flex-grow: 1;">
                                                                                    <div v-for="(imageCompositionItem, index) in item.imageComposition">
                                                                                        <div v-if="item.activeImageCompositionSettings === index">
                                                                                            <div class="flex-row full-row" style="margin-bottom: 1rem;">
                                                                                                <h2>Ebene {{index + 1}} – {{ imageCompositionItem.name }}</h2>
                                                                                                
                                                                                                <!-- <span @click="removeItemFromImageComposition(item, index)" class="btn">X</span> -->
                                                                                            </div>

                                                                                            <p>
                                                                                                {{imageCompositionItem.mediaUrl}}
                                                                                            </p>


                                                                                            <div v-if="imageCompositionItem.type === 'singleImage'" class="flex-row">
                                                                                                <p>Image Quelle</p>
                                                                                                <span class="btn" @click="openMediaLibraryAndSaveUrlAndImage(imageCompositionItem, 'image', 'mediaId', 'mediaUrl')">Bild auswählen</span>
                                                                                                <span class="btn" v-if="imageCompositionItem.mediaUrl" @click="removeImageFromElement(imageCompositionItem, 'mediaId', 'mediaUrl')">Bild entfernen</span>
                                                                                            </div>

                                                                                            <div class="genaral-svg-settings">
                                                                                                <p>Background Color ({{imageCompositionItem.styles.color}})</p>
                                                                                                <input type="color" v-model="imageCompositionItem.styles.color">
                                                                                                
                                                                                                <br>

                                                                                                <p>opacity ({{imageCompositionItem.styles.opacity}})</p>
                                                                                                <input type="range" v-model="imageCompositionItem.styles.opacity" min="0" max="1" step="0.01" value="1">


                                                                                                <p>Width</p>
                                                                                                <input type="number" v-model="imageCompositionItem.styles.width">
                                                                                                <br>

                                                                                                <!-- <p>heigth</p>
                                                                                                <input  type="number" v-model="imageCompositionItem.styles.height">
                                                                                                <br> -->

                                                                                                <!-- <p>Aspect Ratio</p>
                                                                                                <input type="text" v-model="imageCompositionItem.aspectRatio">
                                                                                                <br> -->

                                                                                                <p>z-index (Vordergrund/Hintergrund)</p>
                                                                                                <input  type="number" v-model="imageCompositionItem.styles.zIndex">
                                                                                                <br>


                                                                                                <div class="flex-row">
                                                                                                    <div class="input-group ">
                                                                                                        <p>Top</p>
                                                                                                        <input type="number" v-model="item.imageComposition[index].styles.marginTop">
                                                                                                    </div>

                                                                                                    <!-- <div class="input-group ">
                                                                                                        <p>right</p>
                                                                                                        <input type="number" v-model="item.imageComposition[index].styles.marginRight">
                                                                                                    </div> -->

                                                                                                    <!-- <div class="input-group ">
                                                                                                        <p>bottom</p>
                                                                                                        <input type="number" v-model="item.imageComposition[index].styles.marginBottom">
                                                                                                    </div> -->

                                                                                                    <div class="input-group ">
                                                                                                        <p>left</p>
                                                                                                        <input type="number" v-model="item.imageComposition[index].styles.marginLeft">
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>


                                                                                            <!-- use mask -->
                                                                                            <div v-if="imageCompositionItem.type === 'singleImage'">
                                                                                                <div class="flex-col">
                                                                                                    <div class="input-group full-row">
                                                                                                        <p>use mask</p>
                                                                                                        <label class="toggle-switch">
                                                                                                            <input type="checkbox" v-model="imageCompositionItem.styles.useImageMask">
                                                                                                            <span class="slider"></span>
                                                                                                        </label>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>

                                                                                            <div v-if="imageCompositionItem.styles.useImageMask">

                                                                                                <!-- <div class="flex-col">
                                                                                                    <div class="input-group full-row">
                                                                                                        <p>choose mask</p>
                                                                                                        <select v-model="imageCompositionItem.styles.imageMask">
                                                                                                            <option value="mask-1">mask 1</option>
                                                                                                            <option value="mask-2">mask 2</option>
                                                                                                            <option value="mask-3">mask 3</option>
                                                                                                        </select>
                                                                                                    </div>
                                                                                                </div> -->

                                                                                                <div v-if="imageCompositionItem.type === 'singleImage'" class="image-transformation-settings">
                                                                                                    <div class="flex-row">
                                                                                                        <div class="input-group">
                                                                                                            <p>translateY (in %)</p>
                                                                                                            <input type="number" v-model="imageCompositionItem.styles.imageMaskTranslateY">
                                                                                                        </div>

                                                                                                        <div class="input-group">
                                                                                                            <p>translateX (in %)</p>
                                                                                                            <input type="number" v-model="imageCompositionItem.styles.imageMaskTranslateX">
                                                                                                        </div>
                                                                                                    </div>
                                                                                                    
                                                                                                    <div class="flex-row">
                                                                                                        <div class="input-group full-row">
                                                                                                            <p>scale (in %)</p>
                                                                                                            <input type="number" step="0.1" v-model="imageCompositionItem.styles.imageMaskScale">
                                                                                                        </div>
                                                                                                        
                                                                                                        <div class="input-group full-row">
                                                                                                            <p>Rotate in °</p>
                                                                                                            <input type="range" id="angle-slider" min="0" max="360" v-model="imageCompositionItem.styles.imageMaskRotate">
                                                                                                        </div>
                                                                                                    </div>
                                                                                                </div>

                                                                                                <div v-if="imageCompositionItem.type !== 'singleImage'" class="flex-row">
                                                                                                    <div class="input-group full-row">
                                                                                                        <p>Rotate in °</p>
                                                                                                        <input type="range" id="angle-slider" min="0" max="360" v-model="imageCompositionItem.styles.imageMaskRotate">
                                                                                                    </div>
                                                                                                </div>

                                                                                                <div class="flex-col">
                                                                                                    <div class="input-group full-row">
                                                                                                        <p>use image mask animation</p>
                                                                                                        <label class="toggle-switch">
                                                                                                            <input type="checkbox" v-model="imageCompositionItem.styles.useImageMaskAnimation">
                                                                                                            <span class="slider"></span>
                                                                                                        </label>
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>


                                                                                            <div class="flex-col">
                                                                                                <div class="input-group full-row">
                                                                                                    <p>Use as Video Teaser</p>
                                                                                                    <label class="toggle-switch">
                                                                                                        <input type="checkbox" v-model="imageCompositionItem.isVideoTeaser">
                                                                                                        <span class="slider"></span>
                                                                                                    </label>
                                                                                                </div>
                                                                                            </div>

                                                                                            <div v-if="imageCompositionItem.isVideoTeaser" class="flex-col">
                                                                                                <div class="input-group full-row">
                                                                                                    <button 
                                                                                                        :id="`nw-background-${element.id}`" 
                                                                                                        type="button" 
                                                                                                        @click="openMediaLibraryAndSaveUrlAndImage(imageCompositionItem.videoData)"
                                                                                                    >
                                                                                                        Video auswählen
                                                                                                    </button>
                                                                                                    <div v-if="imageCompositionItem.videoData">
                                                                                                        videoData: {{imageCompositionItem.videoData}}
                                                                                                    </div>
                                                                                                </div>
                                                                                            </div>
                                                                                            
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>

                                                                        </div>


                                                                        <!-- <hr> -->
                                                                        <!-- <p>item: {{item}}</p> -->
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>


                                                        <!-- ///// -->
                                                        <!-- Tabel -->
                                                        <!-- ///// -->
                                                        <div v-if="item.type === 'tabel'" class="tabel-editor">
                                                            <content-item-preview-component
                                                                :item="item"
                                                                :element="element"
                                                                :index="index"
                                                                :item-index="itemIndex"
                                                                :icon-svg="item.iconSvg"
                                                                lightbox-title="Edit Table"
                                                                @delete-content-item="deleteContentItemFromBlock"
                                                            >
                                                                <template #default>
                                                                    <div v-if="item.rows" class="preview-info">
                                                                        <table>
                                                                            <tr v-for="(row, rowIndex) in item.rows" :key="rowIndex">
                                                                                <td v-for="(column, colIndex) in row.columns" :key="colIndex">
                                                                                    <div class="cell-content" v-html="column.content"></div>
                                                                                </td>
                                                                            </tr>
                                                                        </table>


                                                                        <!-- <div class="table-grid">
                                                                            <div v-for="(row, rowIndex) in item.rows" :key="rowIndex" class="flex-row">
                                                                                <div v-for="(column, colIndex) in row.columns" :key="colIndex" class="flex-cell">
                                                                                <div class="cell-content" v-html="column.content"></div>
                                                                                </div>
                                                                            </div>
                                                                        </div> -->
                                                                    </div>
                                                                </template>
                                                            </content-item-preview-component>

                                                            

                                                            <!-- Lightbox for editing the table -->
                                                            <div v-if="element.uiValues.isAddNewItemLightboxOpen === `edit-content-item-${element.id}-${index}-${itemIndex}`" class="lightbox edit-tabel">
                                                                <div class="lightbox-content">
                                                                    <div class="lightbox-header">
                                                                        <h3>Edit Table</h3>
                                                                        <span @click="element.uiValues.isAddNewItemLightboxOpen = false" class="btn">X</span>
                                                                    </div>
                                                                    <div class="lightbox-body">
                                                                        <div class="tabel-controls cols" style="display: flex; gap: 1rem;">
                                                                            <span style="width: 54px;"></span>
                                                                            <span v-for="(column, colIndex) in item.rows[0].columns" :key="colIndex" @click="removeColumn(item, colIndex)" class="btn" style="flex-grow: 1;">
                                                                                <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                                                    <path fill-rule="evenodd" d="M8.586 2.586A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4a2 2 0 0 1 .586-1.414ZM10 6h4V4h-4v2Zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Z" clip-rule="evenodd"/>
                                                                                </svg>
                                                                                Column {{ colIndex + 1 }}
                                                                            </span>
                                                                        </div>

                                                                        <div v-for="(row, rowIndex) in item.rows" :key="rowIndex" class="tabel-row" style="display: flex; gap: 1rem; width: fit-content;">
                                                                            <!-- remove row -->
                                                                            <div class="remove-row-btn-wrapper">
                                                                                <span @click="removeRow(item, rowIndex)" class="btn" style="flex-grow: 1;">
                                                                                    <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                                                        <path fill-rule="evenodd" d="M8.586 2.586A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4a2 2 0 0 1 .586-1.414ZM10 6h4V4h-4v2Zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Z" clip-rule="evenodd"/>
                                                                                    </svg>
                                                                                </span>
                                                                            </div>
                                                                            <div v-for="(column, colIndex) in row.columns" :key="colIndex" class="tabel-column" style="flex-grow: 1;">
                                                                                <div class="col-content">
                                                                                    <textarea v-model="column.content" placeholder="Content here"></textarea>
                                                                                </div>
                                                                            </div>
                                                                        </div>    
                                                                        
                                                                        <!-- Table Controls -->
                                                                        <div class="table-controls" style="display: flex; gap: 1rem; margin-bottom: 1rem;">
                                                                            <span class="btn" @click="addRow(item)">Add Row</span>
                                                                            <span class="btn" @click="addColumn(item)">Add Column</span>
                                                                        </div>
                                                                    
                                                                    </div>


                                                                    <div class="lightbox-footer">
                                                                        <button @click="element.uiValues.isAddNewItemLightboxOpen = false">Done</button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>


                                                        <!-- ///////// -->
                                                        <!-- Accordion -->
                                                        <!-- ///////// -->
                                                        <div v-if="item.type === 'accordion'" class="accordion">
                                                            <!-- Preview -->
                                                            <content-item-preview-component
                                                                :item="item"
                                                                :element="element"
                                                                :index="index"
                                                                :item-index="itemIndex"
                                                                :icon-svg="item.iconSvg"
                                                                :mce="false"
                                                                lightbox-title="Edit Image Settings"
                                                                @delete-content-item="deleteContentItemFromBlock"
                                                            >
                                                                <template #default>
                                                                    <div v-if="item.accordionItems?.length" class="preview-info">
                                                                        <ul> 
                                                                            <li v-for="(accordionItem, index) in item.accordionItems" class="single-accordion-item">
                                                                                <h4>{{accordionItem.question}}</h4>
                                                                                <p>{{ truncateText(accordionItem.answer, 20) }}</p>
                                                                            </li>
                                                                        </ul>
                                                                    </div>
                                                                </template>
                                                            </content-item-preview-component>

                                                            <!-- Lightbox -->
                                                            <div v-if="element.uiValues.isAddNewItemLightboxOpen === `edit-content-item-${element.id}-${index}-${itemIndex}`" class="lightbox add-content-item">
                                                                <div class="lightbox-content">
                                                                    <div class="lightbox-header">
                                                                        <h3>Edit Text</h3>
                                                                        <span @click="element.uiValues.isAddNewItemLightboxOpen = false" class="btn">X</span>
                                                                    </div>
                                                                    <div class="lightbox-body">
                                                                        <div class="col">
                                                                            <ul v-if="item.accordionItems?.length"> 
                                                                                <li v-for="(accordionItem, index) in item.accordionItems" class="single-accordion-item">
                                                                                    <div class="flex" style="display: flex; gap: 1rem;">
                                                                                        <!-- Content -->
                                                                                        <div class="accordion-item-content" style="flex-grow: 1">
                                                                                            <!-- <label :for="`nw-question-${element.id}-${item.id}-${index}`"></label><br/> -->
                                                                                            <input type="text" placeholder="Title / Question" :id="`nw-question-${element.id}-${item.id}-${index}`" v-model="accordionItem.question" style="margin-bottom: 1rem;"/>

                                                                                            <!-- <p>accordionItem.question: {{accordionItem.question}}</p> -->

                                                                                            <!-- <label :for="`nw-answer-${element.id}-${item.id}-${index}`"></label><br/> -->
                                                                                            <textarea type="text" :id="`nw-answer-${element.id}-${item.id}-${index}`" v-model="accordionItem.answer"></textarea>
                                                                                        </div>

                                                                                        <!-- Actions -->
                                                                                        <div class="item-actions">
                                                                                            <!-- Delete button -->
                                                                                            <span @click="deleteAccordionItem(item, index)" class="btn">X</span>
                                                                                        </div>
                                                                                    </div>

                                                                                    
                                                                                    <!-- <p>accordionItem.answer: {{accordionItem.answer}}</p> -->

                                                                                </li>
                                                                            </ul>

                                                                            <span @click="addAccordionItem(item)" class="btn">Add new Accordion item</span>
                                                                            <!-- <p>raw module: {{ module }}</p> -->
                                                                        </div>
                                                                    </div>
                                                                    <div class="lightbox-footer">
                                                                        <button @click="element.uiValues.isAddNewItemLightboxOpen = false">Done</button>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        
                                                        <!-- ////////////// -->
                                                        <!-- Icon with Text -->
                                                        <!-- ////////////// -->
                                                        <div v-if="item.type === 'icon-with-text-old'" class="text-editor">
                                                            <content-item-preview-component
                                                                :item="item"
                                                                :element="element"
                                                                :index="index"
                                                                :item-index="itemIndex"
                                                                :icon-svg="item.iconSvg"
                                                                :mce="true"
                                                                lightbox-title="Edit Icon with Text Settings"
                                                                @delete-content-item="deleteContentItemFromBlock"
                                                            >
                                                                <template #default>
                                                                    <div v-if="item.mediaUrl || item.svgCode || item.content" class="preview-info" style="display: flex; gap: 1rem;">
                                                                        <div v-if="item.mediaUrl && !item.useSvgCode" style="max-width: 64px;">
                                                                            <!-- <p>item.mediaUrl: {{item.mediaUrl}}</p> -->
                                                                            <img :src="item.mediaUrl" />
                                                                            <!-- <p>item URL from fetchImageUrl: {{fetchImageUrl(item.id)}}</p> -->
                                                                        </div>

                                                                        <div v-if="item.svgCode && item.useSvgCode">
                                                                            <p>item.svgCode: {{item.svgCode}}</p>
                                                                        </div>

                                                                        <div class="text">
                                                                            <div v-html="item.content"></div>
                                                                        </div>

                                                                        <!-- <ul>
                                                                            <li v-for="iconWidthText in item.iconsWithText">
                                                                                {{iconWidthText}}
                                                                            </li>
                                                                        </ul> -->
                                                                    </div>
                                                                </template>
                                                            </content-item-preview-component>

                                                            
                                                            <!-- <div class="content-item-preview-wrapper">
                                                                <div class="content-type">
                                                                    <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                                        <path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M9 8h10M9 12h10M9 16h10M4.99 8H5m-.02 4h.01m0 4H5"/>
                                                                    </svg>
                                                                    <div class="preview-info">
                                                                        <p><strong>Name: {{item.name}}</strong></p>
                                                                        <p>index: {{index}} - itemIndex: {{itemIndex}}</p>
                                                                    </div>
                                                                </div>

                                                                <div class="content-item-actions">
                                                                    <span class="btn" @click="element.uiValues.isAddNewItemLightboxOpen = `edit-content-item-${element.id}-${index}-${itemIndex}`, initTinyMCE()">
                                                                        <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m14.304 4.844 2.852 2.852M7 7H4a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-4.5m2.409-9.91a2.017 2.017 0 0 1 0 2.853l-6.844 6.844L8 14l.713-3.565 6.844-6.844a2.015 2.015 0 0 1 2.852 0Z"/>
                                                                        </svg>
                                                                    </span>

                                                                    <span class="btn" @click="deleteContentItemFromBlock(element, index, itemIndex)">
                                                                        Delete
                                                                    </span>
                                                                </div>
                                                            </div> -->

                                                            <!-- Lightbox -->
                                                            <div v-if="element.uiValues.isAddNewItemLightboxOpen === `edit-content-item-${element.id}-${index}-${itemIndex}`" class="lightbox add-content-item">
                                                                <div class="lightbox-content">
                                                                    <div class="lightbox-header">
                                                                        <h3>Add icon and text</h3>
                                                                        <span @click="element.uiValues.isAddNewItemLightboxOpen = false" class="btn">X</span>
                                                                    </div>
                                                                    <div class="lightbox-body">
                                                                        <div class="image-editor grid" style="display: grid; grid-template-columns: 1fr 1fr; grid-gap: 2rem;">
                                                                            <!-- image preview -->
                                                                            <div class="image-preview">
                                                                                <!-- Use Image -->
                                                                                <div v-if="!item.useSvgCode">
                                                                                    <label :for="`nw-input-asset-${element.id}-${index}`">asset</label><br/>
                                                                                    <span class="btn" @click="attachMediaAsstes(item, index, itemIndex)">Choose Image</span>
                                                                                    <div v-if="item.mediaId && item.mediaUrl" class="media-wrapper">
                                                                                        <img v-if="!item.useSvgCode" :src="item.mediaUrl" />
                                                                                    </div>

                                                                                    <label :for="`nw-input-alt-${element.id}-${index}`">alt text</label><br/>
                                                                                    <input type="text" :id="`nw-input-alt-${element.id}-${index}`" v-model="item.alt" />
                                                                                </div>
                                                                                <!-- use SVG Code -->
                                                                                <div v-if="item.useSvgCode">
                                                                                    <p>SVG Code</p>
                                                                                    <textarea v-model="item.svgCode"></textarea>
                                                                                </div>
                                                                            </div>
                                                                            <div class="icon-text">
                                                                                <!-- tinyMCE editor -->
                                                                                <p>index: {{index}} - itemIndex: {{itemIndex}}</p>
                                                                                <textarea 
                                                                                    :id="'tinymce-' + element.id + '-' + index + '-' + itemIndex" 
                                                                                    :class="'tinymce-' + element.id" 
                                                                                    :data-flexible-content-index="index" 
                                                                                    :data-item-index="itemIndex" 
                                                                                    v-model="item.content">
                                                                                </textarea>
                                                                                <!-- <textarea v-model="item.text"></textarea> -->
                                                                            </div>
                                                                        </div>

                                                                        <div class="image-editor grid" style="display: grid; grid-template-columns: 1fr; grid-gap: 2rem;">
                                                                            <p>use SVG code</p>
                                                                            <label class="toggle-switch">
                                                                                <input type="checkbox" v-model="item.useSvgCode">
                                                                                <span class="slider"></span>
                                                                            </label>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>


                                                        <!-- ////////////// -->
                                                        <!-- Icon with Text -->
                                                        <!-- ////////////// -->
                                                        <div v-if="item.type === 'icon-with-text'" class="icon-with-text">
                                                            <content-item-preview-component
                                                                :item="item"
                                                                :element="element"
                                                                :index="index"
                                                                :item-index="itemIndex"
                                                                :icon-svg="item.iconSvg"
                                                                :mce="true"
                                                                lightbox-title="Edit Icon with Text Settings"
                                                                @delete-content-item="deleteContentItemFromBlock"
                                                            >
                                                                <template #default>
                                                                    <div v-if="item.mediaUrl || item.svgCode || item.content" class="preview-info" style="display: flex; gap: 1rem;">
                                                                        <div v-if="item.mediaUrl && !item.useSvgCode" style="max-width: 64px;">
                                                                            <!-- <p>item.mediaUrl: {{item.mediaUrl}}</p> -->
                                                                            <img :src="item.mediaUrl" />
                                                                            <!-- <p>item URL from fetchImageUrl: {{fetchImageUrl(item.id)}}</p> -->
                                                                        </div>

                                                                        <div v-if="item.svgCode && item.useSvgCode">
                                                                            <p>item.svgCode: {{item.svgCode}}</p>
                                                                        </div>

                                                                        <div class="text">
                                                                            <div v-html="item.content"></div>
                                                                        </div>

                                                                        <!-- <ul>
                                                                            <li v-for="iconWidthText in item.iconsWithText">
                                                                                {{iconWidthText}}
                                                                            </li>
                                                                        </ul> -->
                                                                    </div>
                                                                </template>
                                                            </content-item-preview-component>

                                                            
                                                            <!-- <div class="content-item-preview-wrapper">
                                                                <div class="content-type">
                                                                    <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                                        <path stroke="currentColor" stroke-linecap="round" stroke-width="2" d="M9 8h10M9 12h10M9 16h10M4.99 8H5m-.02 4h.01m0 4H5"/>
                                                                    </svg>
                                                                    <div class="preview-info">
                                                                        <p><strong>Name: {{item.name}}</strong></p>
                                                                        <p>index: {{index}} - itemIndex: {{itemIndex}}</p>
                                                                    </div>
                                                                </div>

                                                                <div class="content-item-actions">
                                                                    <span class="btn" @click="element.uiValues.isAddNewItemLightboxOpen = `edit-content-item-${element.id}-${index}-${itemIndex}`, initTinyMCE()">
                                                                        <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m14.304 4.844 2.852 2.852M7 7H4a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-4.5m2.409-9.91a2.017 2.017 0 0 1 0 2.853l-6.844 6.844L8 14l.713-3.565 6.844-6.844a2.015 2.015 0 0 1 2.852 0Z"/>
                                                                        </svg>
                                                                    </span>

                                                                    <span class="btn" @click="deleteContentItemFromBlock(element, index, itemIndex)">
                                                                        Delete
                                                                    </span>
                                                                </div>
                                                            </div> -->

                                                            <!-- Lightbox -->
                                                            <div v-if="element.uiValues.isAddNewItemLightboxOpen === `edit-content-item-${element.id}-${index}-${itemIndex}`" class="lightbox add-content-item">
                                                                <div class="lightbox-content">
                                                                    <div class="lightbox-header">
                                                                        <h3>Add icon and text</h3>
                                                                        <span @click="element.uiValues.isAddNewItemLightboxOpen = false" class="btn">X</span>
                                                                    </div>
                                                                    <div class="lightbox-body">
                                                                        <div class="image-editor grid" style="display: grid; grid-template-columns: 1fr 1fr; grid-gap: 2rem;">
                                                                            <!-- image preview -->
                                                                            <div class="image-preview">
                                                                                <!-- Use Image -->
                                                                                <div v-if="!item.useSvgCode">
                                                                                    <label :for="`nw-input-asset-${element.id}-${index}`">asset</label><br/>
                                                                                    <span class="btn" @click="attachMediaAsstes(item, index, itemIndex)">Choose Image</span>
                                                                                    <div v-if="item.mediaId && item.mediaUrl" class="media-wrapper">
                                                                                        <img v-if="!item.useSvgCode" :src="item.mediaUrl" />
                                                                                    </div>

                                                                                    <label :for="`nw-input-alt-${element.id}-${index}`">alt text</label><br/>
                                                                                    <input type="text" :id="`nw-input-alt-${element.id}-${index}`" v-model="item.alt" />
                                                                                </div>
                                                                                <!-- use SVG Code -->
                                                                                <div v-if="item.useSvgCode">
                                                                                    <p>SVG Code</p>
                                                                                    <textarea v-model="item.svgCode"></textarea>
                                                                                </div>
                                                                            </div>
                                                                            <div class="icon-text">
                                                                                <!-- tinyMCE editor -->
                                                                                <p>index: {{index}} - itemIndex: {{itemIndex}}</p>
                                                                                <textarea 
                                                                                    :id="'tinymce-' + element.id + '-' + index + '-' + itemIndex" 
                                                                                    :class="'tinymce-' + element.id" 
                                                                                    :data-flexible-content-index="index" 
                                                                                    :data-item-index="itemIndex" 
                                                                                    v-model="item.content">
                                                                                </textarea>
                                                                                <!-- <textarea v-model="item.text"></textarea> -->
                                                                            </div>
                                                                        </div>

                                                                        <div class="image-editor grid" style="display: grid; grid-template-columns: 1fr; grid-gap: 2rem;">
                                                                            <p>use SVG code</p>
                                                                            <label class="toggle-switch">
                                                                                <input type="checkbox" v-model="item.useSvgCode">
                                                                                <span class="slider"></span>
                                                                            </label>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>


                                                        <!-- ///////// -->
                                                        <!-- HTML Form -->
                                                        <!-- ///////// -->
                                                        <div v-if="item.type === 'html-form'" class="html-form">
                                                            <content-item-preview-component
                                                                :item="item"
                                                                :element="element"
                                                                :index="index"
                                                                :item-index="itemIndex"
                                                                :icon-svg="item.iconSvg"
                                                                :mce="true"
                                                                lightbox-title="Choose HTML-Form"
                                                                @delete-content-item="deleteContentItemFromBlock"
                                                            >
                                                                <template #default>
                                                                    <div class="preview-info">
                                                                        <!-- <p>Form ID: {{item.htmlFormId}}</p> -->
                                                                        <div v-for="form in availableForms" :key="form.id">
                                                                            <div v-if="form.id === item.htmlFormId" class="form-preview">
                                                                                <h3>{{form.title}}</h3>
                                                                                <p>{{form.description}}</p>
                                                                                <!-- <p>Form ID: {{form.id}}</p> -->
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                </template>
                                                            </content-item-preview-component>

                                                        
                                                            <!-- Lightbox -->
                                                            <div v-if="element.uiValues.isAddNewItemLightboxOpen === `edit-content-item-${element.id}-${index}-${itemIndex}`" class="lightbox add-content-item">
                                                                <div class="lightbox-content">
                                                                    <div class="lightbox-header">
                                                                        <h3>Choose HTML-Form</h3>
                                                                        <span @click="element.uiValues.isAddNewItemLightboxOpen = false" class="btn">X</span>
                                                                    </div>
                                                                    <div class="lightbox-body">
                                                                        <div class="form-select py-4">
                                                                            <select v-model="item.htmlFormId">
                                                                                <option value="0">No Form</option>
                                                                                <option v-for="(form, index) in availableForms" :value="form.id">{{form.title}}</option>
                                                                            </select>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>


                                                        <!-- if type is headline or paragraph -->
                                                        <div v-if="item.type === 'headline' || item.type === 'paragraph'">
                                                            <div class="content-item-preview-wrapper">
                                                                <!-- Actions -->
                                                                <div class="content-item-actions-wrapper">
                                                                    <div class="content-item-actions">
                                                                        <!-- Edit Button -->
                                                                        <span class="btn" @click="element.uiValues.isAddNewItemLightboxOpen = `edit-content-item-${element.id}-${index}-${itemIndex}`, logIndices(index, itemIndex), initTinyMCE()">
                                                                            <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m14.304 4.844 2.852 2.852M7 7H4a1 1 0 0 0-1 1v10a1 1 0 0 0 1 1h11a1 1 0 0 0 1-1v-4.5m2.409-9.91a2.017 2.017 0 0 1 0 2.853l-6.844 6.844L8 14l.713-3.565 6.844-6.844a2.015 2.015 0 0 1 2.852 0Z"/>
                                                                            </svg>
                                                                        </span>

                                                                        <!-- delete Button -->
                                                                        <span class="btn" @click="deleteContentItemFromBlock(element, index, itemIndex)">
                                                                            <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                                                <path fill-rule="evenodd" d="M8.586 2.586A2 2 0 0 1 10 2h4a2 2 0 0 1 2 2v2h3a1 1 0 1 1 0 2v12a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V8a1 1 0 0 1 0-2h3V4a2 2 0 0 1 .586-1.414ZM10 6h4V4h-4v2Zm1 4a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Zm4 0a1 1 0 1 0-2 0v8a1 1 0 1 0 2 0v-8Z" clip-rule="evenodd"/>
                                                                            </svg>
                                                                        </span>
                                                                    </div>
                                                                </div>

                                                                <!-- content item preview -->
                                                                <div class="content-item-preview-body">
                                                                    <div class="preview-info">
                                                                        <!-- <div class="text-preview" v-html="item.content"></div> -->
                                                                        <label :for="`nw-input-${element.id}-${index}`">{{item.type}}</label><br/>
                                                                        <input type="text" :id="`nw-input-${element.id}-${index}`" v-model="item.content" />
                                                                    </div>
                                                                </div>
                                                                
                                                            </div>

                                                            
                                                        </div>
                                                        
                                                        <!-- list -->
                                                        <div v-if="item.type === 'list'">
                                                            <ul>
                                                                <li v-for="(item, itemIndex) in item.listItems">
                                                                    <div class="item-list-header flex">
                                                                        <label :for="`nw-input-${element.id}-${index}-${itemIndex}`">Item</label><br/>
                                                                        <span @click="deleteListItem(element, index, itemIndex)" class="btn">X</span>
                                                                    </div>
                                                                    <input type="text" :id="`nw-input-${element.id}-${index}-${itemIndex}`" v-model="item.content" />
                                                                </li>
                                                                <li v-if="item.listItems?.length === 0">No List Items</li>
                                                            </ul>
                                                            <span @click="addListItem(item.listItems)" class="btn">add list item</span>
                                                        </div>

                                                        <!-- button -->
                                                        <div class="inner-element" v-if="item.type === 'button'">
                                                            <!-- Button text -->
                                                            <label :for="`nw-input-${element.id}-${index}`">{{item.type}} text</label><br/>
                                                            <input type="text" :id="`nw-input-${element.id}-${index}`" v-model="item.content" />
                                                            
                                                            <!-- Button Link -->
                                                            <label :for="`nw-input-${element.id}-${index}`">{{item.type}} Link</label><br/>
                                                            <input type="text" :id="`nw-input-${element.id}-${index}`" v-model="item.link" />

                                                            <!-- useModal Switch -->
                                                            <p>useModal: {{item.useModal}}</p>
                                                            <label class="toggle-switch">
                                                                <input type="checkbox" v-model="element.useImage">
                                                                <span class="slider"></span>
                                                            </label>
                                                        </div>
                                                        
                                                    </template>
                                                </li>
                                            </draggable>
                                        </ul>
                                    </div>
                                    <div class="add-content-item-wrapper">
                                        <span class="btn" @click="element.uiValues.isAddNewItemLightboxOpen = `add-content-item-${element.id}-${index}`">Add Content Item</span>
                                    </div>


                                    <div v-if="element.uiValues.isAddNewItemLightboxOpen === `add-content-item-${element.id}-${index}`" class="lightbox add-content-item">
                                        <div class="lightbox-content">
                                            <div class="lightbox-header">
                                                <h3>Add Content Item</h3>
                                                <span class="btn" @click="element.uiValues.isAddNewItemLightboxOpen = false">Close</span>
                                            </div>

                                            <div class="lightbox-body">
                                                <div class="grid" style="grid-template-columns: 1fr 1fr;">
                                                    <span @click="addContentItemToBlock(element, index, 'textEditor')" class="btn">
                                                        <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6.2V5h11v1.2M8 5v14m-3 0h6m2-6.8V11h8v1.2M17 11v8m-1.5 0h3"/>
                                                        </svg>
                                                        <p>add Text</p>
                                                    </span>
                                                    <span @click="addContentItemToBlock(element, index, 'image')" class="btn">
                                                        <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m3 16 5-7 6 6.5m6.5 2.5L16 13l-4.286 6M14 10h.01M4 19h16a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1H4a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1Z"/>
                                                        </svg>
                                                        <p>add Image</p>
                                                    </span>
                                                    <span @click="addContentItemToBlock(element, index, 'tabel')" class="btn">
                                                        <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 9h6m-6 3h6m-6 3h6M6.996 9h.01m-.01 3h.01m-.01 3h.01M4 5h16a1 1 0 0 1 1 1v12a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V6a1 1 0 0 1 1-1Z"/>
                                                        </svg>
                                                        <p>add Tabel</p>
                                                    </span>

                                                    <!-- Only in Pages -->
                                                    <!-- TODO: include a script in the PageBuilder admin Script, wich allows to enable different items on different postTypes-->

                                                    <span v-if="postType === 'page'" @click="addContentItemToBlock(element, index, 'highlighted-text')" class="btn">
                                                        <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                            <path fill-rule="evenodd" d="M18.458 3.11A1 1 0 0 1 19 4v16a1 1 0 0 1-1.581.814L12 16.944V7.056l5.419-3.87a1 1 0 0 1 1.039-.076ZM22 12c0 1.48-.804 2.773-2 3.465v-6.93c1.196.692 2 1.984 2 3.465ZM10 8H4a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h6V8Zm0 9H5v3a1 1 0 0 0 1 1h3a1 1 0 0 0 1-1v-3Z" clip-rule="evenodd"/>
                                                        </svg>
                                                        <p>add highlighted Text</p>
                                                    </span>


                                                    <span v-if="postType === 'page' || postType === 'erfolgsgeschichten'" @click="addContentItemToBlock(element, index, 'CTAs')" class="btn">
                                                        <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                            <path d="M8.597 3.2A1 1 0 0 0 7.04 4.289a3.49 3.49 0 0 1 .057 1.795 3.448 3.448 0 0 1-.84 1.575.999.999 0 0 0-.077.094c-.596.817-3.96 5.6-.941 10.762l.03.049a7.73 7.73 0 0 0 2.917 2.602 7.617 7.617 0 0 0 3.772.829 8.06 8.06 0 0 0 3.986-.975 8.185 8.185 0 0 0 3.04-2.864c1.301-2.2 1.184-4.556.588-6.441-.583-1.848-1.68-3.414-2.607-4.102a1 1 0 0 0-1.594.757c-.067 1.431-.363 2.551-.794 3.431-.222-2.407-1.127-4.196-2.224-5.524-1.147-1.39-2.564-2.3-3.323-2.788a8.487 8.487 0 0 1-.432-.287Z"/>
                                                        </svg>
                                                        <p>add CTAs</p>
                                                    </span>
                                                    
                                                    
                                                    <span v-if="postType === 'page' || postType === 'erfolgsgeschichten'" @click="addContentItemToBlock(element, index, 'html-form')" class="btn">
                                                        <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                            <path fill-rule="evenodd" d="M8 7V2.221a2 2 0 0 0-.5.365L3.586 6.5a2 2 0 0 0-.365.5H8Zm2 0V2h7a2 2 0 0 1 2 2v.126a5.087 5.087 0 0 0-4.74 1.368v.001l-6.642 6.642a3 3 0 0 0-.82 1.532l-.74 3.692a3 3 0 0 0 3.53 3.53l3.694-.738a3 3 0 0 0 1.532-.82L19 15.149V20a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9h5a2 2 0 0 0 2-2Z" clip-rule="evenodd"/>
                                                            <path fill-rule="evenodd" d="M17.447 8.08a1.087 1.087 0 0 1 1.187.238l.002.001a1.088 1.088 0 0 1 0 1.539l-.377.377-1.54-1.542.373-.374.002-.001c.1-.102.22-.182.353-.237Zm-2.143 2.027-4.644 4.644-.385 1.924 1.925-.385 4.644-4.642-1.54-1.54Zm2.56-4.11a3.087 3.087 0 0 0-2.187.909l-6.645 6.645a1 1 0 0 0-.274.51l-.739 3.693a1 1 0 0 0 1.177 1.176l3.693-.738a1 1 0 0 0 .51-.274l6.65-6.646a3.088 3.088 0 0 0-2.185-5.275Z" clip-rule="evenodd"/>
                                                        </svg>
                                                        <p>add HTML Forms</p>
                                                    </span>




                                                    <!-- <span @click="addContentItemToBlock(element, index, 'headline')" class="btn">add headline</span> -->
                                                    <!-- <span @click="addContentItemToBlock(element, index, 'paragraph')" class="btn">add paragraph</span> -->
                                                    <!-- <span @click="addContentItemToBlock(element, index, 'list')" class="btn">add list</span> -->
                                                    <span v-if="postType === 'page'" @click="addContentItemToBlock(element, index, 'icon-with-text')" class="btn">add icon with text</span>
                                                    <span v-if="postType === 'page'" @click="addContentItemToBlock(element, index, 'button')" class="btn">add button</span>
                                                    <span v-if="postType === 'page'" @click="addContentItemToBlock(element, index, 'accordion')" class="btn">add accordion</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>

                            </template>
                        </draggable>
                    </div>
                    <!-- <p>raw element: {{ element }}</p> -->

                </div>
            </div>
        </div>
    
        <div v-if="element.uiValues.activeTabNumber === 2" class="appearance-wrapper p-4">
            <h2>Appearance Settings</h2>

            <!-- advancedSettings (custom classes) -->
            <p>Appearance:</p>
            <!-- <p>Appearance: {{element.advancedSettings.appearance}}</p> -->
            <select v-model="element.advancedSettings.appearance">
                <option v-for="advancedSettingsAppearanceOption in advancedSettingsOptions.appearance" :value="advancedSettingsAppearanceOption">{{advancedSettingsAppearanceOption}}</option>
                <!-- <option value="normal">top</option>
                <option value="center">middle</option>
                <option value="flex-end">bottom</option> -->
            </select>

            
            <!-- <p>advancedSettings: <br>{{element.advancedSettings}}</p> -->

            <!-- Vertical alignment -->
            <div class="input-group">
                <p>Vertcal alignment:</p>
                <select v-model="element.verticalAlignment">
                    <option value="normal">top</option>
                    <option value="center">middle</option>
                    <option value="flex-end">bottom</option>
                </select>
            </div>

            <div class="input-group">
                <p>equal height columns:</p>
                <label class="toggle-switch">
                    <input type="checkbox" v-model="element.equalHeightColumns">
                    <span class="slider"></span>
                </label>
            </div>

            <div v-if="element.flexibleContent.length > 1">
                <p>Reverse columns on mobile (<= 999px):</p>
                <label class="toggle-switch">
                    <input type="checkbox" v-model="element.reverseColumnsOnMobile">
                    <span class="slider"></span>
                </label>
            </div>
        </div>
    
        <div v-if="element.uiValues.activeTabNumber === 3" class="advanced-settings-wrapper p-4">
            <h2>Spacing</h2>
            <!-- <p>element.uiValues: {{element.uiValues}}</p> -->
            <?php include PLUGIN_ROOT_PATH . 'components/advancedSettings/spacing-component.php'; ?>
        </div>
    </tab-transition-component>
    <!-- </transition> -->

</div>
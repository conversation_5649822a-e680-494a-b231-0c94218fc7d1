<div v-if="element.type === 'timeline-section'" class="timeline-section">
    <!-- Content -->
    <div v-if="element.uiValues.activeTab === 'content'" class="content-wrapper p-4">
        <div class="block-content">
            <div class="grid">
                <div>
                    <label :for="`nw-input-tagline-${element.id}`">Tagline</label><br/>
                    <input type="text" :id="`nw-input-tagline-${element.id}`" v-model="element.tagline" />
                </div>

                <div>
                    <label :for="`nw-input-heading-${element.id}`">Heading</label><br/>
                    <input type="text" :id="`nw-input-heading-${element.id}`" v-model="element.heading" />
                </div>

                <div>
                    <label :for="`nw-input-paragraph-${element.id}`">Absatz</label><br/>
                    <textarea :id="`nw-input-paragraph-${element.id}`" v-model="element.paragraph" /></textarea>
                </div>
            </div>

            <item-tab-component 
                v-model="element.timelineItems" 
                :element="element" 
                @add-new-item="addTimelineItem(element)" 
                @delete-item="deleteTimelineItem(element, $event)"
            >
                <template #default="{ item, index }">
                    <!-- Variabler Inhalt für Testimonials -->
                    <div>
                        <div class="text-col">
                            <label :for="`nw-input-title-${element.id}-${index}`">Zeit</label><br/>
                            <input type="text" :id="`nw-input-title-${element.id}-${index}`" v-model="item.time" />

                            <label :for="`nw-input-name-${element.id}-${index}`">Content</label><br/>
                            <input type="text" :id="`nw-input-name-${element.id}-${index}`" v-model="item.content" />
                        </div>
                    </div>
                </template>
            </item-tab-component>
        </div>
    </div>


    <!-- Appearance -->
    <div v-if="element.uiValues.activeTab === 'appearance'" class="content-wrapper p-4">
        <h2>Appearance</h2>
        <p>Nothing here yet</p>
    </div>

    <!-- Spacing -->
    <div v-if="element.uiValues.activeTab === 'spacing'" class="content-wrapper p-4">
        <?php include PLUGIN_ROOT_PATH . 'components/advancedSettings/spacing-component.php'; ?>
    </div>
    
</div>
<!-- End timeline section -->
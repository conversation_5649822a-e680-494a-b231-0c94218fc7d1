<!-- Text Image Modul -->
<div v-if="module.type === 'accordion'" class="accordion">
    <div class="block-content">
        <div class="col">
            <ul v-if="module.accordionItems?.length"> 
                <li v-for="(accordionItem, index) in module.accordionItems">
                    <!-- Delete button -->
                    <span @click="deleteAccordionItem(module, index)" class="btn">X</span>

                    <label :for="`nw-question-${element.id}-${module.id}-${index}`"></label><br/>
                    <input type="text" :id="`nw-question-${element.id}-${module.id}-${index}`" v-model="accordionItem.question" />

                    <!-- <p>accordionItem.question: {{accordionItem.question}}</p> -->

                    <label :for="`nw-answer-${element.id}-${module.id}-${index}`"></label><br/>
                    <textarea type="text" :id="`nw-answer-${element.id}-${module.id}-${index}`" v-model="accordionItem.answer"></textarea>
                    
                    <!-- <p>accordionItem.answer: {{accordionItem.answer}}</p> -->

                </li>
            </ul>

            <span @click="addAccordionItem(module)" class="btn">Add new accordion item</span>

            <!-- <p>raw module: {{ module }}</p> -->

        </div>
    </div>

    <!-- TODO: add advanced Settings & enable accordion styles dropdown/select -->

    <!-- Switch sides -->
    <!-- <p>Reverse Order: {{element.reverseOrder}}</p>
    <label class="toggle-switch">
        <input type="checkbox" v-model="element.reverseOrder">
        <span class="slider"></span>
    </label> -->
</div>
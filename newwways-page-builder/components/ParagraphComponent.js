// ParagraphComponent.js
export default {
	props: {
		// Empfängt den Text als Prop
		element: {
			type: Object,
		},
	},
	template: `
    <div class="paragraph">
        <p>Element: {{ element }}</p>
        <input type="text" plaxceholder="whatever" v-model="element.content">
    </div>
  `,
	// methods: {
	// 	updateContent(newContent) {
	// 		// Eine Methode, um den Inhalt zu aktualisieren und das Update zu emittieren
	// 		this.$emit("update-content", newContent)
	// 	},
	// },
}

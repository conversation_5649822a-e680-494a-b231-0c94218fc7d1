<div v-if="element.type === 'testimonial-section'" class="testimonial-section">
    <tab-transition-component :active-tab-number="element.uiValues.activeTabNumber">
        <!-- Content -->
        <div v-if="element.uiValues.activeTab === 'content'" class="content-wrapper p-4">
            <div class="grid">
                <div>
                    <div>
                        <label :for="`nw-input-tagline-${element.id}`">Tagline</label><br/>
                        <input type="text" :id="`nw-input-tagline-${element.id}`" v-model="element.tagline" />
                    </div>
                    <div>
                        <label :for="`nw-input-heading-${element.id}`">Heading</label><br/>
                        <input type="text" :id="`nw-input-heading-${element.id}`" v-model="element.heading" />
                    </div>
                    <div>
                        <label :for="`nw-input-content-${element.id}`">Content</label><br/>
                        <textarea :id="`nw-input-content-${element.id}`" v-model="element.content"></textarea>
                    </div>
                </div>

                
                <item-tab-component 
                    v-model="element.testimonials" 
                    :element="element" 
                    @add-new-item="addTestimonialItem(element)" 
                    @delete-item="deleteTestimonial(element, $event)">
                    <template #default="{ item, index }">
                        <!-- Inhalt für Testimonials -->
                        <div>
                            <!-- Bild hinzufügen -->
                            <div class="image-dependend-styles" style="display: grid; justify-content: space-between; grid-template-columns: 1fr 1fr; gap: 2rem;">
                                <div class="image-preview">
                                    <!-- <div v-if="item.image" style="display: flex; justify-content: center;">
                                        <img :src="item.image" alt="Uploaded Image" style="max-width: 240px; height: auto;" />
                                    </div> -->
                                    <svg 
                                        v-if="item.image"
                                        class="active" 
                                        xmlns="http://www.w3.org/2000/svg" 
                                        xmlns:xlink="http://www.w3.org/1999/xlink" 
                                        viewBox="0 0 116 100"
                                        style="max-width: 240px; position: relative; z-index: 1;"
                                    >
                                        <defs>
                                            <mask :id="`${element.id}-${index}-mask-1`">
                                                <path 
                                                    class="popup-anim path-anim" 
                                                    d="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" 
                                                    data-path-to="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" 
                                                    data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                                                    fill="#fff" 
                                                    style="translate: none; rotate: none; scale: none; transform-origin: 0px 0px;" 
                                                    data-svg-origin="51 45.5" 
                                                    transform="matrix(1,0,0,1,0,0)">
                                                </path>
                                            </mask>
                                        </defs>
                                        <svg 
                                            xmlns="http://www.w3.org/2000/svg" 
                                            viewBox="0 0 116 100" 
                                            :mask="`url(#${element.id}-${index}-mask-1)`" 
                                            style="width: 100%; height: auto; position: absolute; top: 0; left: 0;"
                                        >
                                            <rect 
                                                width="100%" 
                                                height="100%" 
                                                fill="#ccc"
                                                :fill="`${item.image.styles.backgroundColor}`"
                                                :style="`fill: ${item.image.styles.backgroundColor};`"
                                            ></rect>
                                            <image 
                                                :xlink:href="item.image.url" 
                                                :y="`${item.image.styles.imageTranslateY}%`" 
                                                :x="`${item.image.styles.imageTranslateX}%`" 
                                                :transform="`scale(${item.image.styles.imageScale})`"
                                                style="width: 100%;" 
                                            />
                                            <!-- rotate(-${item.image.styles.rotateMask ? item.image.styles.rotateMask : 0}deg) -->
                                        </svg>
                                    </svg>

                                    <div class="input-group">
                                        <div style="width: 100%; display: flex; justify-content: space-between;">
                                            <span class="btn" @click="openMediaLibraryAndSaveUrlAndImage(item.image)">Bild auswählen</span>
                                            <span class="btn" @click="removeImageUrlAndIdFromElement(item.image)">Bild entfernen</span>
                                        </div>
                                    </div>

                                </div>

                                    
                                <div class="image-settings">

                                    <div class="input-group">
                                        <label>Alt Text</label><br/>
                                        <input type="text" v-model=" item.image.altText" />
                                    </div>
                                    
                                    <div style="display: flex; justify-content: space-between;">
                                        <div class="input-group">
                                            <label>Image Translate X</label><br/>
                                            <input type="number" step="0.5" v-model="item.image.styles.imageTranslateX" />
                                        </div>
                                        
                                        <div class="input-group">
                                            <label>Image Translate Y</label><br/>
                                            <input type="number" step="0.5" v-model="item.image.styles.imageTranslateY" />
                                        </div>
                                    </div>

                                    <div class="input-group">
                                        <label>Image Scale</label><br/>
                                        <input type="number" step="0.01" v-model="item.image.styles.imageScale" />
                                    </div>
                                    
                                    <div class="input-group">
                                        <p>Background Color ({{ item.image.styles.backgroundColor }})</p>
                                        <input type="color" v-model="item.image.styles.backgroundColor">
                                    </div>

                                    <!-- select mask -->
                                    <!-- <p>Select Image Mask</p>
                                    <select :id="`nw-input-item-mask-${element.id}-${index}`" v-model="item.image.styles.imageMask">
                                        <option v-for="mask in maskStyles" :value="mask">{{ mask }}</option>
                                    </select> -->
                                </div>

                            </div>

                            <!-- Text -->
                            <div class="input-group">
                                <!-- name -->
                                <label :for="`nw-input-item-name-${element.id}-${index}`">Name</label><br/>
                                <input type="text" :id="`nw-input-item-name-${element.id}-${index}`" v-model="item.name" />
                                <!-- Position -->
                                <label :for="`nw-input-item-position-${element.id}-${index}`">Position</label><br/>
                                <input type="text" :id="`nw-input-item-position-${element.id}-${index}`" v-model=" item.position" />
                                <!-- text -->
                                <label :for="`nw-input-item-content-${element.id}-${index}`">Text</label><br/>
                                <textarea :id="`nw-input-item-content-${element.id}-${index}`" v-model="item.content" style="height: 90px;"></textarea>
                            </div>
                        </div>
                    </template>
                </item-tab-component>

            </div>
        </div>

        <!-- Appearance -->
        <div v-if="element.uiValues.activeTab === 'appearance'" class="appearance-wrapper p-4">
            <h2>Appearance</h2>
            
            <p>Nothing here yet</p>
        </div>

        <!-- Spacing -->
        <div v-if="element.uiValues.activeTab === 'spacing'" class="advanced-settings-wrapper p-4">
            <?php include PLUGIN_ROOT_PATH . 'components/advancedSettings/spacing-component.php'; ?>
        </div>
    </tab-transition-component>
</div>
<?php
// Theme-Funktionen und Definitionen

add_theme_support('post-thumbnails');

/**
 * Utils
 */
function generate_random_id($prefix = '', $length = 8) {
    return $prefix . bin2hex(random_bytes($length / 2));
}


/**
 * Check if Page-Builder is active
 */
// first we need "is_plugin_active" function from "wp-admin/includes/plugin.php"

if (!function_exists('is_plugin_active')) {
    require_once(ABSPATH . 'wp-admin/includes/plugin.php');
}

function newwways_theme_check_dependencies() {
    // Überprüfen, ob das Plugin aktiv ist
    if ( !is_plugin_active('newwways-page-builder/newwways-page-builder.php') ) {
        add_action('admin_notices', 'newwways_plugin_missing_notice');
    }
}

function newwways_plugin_missing_notice() {
    echo '<div class="notice notice-error">
        <p>' . __('<PERSON> Zollwärts-Theme benötigt das Page Builder Plugin, um korrekt zu funktionieren. Bitte installiere und aktiviere das Page Builder Plugin.', 'zollwaerts-theme') . '</p>
    </div>';
}

// Überprüfen der Abhängigkeiten nach dem Setup des Themes
add_action('after_setup_theme', 'newwways_theme_check_dependencies');



/** Theme Setup */
function mein_leeres_theme_setup() {
    // Lade Theme-Textdomain für Übersetzungen
    load_theme_textdomain('zollwaerts-theme', get_template_directory() . '/languages');

    // Unterstützung für automatische Feed-Links
    add_theme_support('automatic-feed-links');

    // Unterstützung für Titel-Tag durch WordPress verwalten lassen
    add_theme_support('title-tag');

    /**
     * aktivate and register menus
     */
    // Fügt Theme-Support für Menüs hinzu
    add_theme_support('menus');

    // Registriert ein Menü
    register_nav_menu('primary', __('Hauptmenü', 'mein-theme'));
}

add_action('after_setup_theme', 'mein_leeres_theme_setup');


function enqueue_nw_styles() {
    // Get the current time as a versioning parameter to avoid caching
    $version = time();
    // Enqueue the stylesheet with the version parameter
    wp_enqueue_style('nw-style', get_template_directory_uri() . '/css/global.css', array(), $version);
}
add_action('wp_enqueue_scripts', 'enqueue_nw_styles');


function enqueue_nw_scripts() {
    // Erstelle einen Timestamp oder eine einzigartige Version
    $version = date('YmdHis'); // Dynamisch basierend auf der aktuellen Zeit

    // Registriere das Skript mit diesem Timestamp als Version, um Caching zu vermeiden
    wp_register_script('nw-script', get_template_directory_uri() . '/js/scripts.js', array(), $version, true);

    // Reihen das Skript in die Warteschlange ein
    wp_enqueue_script('nw-script');
}

add_action('wp_enqueue_scripts', 'enqueue_nw_scripts');


/* Code Mirror for adminmenu */
// function enqueue_codemirror_assets() {
//     // CodeMirror CSS und JS einbinden
//     wp_enqueue_style('codemirror-css', 'https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.9/codemirror.min.css');
//     wp_enqueue_script('codemirror-js', 'https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.9/codemirror.min.js', array(), null, true);
//     // Zusätzliche CodeMirror-Modi und Themes
//     wp_enqueue_script('codemirror-mode-html', 'https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.9/mode/xml/xml.min.js', array(), null, true);
//     wp_enqueue_script('codemirror-mode-js', 'https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.9/mode/javascript/javascript.min.js', array(), null, true);
//     wp_enqueue_script('codemirror-mode-css', 'https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.9/mode/css/css.min.js', array(), null, true);
// }
// add_action('admin_enqueue_scripts', 'enqueue_codemirror_assets');


/**
 * Custom menu features 
 * 
 * 
 */ 
require get_template_directory() . '/inc/class-wpse-walker-nav-menu.php';

// Hinzufügen der Metadaten zum Menüelement
function custom_nav_menu_item($item) {
    $item->appereance = get_post_meta($item->ID, '_menu_item_appereance', true);
    $item->use_modal = get_post_meta($item->ID, '_menu_item_use_modal', true); // Neues Feld
    return $item;
}
add_action('wp_setup_nav_menu_item', 'custom_nav_menu_item', 10, 1);


// Speichern der Metadaten beim Aktualisieren des Menüelements
function custom_nav_update($menu_id, $menu_item_db_id, $args) {
    if (isset($_REQUEST['menu-item-appereance'][$menu_item_db_id])) {
        update_post_meta($menu_item_db_id, '_menu_item_appereance', sanitize_text_field($_REQUEST['menu-item-appereance'][$menu_item_db_id]));
    }
    if (isset($_REQUEST['menu-item-use-modal'][$menu_item_db_id])) {
        update_post_meta($menu_item_db_id, '_menu_item_use_modal', sanitize_text_field($_REQUEST['menu-item-use-modal'][$menu_item_db_id]));
    } else {
        delete_post_meta($menu_item_db_id, '_menu_item_use_modal'); // Entferne das Feld, wenn es nicht gesetzt ist
    }
}
add_action('wp_update_nav_menu_item', 'custom_nav_update', 10, 3);

// Invoke admin menu js with custom js functions for additional features (e.g. "Link style/apperance"-dropdown and "use modal"-checkbox)
function custom_admin_scripts($hook) {
    if ('nav-menus.php' === $hook) {
        wp_enqueue_script('custom-admin-js', get_template_directory_uri() . '/js/admin-menu-features.js', array('jquery'), null, true);

        // Daten, die an JavaScript übergeben werden
        $menus = wp_get_nav_menus();
        $menu_items_appereance_data = array();

        ?>
        <script type="text/javascript">
            // PHP-Array als JSON konvertieren und an JavaScript übergeben
            console.log('menu_items', <?php echo json_encode($menus); ?>);
            console.log('menu_items_appereance_data', <?php echo json_encode($menu_items_appereance_data); ?>);
        </script>
        <?php

        foreach ($menus as $menu) {
            $menu_items = wp_get_nav_menu_items($menu->term_id);
            if ($menu_items) {
                foreach ($menu_items as $item) {
                    $menu_items_appereance_data[$item->ID] = array(
                        'ID' => $item->ID,
                        'title' => $item->title,
                        'appereance' => get_post_meta($item->ID, '_menu_item_appereance', true),
                        'use_modal' => get_post_meta($item->ID, '_menu_item_use_modal', true),
                    );
                }
            }
        }

        wp_localize_script('custom-admin-js', 'menuItemsAppereance', $menu_items_appereance_data);
    }
}
add_action('admin_enqueue_scripts', 'custom_admin_scripts');


/**
 * Standart Beiträge/Posts
 */
// Meta boxses für "Beiträge"/"Posts" definieren 
// Metaboxen zu Beiträgen hinzufügen
function my_custom_post_meta_boxes() {
    add_meta_box(
        'grenzlotsen_post_intro_meta_box', // ID der Metabox
        __('Beitrags Intro', 'textdomain'), // Titel der Metabox
        'grenzlotsen_post_intro_meta_box_callback', // Callback-Funktion für die Inhalte
        'post', // Beitragstyp
        'normal', // Kontext (normal, side, etc.)
        'high' // Priorität (high, low, etc.)
    );
}
add_action('add_meta_boxes', 'my_custom_post_meta_boxes');

function grenzlotsen_post_intro_meta_box_callback($post) {
    // Daten abrufen
    $title_big = get_post_meta($post->ID, '_grenzlotsen_title_big', true);
    $title_small = get_post_meta($post->ID, '_grenzlotsen_title_small', true);
    $intro_text = get_post_meta($post->ID, '_grenzlotsen_intro', true);
    $image_id = get_post_meta($post->ID, '_grenzlotsen_image', true);
    $image_url = $image_id ? wp_get_attachment_url($image_id) : '';

    // Title Big
    echo '<label for="grenzlotsen_title_big_field">'.__('Title Big', 'textdomain').'</label>';
    echo '<input type="text" id="grenzlotsen_title_big_field" name="grenzlotsen_title_big_field" value="' . esc_attr($title_big) . '" class="widefat" />';
    echo '<br>';
    echo '<br>';
    
    // Title Small
    echo '<label for="grenzlotsen_title_small_field">'.__('Title Small', 'textdomain').'</label>';
    echo '<input type="text" id="grenzlotsen_title_small_field" name="grenzlotsen_title_small_field" value="' . esc_attr($title_small) . '" class="widefat" />';
    echo '<br>';
    echo '<br>';

    // Intro Text
    // echo '<label for="grenzlotsen_post_intro_field">'.__('Intro Text', 'textdomain').'</label>';
    echo '<p>Intro Text</p>';
    wp_editor($intro_text, 'grenzlotsen_post_intro_field', array(
        'textarea_name' => 'grenzlotsen_post_intro_field',
        'media_buttons' => false,
        'textarea_rows' => 10,
        'tinymce' => array(
            'toolbar1' => 'bold,italic,underline,|,bullist,numlist,|,link,unlink,|,grenzlotsen_button',
            'toolbar2' => '',
        ),
        'quicktags' => true
    ));

    // Bild hochladen
    // echo '<label for="grenzlotsen_image_field">'.__('Intro Image', 'textdomain').'</label>';
    // echo '<input type="hidden" id="grenzlotsen_image_field" name="grenzlotsen_image_field" value="' . esc_attr($image_id) . '" />';
    // echo '<div><img id="grenzlotsen_image_preview" src="' . esc_url($image_url) . '" style="max-width: 100%; height: auto; display: ' . ($image_url ? 'block' : 'none') . ';" /></div>';
    // echo '<button type="button" class="button" id="grenzlotsen_image_upload_button">'.__('Upload Image', 'textdomain').'</button>';
    // echo '<button type="button" class="button" id="grenzlotsen_image_remove_button" style="display: ' . ($image_url ? 'inline-block' : 'none') . ';">'.__('Remove Image', 'textdomain').'</button>';

    // JavaScript für Bild-Upload
    ?>
    <script>
    jQuery(document).ready(function($) {
        var file_frame;
        $('#grenzlotsen_image_upload_button').on('click', function(e) {
            e.preventDefault();

            if (file_frame) {
                file_frame.open();
                return;
            }

            file_frame = wp.media.frames.file_frame = wp.media({
                title: '<?php _e('Select or Upload Image', 'textdomain'); ?>',
                button: {
                    text: '<?php _e('Use this image', 'textdomain'); ?>',
                },
                multiple: false
            });

            file_frame.on('select', function() {
                var attachment = file_frame.state().get('selection').first().toJSON();
                $('#grenzlotsen_image_field').val(attachment.id);
                $('#grenzlotsen_image_preview').attr('src', attachment.url).show();
                $('#grenzlotsen_image_remove_button').show();
            });

            file_frame.open();
        });

        $('#grenzlotsen_image_remove_button').on('click', function(e) {
            e.preventDefault();
            $('#grenzlotsen_image_field').val('');
            $('#grenzlotsen_image_preview').hide();
            $('#grenzlotsen_image_remove_button').hide();
        });
    });
    </script>
    <?php
}

function save_my_custom_meta_box_data($post_id) {
    // Title Big
    if (array_key_exists('grenzlotsen_title_big_field', $_POST)) {
        update_post_meta(
            $post_id,
            '_grenzlotsen_title_big',
            sanitize_text_field($_POST['grenzlotsen_title_big_field'])
        );
    }

    // Title Small
    if (array_key_exists('grenzlotsen_title_small_field', $_POST)) {
        update_post_meta(
            $post_id,
            '_grenzlotsen_title_small',
            sanitize_text_field($_POST['grenzlotsen_title_small_field'])
        );
    }

    // Intro Text
    if (array_key_exists('grenzlotsen_post_intro_field', $_POST)) {
        update_post_meta(
            $post_id,
            '_grenzlotsen_intro',
            wp_kses_post($_POST['grenzlotsen_post_intro_field'])
        );
    }

    // Bild
    if (array_key_exists('grenzlotsen_image_field', $_POST)) {
        update_post_meta(
            $post_id,
            '_grenzlotsen_image',
            absint($_POST['grenzlotsen_image_field'])
        );
    }
}
add_action('save_post', 'save_my_custom_meta_box_data');





/**
 * Add "Sessions" CPT 
 */
function create_sessions_cpt() {

    $labels = array(
        'name' => _x('Sessions', 'Post Type General Name', 'textdomain'),
        'singular_name' => _x('Session', 'Post Type Singular Name', 'textdomain'),
        'menu_name' => __('Sessions', 'textdomain'),
        'name_admin_bar' => __('Session', 'textdomain'),
        'archives' => __('Session Archives', 'textdomain'),
        'attributes' => __('Session Attributes', 'textdomain'),
        'parent_item_colon' => __('Parent Session:', 'textdomain'),
        'all_items' => __('All Sessions', 'textdomain'),
        'add_new_item' => __('Add New Session', 'textdomain'),
        'add_new' => __('Add New', 'textdomain'),
        'new_item' => __('New Session', 'textdomain'),
        'edit_item' => __('Edit Session', 'textdomain'),
        'update_item' => __('Update Session', 'textdomain'),
        'view_item' => __('View Session', 'textdomain'),
        'view_items' => __('View Sessions', 'textdomain'),
        'search_items' => __('Search Session', 'textdomain'),
        'not_found' => __('Not found', 'textdomain'),
        'not_found_in_trash' => __('Not found in Trash', 'textdomain'),
        'featured_image' => __('Featured Image', 'textdomain'),
        'set_featured_image' => __('Set featured image', 'textdomain'),
        'remove_featured_image' => __('Remove featured image', 'textdomain'),
        'use_featured_image' => __('Use as featured image', 'textdomain'),
        'insert_into_item' => __('Insert into session', 'textdomain'),
        'uploaded_to_this_item' => __('Uploaded to this session', 'textdomain'),
        'items_list' => __('Sessions list', 'textdomain'),
        'items_list_navigation' => __('Sessions list navigation', 'textdomain'),
        'filter_items_list' => __('Filter sessions list', 'textdomain'),
    );

    $args = array(
        'label' => __('Session', 'textdomain'),
        'description' => __('Post Type for Sessions', 'textdomain'),
        'labels' => $labels,
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt', 'revisions'),
        'taxonomies' => array(), // Keine Standard-Taxonomien
        'hierarchical' => false,
        'public' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'menu_position' => 5,
        'menu_icon' => 'dashicons-calendar', // Icon für das Menü im Admin-Bereich
        'show_in_admin_bar' => true,
        'show_in_nav_menus' => true,
        'can_export' => true,
        'has_archive' => true,
        'exclude_from_search' => false,
        'publicly_queryable' => true,
        'capability_type' => 'post',
        'show_in_rest' => true, // Gutenberg Unterstützung
        'rewrite' => array('slug' => 'sessions', 'with_front' => false), // Hier wird die Permalink-Struktur festgelegt
    );

    register_post_type('sessions', $args);

}
add_action('init', 'create_sessions_cpt', 0);

// Session taxonomy "Session Location"
function create_session_location_taxonomy() {

    $labels = array(
        'name' => _x('Session Locations', 'taxonomy general name', 'textdomain'),
        'singular_name' => _x('Session Location', 'taxonomy singular name', 'textdomain'),
        'search_items' => __('Search Session Locations', 'textdomain'),
        'all_items' => __('All Session Locations', 'textdomain'),
        'parent_item' => __('Parent Session Location', 'textdomain'),
        'parent_item_colon' => __('Parent Session Location:', 'textdomain'),
        'edit_item' => __('Edit Session Location', 'textdomain'),
        'update_item' => __('Update Session Location', 'textdomain'),
        'add_new_item' => __('Add New Session Location', 'textdomain'),
        'new_item_name' => __('New Session Location Name', 'textdomain'),
        'menu_name' => __('Session Locations', 'textdomain'),
    );

    $args = array(
        'labels' => $labels,
        'hierarchical' => true, // `true` macht die Taxonomie hierarchisch (wie Kategorien), `false` nicht-hierarchisch (wie Tags)
        'public' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true,
        'show_in_quick_edit' => true,
        'show_admin_column' => true,
        'show_in_rest' => true, // Für Gutenberg- und REST-API-Unterstützung
    );

    register_taxonomy('session_location', array('sessions'), $args);

}
add_action('init', 'create_session_location_taxonomy', 0);


// Session taxonomy "Target Audience"
function create_target_audience_taxonomy() {

    $labels = array(
        'name' => _x('Target Audiences', 'taxonomy general name', 'textdomain'),
        'singular_name' => _x('Target Audience', 'taxonomy singular name', 'textdomain'),
        'search_items' => __('Search Target Audiences', 'textdomain'),
        'all_items' => __('All Target Audiences', 'textdomain'),
        'parent_item' => __('Parent Target Audience', 'textdomain'),
        'parent_item_colon' => __('Parent Target Audience:', 'textdomain'),
        'edit_item' => __('Edit Target Audience', 'textdomain'),
        'update_item' => __('Update Target Audience', 'textdomain'),
        'add_new_item' => __('Add New Target Audience', 'textdomain'),
        'new_item_name' => __('New Target Audience Name', 'textdomain'),
        'menu_name' => __('Target Audience', 'textdomain'),
    );

    $args = array(
        'labels' => $labels,
        'hierarchical' => true, // `true` macht die Taxonomie hierarchisch (wie Kategorien), `false` nicht-hierarchisch (wie Tags)
        'public' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true,
        'show_in_quick_edit' => true,
        'show_admin_column' => true,
        'show_in_rest' => true,
    );

    register_taxonomy('target_audience', array('sessions'), $args);

}
add_action('init', 'create_target_audience_taxonomy', 0);


// Session Meta Box for "global Session"/"All Tracks"
// add global_event_meta_box
function add_global_event_meta_box() {
    add_meta_box(
        'global_event_meta_box', // ID der Meta Box
        __('Global Event', 'textdomain'), // Titel der Meta Box
        'render_global_event_meta_box', // Callback, um die Inhalte der Meta Box anzuzeigen
        'sessions', // Post-Typ
        'side', // Position
        'default' // Priorität
    );
}
add_action('add_meta_boxes', 'add_global_event_meta_box');

// render global_event_meta_box
function render_global_event_meta_box($post) {
    // Sicherheitstoken hinzufügen
    wp_nonce_field('save_global_event_meta_box_data', 'global_event_meta_box_nonce');

    // Aktuellen Wert der Checkbox abrufen
    $value = get_post_meta($post->ID, '_global_event', true);

    // Checkbox anzeigen
    echo '<label for="global_event">';
    echo '<input type="checkbox" id="global_event" name="global_event" value="1"' . checked(1, $value, false) . ' />';
    echo __('This event belongs to all tracks', 'textdomain');
    echo '</label>';
}

// save global_event_meta_box
function save_global_event_meta_box_data($post_id) {
    // Sicherheitsüberprüfung
    if (!isset($_POST['global_event_meta_box_nonce']) || !wp_verify_nonce($_POST['global_event_meta_box_nonce'], 'save_global_event_meta_box_data')) {
        return;
    }

    // Überprüfung der Berechtigungen
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Speichern der Checkbox-Daten
    $is_global_event = isset($_POST['global_event']) ? 1 : 0;
    update_post_meta($post_id, '_global_event', $is_global_event);
}
add_action('save_post', 'save_global_event_meta_box_data');


// Session taxonomy "Session Type"
function create_session_type_taxonomy() {

    $labels = array(
        'name' => _x('Session Types', 'taxonomy general name', 'textdomain'),
        'singular_name' => _x('Session Type', 'taxonomy singular name', 'textdomain'),
        'search_items' => __('Search Session Types', 'textdomain'),
        'all_items' => __('All Session Types', 'textdomain'),
        'parent_item' => __('Parent Session Type', 'textdomain'),
        'parent_item_colon' => __('Parent Session Type:', 'textdomain'),
        'edit_item' => __('Edit Session Type', 'textdomain'),
        'update_item' => __('Update Session Type', 'textdomain'),
        'add_new_item' => __('Add New Session Type', 'textdomain'),
        'new_item_name' => __('New Session Type Name', 'textdomain'),
        'menu_name' => __('Session Types', 'textdomain'),
    );

    $args = array(
        'labels' => $labels,
        'hierarchical' => true, // Setze auf `false`, wenn du keine hierarchische Struktur möchtest
        'public' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true,
        'show_in_quick_edit' => true,
        'show_admin_column' => true,
        'show_in_rest' => true, // Für Gutenberg- und REST-API-Unterstützung
    );

    register_taxonomy('session_type', array('sessions'), $args);

}
add_action('init', 'create_session_type_taxonomy', 0);


// Create custom Meta box for "Timing"
// Meta Box hinzufügen
function add_session_time_meta_box() {
    add_meta_box(
        'session_time_meta_box', // ID der Meta Box
        __('Session Timing', 'textdomain'), // Titel der Meta Box
        'render_session_time_meta_box', // Callback, um die Inhalte der Meta Box anzuzeigen
        'sessions', // Post-Typ
        'side', // Position
        'high' // Priorität
    );
}
add_action('add_meta_boxes', 'add_session_time_meta_box');

// Inhalt der Meta Box "Timing" anzeigen
function render_session_time_meta_box($post) {
    // Sicherheitstoken hinzufügen
    wp_nonce_field('save_session_time_meta_box_data', 'session_time_meta_box_nonce');

    // Aktuelle Werte abrufen
    $start_time = get_post_meta($post->ID, '_session_start_time', true);
    $end_time = get_post_meta($post->ID, '_session_end_time', true);
    $session_date = get_post_meta($post->ID, '_session_date', true);

    // HTML für die Meta Box
    echo '<label for="session_date">' . __('Session Date:', 'textdomain') . '</label>';
    echo '<input type="date" id="session_date" name="session_date" value="' . esc_attr($session_date) . '" size="25" />';

    echo '<br><br>';

    echo '<label for="session_start_time">' . __('Start Time:', 'textdomain') . '</label>';
    echo '<input type="time" id="session_start_time" name="session_start_time" value="' . esc_attr($start_time) . '" size="25" />';

    echo '<br><br>';

    echo '<label for="session_end_time">' . __('End Time:', 'textdomain') . '</label>';
    echo '<input type="time" id="session_end_time" name="session_end_time" value="' . esc_attr($end_time) . '" size="25" />';
}


// Meta Box für "Kein Link zur Detailseite" hinzufügen
function add_no_link_to_detail_meta_box() {
    add_meta_box(
        'no_link_to_detail_meta_box', // ID der Meta Box
        __('Kein Link zur Detailseite', 'textdomain'), // Titel der Meta Box
        'render_no_link_to_detail_meta_box', // Callback, um die Inhalte der Meta Box anzuzeigen
        'sessions', // Post-Typ
        'side', // Position
        'default' // Priorität
    );
}
add_action('add_meta_boxes', 'add_no_link_to_detail_meta_box');

// Inhalt der Meta Box "Kein Link zur Detailseite" anzeigen
function render_no_link_to_detail_meta_box($post) {
    // Sicherheitstoken hinzufügen
    wp_nonce_field('save_no_link_to_detail_meta_box_data', 'no_link_to_detail_meta_box_nonce');

    // Aktuellen Wert der Checkbox abrufen
    $value = get_post_meta($post->ID, '_no_link_to_detail', true);

    // Checkbox anzeigen
    echo '<label for="no_link_to_detail">';
    echo '<input type="checkbox" id="no_link_to_detail" name="no_link_to_detail" value="1"' . checked(1, $value, false) . ' />';
    echo __('Kein Link zur Detailseite', 'textdomain');
    echo '</label>';
}

// Speichern der Meta Box-Daten
function save_no_link_to_detail_meta_box_data($post_id) {
    // Sicherheitsüberprüfung
    if (!isset($_POST['no_link_to_detail_meta_box_nonce']) || !wp_verify_nonce($_POST['no_link_to_detail_meta_box_nonce'], 'save_no_link_to_detail_meta_box_data')) {
        return;
    }

    // Überprüfung der Berechtigungen
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Speichern der Checkbox-Daten
    $no_link_to_detail = isset($_POST['no_link_to_detail']) ? 1 : 0;
    update_post_meta($post_id, '_no_link_to_detail', $no_link_to_detail);
}
add_action('save_post', 'save_no_link_to_detail_meta_box_data');


// Speichern der Meta Box-Daten
function save_session_time_meta_box_data($post_id) {
    // Sicherheitsüberprüfung
    if (!isset($_POST['session_time_meta_box_nonce']) || !wp_verify_nonce($_POST['session_time_meta_box_nonce'], 'save_session_time_meta_box_data')) {
        return;
    }

    // Überprüfung der Berechtigungen
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Speichern des Datums
    if (isset($_POST['session_date'])) {
        update_post_meta($post_id, '_session_date', sanitize_text_field($_POST['session_date']));
    }

    // Speichern der Startzeit
    if (isset($_POST['session_start_time'])) {
        update_post_meta($post_id, '_session_start_time', sanitize_text_field($_POST['session_start_time']));
    }

    // Speichern der Endzeit
    if (isset($_POST['session_end_time'])) {
        update_post_meta($post_id, '_session_end_time', sanitize_text_field($_POST['session_end_time']));
    }
}
add_action('save_post', 'save_session_time_meta_box_data');

function load_session_validation_script($hook) {
    // Stelle sicher, dass das Script nur auf den relevanten Seiten geladen wird
    if ($hook != 'post.php' && $hook != 'post-new.php') {
        return;
    }

    // Überprüfe, ob es sich um den Custom Post Type "sessions" handelt
    global $post;
    if ($post->post_type != 'sessions') {
        return;
    }

    // Lade das JavaScript
    wp_enqueue_script('session-validation-script', get_template_directory_uri() . '/js/session-validation.js', array('jquery'), null, true);

    // Sende die Nonce an das JavaScript
    wp_localize_script('session-validation-script', 'ajax_object', array(
        'security' => wp_create_nonce('validate_session_time_overlap')
    ));
}
add_action('admin_enqueue_scripts', 'load_session_validation_script');

// AJAX-Handler für die Validierung der Session-Zeiten
function validate_session_time_overlap_ajax() {
    // Sicherheitsüberprüfung
    check_ajax_referer('validate_session_time_overlap', 'security');

    // Post-Daten aus dem AJAX-Request
    $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;
    $location_id = isset($_POST['location_id']) ? intval($_POST['location_id']) : 0;
    $start_time = isset($_POST['start_time']) ? sanitize_text_field($_POST['start_time']) : '';
    $end_time = isset($_POST['end_time']) ? sanitize_text_field($_POST['end_time']) : '';
    $session_date = isset($_POST['session_date']) ? sanitize_text_field($_POST['session_date']) : '';


    // Überprüfe auf Überschneidungen
    $args = array(
        'post_type' => 'sessions',
        'post_status' => 'publish',
        'posts_per_page' => -1,
        'post__not_in' => array($post_id), // Schließt den aktuellen Post aus
        'tax_query' => array(
            array(
                'taxonomy' => 'session_location',
                'field' => 'term_id',
                'terms' => $location_id,
            ),
        ),
        'meta_query' => array(
            'relation' => 'AND',
            array(
                'key' => '_session_date',
                'value' => $session_date,
                'compare' => '=', // Überprüft, ob das Datum übereinstimmt
                'type' => 'DATE'
            ),
            array(
                'key' => '_session_start_time',
                'value' => $end_time,
                'compare' => '<',
                'type' => 'TIME'
            ),
            array(
                'key' => '_session_end_time',
                'value' => $start_time,
                'compare' => '>',
                'type' => 'TIME'
            ),
        ),
    );

    $overlapping_sessions = new WP_Query($args);

    if ($overlapping_sessions->have_posts()) {
        // Fehlernachricht zurückgeben
        wp_send_json_error(__('Error: There is a time conflict with another session in the same location.'));
    } else {
        wp_send_json_success();
    }

    wp_die(); // Beendet AJAX-Request
}
add_action('wp_ajax_validate_session_time_overlap', 'validate_session_time_overlap_ajax');


// Felder zu Quick Edit hinzufügen
function add_custom_quick_edit_fields($column_name, $post_type) {
    if ($post_type == 'sessions') {
        switch ($column_name) {
            case 'global_event':
                ?>
                <fieldset class="inline-edit-col-right">
                    <div class="inline-edit-col">
                        <label class="alignleft">
                            <span class="title"><?php _e('Global Event', 'textdomain'); ?></span>
                            <span class="input-text-wrap">
                                <input type="checkbox" name="global_event" value="1">
                            </span>
                        </label>
                    </div>
                </fieldset>
                <?php
                break;
            case 'no_link_to_detail':
                ?>
                <fieldset class="inline-edit-col-right">
                    <div class="inline-edit-col">
                        <label class="alignleft">
                            <span class="title"><?php _e('Kein Link zur Detailseite', 'textdomain'); ?></span>
                            <span class="input-text-wrap">
                                <input type="checkbox" name="no_link_to_detail" value="1">
                            </span>
                        </label>
                    </div>
                </fieldset>
                <?php
                break;
        }
    }
}
add_action('quick_edit_custom_box', 'add_custom_quick_edit_fields', 10, 2);



// Werte für Quick Edit bereitstellen
function enqueue_custom_quick_edit_script() {
    wp_enqueue_script('session-quick-edit', get_template_directory_uri() . '/js/session-quick-edit.js', array('jquery'), '', true);
}
add_action('admin_enqueue_scripts', 'enqueue_custom_quick_edit_script');


// Quick Edit-Daten speichern
function save_custom_quick_edit_data($post_id) {
    if (isset($_POST['global_event'])) {
        update_post_meta($post_id, '_global_event', 1);
    } else {
        update_post_meta($post_id, '_global_event', 0);
    }

    if (isset($_POST['no_link_to_detail'])) {
        update_post_meta($post_id, '_no_link_to_detail', 1);
    } else {
        update_post_meta($post_id, '_no_link_to_detail', 0);
    }
}
add_action('save_post', 'save_custom_quick_edit_data');


// Spalten hinzufügen
function add_custom_columns($columns) {
    $columns['global_event'] = __('Global Event', 'textdomain');
    $columns['no_link_to_detail'] = __('Kein Link zur Detailseite', 'textdomain');
    return $columns;
}
add_filter('manage_sessions_posts_columns', 'add_custom_columns');


// Inhalte der Spalten füllen und IDs hinzufügen
function custom_columns_content($column, $post_id) {
    switch ($column) {
        case 'global_event':
            $value = get_post_meta($post_id, '_global_event', true);
            echo '<span id="global_event-' . $post_id . '">' . ($value ? __('Yes', 'textdomain') : __('No', 'textdomain')) . '</span>';
            break;
        case 'no_link_to_detail':
            $value = get_post_meta($post_id, '_no_link_to_detail', true);
            echo '<span id="no_link_to_detail-' . $post_id . '">' . ($value ? __('Yes', 'textdomain') : __('No', 'textdomain')) . '</span>';
            break;
    }
}
add_action('manage_sessions_posts_custom_column', 'custom_columns_content', 10, 2);





/**
 * Add "Externe Beiträge" CPT 
 */
// Create CPT for "Externe Beiträge"
function create_externe_beitraege_cpt() {
    $labels = array(
        'name'               => _x('Externe Beiträge', 'Post Type General Name', 'textdomain'),
        'singular_name'      => _x('Externer Beitrag', 'Post Type Singular Name', 'textdomain'),
        'menu_name'          => __('Externe Beiträge', 'textdomain'),
        'all_items'          => __('Alle Beiträge', 'textdomain'),
        'add_new_item'       => __('Neuen Beitrag hinzufügen', 'textdomain'),
        'add_new'            => __('Neu hinzufügen', 'textdomain'),
        'edit_item'          => __('Beitrag bearbeiten', 'textdomain'),
        'update_item'        => __('Beitrag aktualisieren', 'textdomain'),
        'search_items'       => __('Beiträge durchsuchen', 'textdomain'),
        'not_found'          => __('Nicht gefunden', 'textdomain'),
        'not_found_in_trash' => __('Nicht im Papierkorb gefunden', 'textdomain'),
    );

    $args = array(
        'label'              => __('Externer Beitrag', 'textdomain'),
        'description'        => __('Beiträge von externen Quellen', 'textdomain'),
        'labels'             => $labels,
        'supports'           => array('title', 'thumbnail'), // Titel und Beitragsbild
        'taxonomies'         => array('externe_kategorien'), // Benutzerspezifische Kategorien hinzufügen
        'hierarchical'       => false,
        'public'             => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'menu_position'      => 5,
        'show_in_admin_bar'  => true,
        'show_in_nav_menus'  => true,
        'can_export'         => true,
        'rewrite'            => false, // Keine Permalink-Struktur
        'capability_type'    => 'post',
        'has_archive'        => false, // Kein Archiv
        'exclude_from_search'=> false,
        'publicly_queryable' => true,
        'capability_type'    => 'post',
    );

    register_post_type('externe_beitraege', $args);
}
add_action('init', 'create_externe_beitraege_cpt', 0);

// Disable "Beitrag ansehen" in backend
// disable in list
function remove_view_link_for_custom_post_type($actions, $post) {
    if ($post->post_type == 'externe_beitraege') {
        unset($actions['view']); // Entfernt den "Ansehen"-Button
    }
    return $actions;
}
add_filter('post_row_actions', 'remove_view_link_for_custom_post_type', 10, 2);

// Entfernt den "Beitrag ansehen" Link in der Admin-Bar
function remove_view_link_from_admin_bar($wp_admin_bar) {
    if (is_admin()) {
        global $post;
        if ($post && $post->post_type == 'externe_beitraege') {
            $wp_admin_bar->remove_node('view');
        }
    }
}
add_action('admin_bar_menu', 'remove_view_link_from_admin_bar', 999);

// Entfernt den "Vorschau der Änderungen" Button in der Admin-Bar
function remove_preview_button_from_admin_bar($wp_admin_bar) {
    if (is_admin()) {
        global $post;
        if ($post && $post->post_type == 'externe_beitraege') {
            $wp_admin_bar->remove_node('preview');
        }
    }
}
add_action('admin_bar_menu', 'remove_preview_button_from_admin_bar', 999);


// Entfernt den "Vorschau der Änderungen" Button aus dem "Veröffentlichen"-Metabox für den Custom Post Type
function remove_preview_button_for_custom_post_type() {
    global $post;
    if ($post && $post->post_type == 'externe_beitraege') {
        echo '<style>
            a#post-preview {
                display: none !important;
            }
        </style>';
    }
}
add_action('post_submitbox_misc_actions', 'remove_preview_button_for_custom_post_type');


// redirect externe_beitraege to 404
function disable_single_view_for_custom_post_type() {
    if (is_singular('externe_beitraege')) {
        wp_redirect(home_url('/404'), 301);
        exit;
    }
}
add_action('template_redirect', 'disable_single_view_for_custom_post_type');

// Create custom taxonomy for "Externe Kategorien"
function create_externe_kategorien_taxonomy() {
    $labels = array(
        'name'              => _x('Externe Kategorien', 'taxonomy general name', 'textdomain'),
        'singular_name'     => _x('Externe Kategorie', 'taxonomy singular name', 'textdomain'),
        'search_items'      => __('Kategorien durchsuchen', 'textdomain'),
        'all_items'         => __('Alle Kategorien', 'textdomain'),
        'parent_item'       => __('Übergeordnete Kategorie', 'textdomain'),
        'parent_item_colon' => __('Übergeordnete Kategorie:', 'textdomain'),
        'edit_item'         => __('Kategorie bearbeiten', 'textdomain'),
        'update_item'       => __('Kategorie aktualisieren', 'textdomain'),
        'add_new_item'      => __('Neue Kategorie hinzufügen', 'textdomain'),
        'new_item_name'     => __('Neue Kategorie', 'textdomain'),
        'menu_name'         => __('Externe Kategorien', 'textdomain'),
    );

    $args = array(
        'hierarchical'      => true, // true für Kategorien, false für Tags
        'labels'            => $labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'externe-kategorien'),
    );

    register_taxonomy('externe_kategorien', array('externe_beitraege'), $args);
}
add_action('init', 'create_externe_kategorien_taxonomy', 0);


// Custom Meta Box for "Externe Beiträge"
function externe_beitraege_add_meta_box() {
    add_meta_box(
        'externe_beitraege_url',          // ID der Meta Box
        'Externe URL',                    // Titel der Meta Box
        'externe_beitraege_url_callback', // Callback-Funktion für das Rendern der Box
        'externe_beitraege',              // Beitragstyp
        'normal',                           // Platzierung: 'normal', 'side' oder 'advanced'
        'high'                            // Priorität: 'high', 'core', 'default' oder 'low'
    );
}
add_action('add_meta_boxes', 'externe_beitraege_add_meta_box');

function externe_beitraege_url_callback($post) {
    wp_nonce_field('externe_beitraege_save_url', 'externe_beitraege_url_nonce');

    $value = get_post_meta($post->ID, '_externe_beitraege_url', true);

    echo '<label for="externe_beitraege_url">Externe URL:</label>';
    echo '<input type="url" id="externe_beitraege_url" name="externe_beitraege_url" value="' . esc_attr($value) . '" size="25" />';
}

function externe_beitraege_save_url($post_id) {
    if (!isset($_POST['externe_beitraege_url_nonce'])) {
        return;
    }

    if (!wp_verify_nonce($_POST['externe_beitraege_url_nonce'], 'externe_beitraege_save_url')) {
        return;
    }

    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }

    if (isset($_POST['post_type']) && $_POST['post_type'] === 'externe_beitraege') {
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }
    }

    if (!isset($_POST['externe_beitraege_url'])) {
        return;
    }

    $url = sanitize_text_field($_POST['externe_beitraege_url']);
    update_post_meta($post_id, '_externe_beitraege_url', $url);
}
add_action('save_post', 'externe_beitraege_save_url');



// function validate_session_time_overlap($post_id, $post, $update) {
//     // Überprüfe, ob es sich um den richtigen Post-Typ handelt
//     if ($post->post_type !== 'sessions') {
//         return;
//     }

//     // Hole die aktuelle Location und Zeiten
//     $current_location = wp_get_post_terms($post_id, 'session_location', array("fields" => "ids"));
//     if (empty($current_location)) {
//         return; // Keine Location gesetzt, keine Überprüfung nötig
//     }
//     $current_location = $current_location[0];
    
//     $start_time = get_post_meta($post_id, '_session_start_time', true);
//     $end_time = get_post_meta($post_id, '_session_end_time', true);

//     if (empty($start_time) || empty($end_time)) {
//         return; // Keine Zeiten gesetzt, keine Überprüfung nötig
//     }

//     // Bereite die Query vor, um nach Überschneidungen zu suchen
//     $args = array(
//         'post_type' => 'sessions',
//         'post_status' => 'publish',
//         'posts_per_page' => -1,
//         'post__not_in' => array($post_id), // Schließt den aktuellen Post aus
//         'tax_query' => array(
//             array(
//                 'taxonomy' => 'session_location',
//                 'field' => 'term_id',
//                 'terms' => $current_location,
//             ),
//         ),
//         'meta_query' => array(
//             'relation' => 'AND',
//             array(
//                 'key' => '_session_start_time',
//                 'value' => $end_time,
//                 'compare' => '<',
//                 'type' => 'TIME'
//             ),
//             array(
//                 'key' => '_session_end_time',
//                 'value' => $start_time,
//                 'compare' => '>',
//                 'type' => 'TIME'
//             ),
//         ),
//     );

//     $overlapping_sessions = new WP_Query($args);

//     if ($overlapping_sessions->have_posts()) {
//         // Fehler setzen und das Speichern verhindern
//         wp_die(__('Error: There is a time conflict with another session in the same location.'));
//     }
// }
// add_action('save_post', 'validate_session_time_overlap', 10, 3);


/**
 * Add custom TinyMCE Modules
 */
// function grenzlotsen_add_tinymce_button() {
//     // Prüfen, ob der Benutzerberechtigungen hat
//     if (!current_user_can('edit_posts') && !current_user_can('edit_pages')) {
//         return;
//     }

//     // Prüfen, ob der WYSIWYG-Editor aktiviert ist
//     if ('true' == get_user_option('rich_editing')) {
//         add_filter('mce_external_plugins', 'grenzlotsen_add_tinymce_plugin');
//         add_filter('mce_buttons', 'grenzlotsen_register_tinymce_button');
//     }
// }
// add_action('admin_head', 'grenzlotsen_add_tinymce_button');

// function grenzlotsen_add_tinymce_plugin($plugin_array) {
//     $plugin_array['grenzlotsen_button'] = get_template_directory_uri() . '/js/grenzlotsen-tinymce.js';
//     return $plugin_array;
// }

// function grenzlotsen_register_tinymce_button($buttons) {
//     array_push($buttons, 'grenzlotsen_button');
//     return $buttons;
// }




/*
 * Sonstiges
 * 
 */

/** Custom Image Sizes */
add_image_size( 'blog-thumb', 620, 0, true );
add_image_size( 'blog-hero', 1272, 0, true );


/* Get ajax url */ 
function enqueue_custom_scripts() {
    // Hole die aktuelle Zeit, um eine eindeutige Version zu generieren (nur für die Entwicklung sinnvoll)
    $version = filemtime(get_template_directory() . '/js/ajax-category-filter.js');

    // Skript einbinden, mit einer dynamischen Version, basierend auf dem letzten Änderungsdatum der Datei
    wp_enqueue_script( 'ajax-category-filter', get_template_directory_uri() . '/js/ajax-category-filter.js', array('jquery'), $version, true );

    // AJAX-URL bereitstellen
    wp_localize_script( 'ajax-category-filter', 'ajax_params', array(
        'ajaxurl' => admin_url( 'admin-ajax.php' ),
    ));
}
add_action( 'wp_enqueue_scripts', 'enqueue_custom_scripts' );


/**
 * category filtering ajax
 */

// error_log('AJAX Testing new:');

add_action( 'wp_ajax_filter_posts_by_category', 'filter_posts_by_category' );
add_action( 'wp_ajax_nopriv_filter_posts_by_category', 'filter_posts_by_category' );

// function filter_posts_by_category() {
//     error_log('AJAX Anfrage erhalten.');
//     error_log('Kategorie ID: ' . $_POST['category_id']);
//     echo 'AJAX Anfrage erhalten';  // Diese Zeile sollte in der Antwort erscheinen
//     wp_die();  // Wichtig, um die AJAX-Anfrage korrekt zu beenden
// }

function filter_posts_by_category() {
    $category_id = isset($_POST['category_id']) && $_POST['category_id'] !== null ? intval($_POST['category_id']) : null;

    $args = array(
        'posts_per_page' => 10,  // Anzahl der Posts pro Seite
    );

    // Füge den Kategorienfilter nur hinzu, wenn eine Kategorie ausgewählt ist
    if ($category_id) {
        $args['cat'] = $category_id;
    }

    $query = new WP_Query($args);

    // Ausgabe der gefilterten Beiträge
    if ( $query->have_posts() ) :
        while ( $query->have_posts() ) : $query->the_post(); ?>
            <div class="single-post-item" style="view-transition-name: post-<?php the_ID(); ?>">
                <a href="<?php the_permalink(); ?>">
                    <div class="single-post-item-inner">
                        <?php if ( has_post_thumbnail() ) : ?>
                            <div class="post-thumbnail">
                                <?php the_post_thumbnail('blog-thumb'); ?>
                            </div>
                        <?php endif; ?>

                        <div class="post-meta">
                            <span class="post-date"><?php echo get_the_date('d. F Y'); ?></span>
                            <span> | </span>
                            <span class="post-author"><?php the_author(); ?></span>
                        </div>

                        <h2><?php the_title(); ?></h2>
                        <div class="post-excerpt"><?php the_excerpt(); ?></div>

                        <div class="read-more">
                            <p>Weiterlesen</p>
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="12" height="12">
                                <path fill="#11114A" transform="matrix(-1 0 0 1 12 0.0812988)" d="M0.010917343 4.8662171L0.0017641115 4.9527488L0.010285715 5.0004244L0 5.0004597L0.0042217541 5.0822744C0.0051675173 5.0910196 0.0064603202 5.1014748 0.0083995117 5.1171575L0.035038877 5.236444L0.078554988 5.3484674L0.1172509 5.420835L0.16688135 5.4944906C0.1927705 5.5286436 0.22035234 5.5595098 0.25384098 5.5914679L4.5367041 9.7558832C4.8714046 10.081326 5.4141178 10.081379 5.7488871 9.7560034L5.8201995 9.6775103C6.0816836 9.3506546 6.0579653 8.8780003 5.7490106 8.5775919L2.9262857 5.8328538L11.142933 5.8328381C11.616307 5.832799 12 5.4597206 12 4.9995403L11.994232 4.9023609C11.944712 4.4879346 11.582367 4.1662769 11.142784 4.1663132L2.9254286 4.1663284L5.7489448 1.422472C6.083684 1.0970589 6.083684 0.56946623 5.7489486 0.24405697C5.4142137 -0.081352323 4.8715005 -0.081352323 4.5367656 0.24405697L0.2435506 4.4179473L0.1963734 4.4692841L0.15299548 4.5248733L0.11413158 4.5845737L0.081366442 4.6456332C0.074457817 4.659668 0.069259398 4.6720638 0.058862571 4.6968551L0.029049635 4.7849994L0.010917343 4.8662171Z"/>
                            </svg>
                        </div>
                    </div>
                </a>
            </div>
        <?php endwhile;
    else :
        echo '<p>Keine Beiträge gefunden.</p>';
    endif;

    wp_reset_postdata();
    wp_die();  // Beendet die AJAX-Anfrage korrekt
}






// function custom_avatar_size($avatar_defaults) {
//     // Setze die Größe des Gravatars auf 97px
//     return array_merge($avatar_defaults, ['size' => 97]);
// }
// add_filter('avatar_defaults', 'custom_avatar_size');

// function custom_avatar_dimensions($args) {
//     // Passe die Höhe und Breite des Avatars an
//     $args['height'] = 97;
//     $args['width'] = 97;
//     return $args;
// }
// add_filter('get_avatar', 'custom_avatar_dimensions', 10, 1);
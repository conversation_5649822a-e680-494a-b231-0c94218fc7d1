<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <title><?php bloginfo('name'); ?></title>
    <?php wp_head(); ?>


    <style>
        /* Header */
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            /* background: #2e2e2e; */
            z-index: 999;
        }

        header.fixed {
            position: fixed;
            width: 100%;
            /* background: rgba(255, 255, 255, 80%); */
            /* backdrop-filter: blur(6px); */
        }

        header .header-wrapper {
            width: 100%;
            padding: 20px 0px;
        }

        header .header-inner {
            max-width: var(--content-width);
            padding: 0 var(--content-padding);
            margin: 0 auto;

            display: flex;
            flex-grow: 1;
            justify-content: space-between;
            align-items: center;

            /* transform: rotate(0deg);
            transition: all 0.66s; */
        }

        /* .header-inner:hover {
            background: #73b;
            transform: rotate(180deg);
        } */

        /* Blur wrapper */ 
        .blur-wrapper {
            position: relative;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 0px 10px 0px;
            border-radius: 12px;
            background: rgb(255 255 255 / 0%);
            
            transition: background 0.66s cubic-bezier(0.77,0.2,0.05,1.0), box-shadow 0.66s cubic-bezier(0.77,0.2,0.05,1.0), padding 0.66s cubic-bezier(0.77,0.2,0.05,1.0);
            /* box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.0); */
        }

        /* .blur-wrapper {
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 10px 10px 26px;
            border-radius: 999px;
            background: rgb(255 255 255 / 0%);
            transition: background 0.3s ease-in-out, box-shadow 0.3s ease-in-out;
            box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.0);
        } */

        header.fixed .blur-wrapper {
            /* rgb(245 245 245 / 80%) */
            padding: 10px 10px 10px 20px;
            background: rgb(255 255 255 / 80%);
            box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(9px);
            -webkit-backdrop-filter: blur(9px);
        }


        header .header-inner .logo {
            color: #fff;
        }

        header .header-inner img {
            width: auto;
            max-width: 300px;
            height: auto;
            max-height: 40px;

            transition: opacity 0.66s cubic-bezier(0.77,0.2,0.05,1.0);
        }

        @media screen and (max-width: 1239px) {
            header .header-inner img {
                max-width: 250px;
                max-height: 32px;
            }
        }

        // TODO: just für dev testing 


        /* Default logo */
        header .header-inner img.default-logo {
            max-width: 300px;
            opacity: 1;    
        }
        header .header-inner img.scrolled-logo {
            position: absolute;
            max-width: 300px;
            opacity: 1;
        }


        /** Inverted logo */
        header .header-inner img.inverted-logo {
            opacity: 0;
        }
        header.inverted-header .header-inner img.inverted-logo {
            opacity: 1;
        }
        header.inverted-header .header-inner img.scrolled-logo {
            position: absolute;
            max-width: 300px;
            opacity: 0;
        }

        /* Fixed Header Logo */
        header.fixed .header-inner img.default-logo {
            max-width: 300px;
            opacity: 0;
        }
        header.fixed .header-inner img.scrolled-logo {
            max-width: 300px;
            opacity: 1;
        }

        header .header-inner nav ul.nav-menu {
            display: flex;
            align-items: center;
        }

        header .header-inner nav ul.nav-menu li {
            list-style: none;
            margin: 0 10px;
        }

        header .header-inner nav ul.nav-menu li:last-child {
            margin-right: 0;
        }

        /* nav CTA Button */
        li.menu-item.cta-button a,
        a.cta-button {
            color: #fff;
            text-decoration: none;
            text-align: center;
        }
        li.menu-item.cta-button a div,
        a.cta-button div {
            padding: 10px 20px 10px 26px;
            background: var(--accent-color);
            border-radius: 4px;
            color: #fff;
            text-decoration: none;
            
            /* for svg icon */ 
            display: flex;
            align-items: center;
            justify-content: center;
        }
        li.menu-item.cta-button a div svg, 
        a.cta-button div svg {
            margin-left: 4px;
            margin-right: 6px;
            transition: all 0.3s ease;
        }
        li.menu-item.cta-button:hover a div svg, 
        a.cta-button:hover div svg {
            margin-left: 10px;
            margin-right: 0;
        }

        header .header-inner nav ul.nav-menu li a {
            text-decoration: none;
        }
        header.fixed .header-inner nav ul.nav-menu li a {
            color: #fff;
        }


            .header-outer {
                position: fixed;
                width: 100%;
                z-index: 100;

                transition: all 0.3s ease;
            }

            .fixed {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                z-index: 100;
                /* background: #fff; */

                color: #000;

                /* box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.2); */
            }

            body:has(#wpadminbar) .fixed {
                top: 32px;
            }


            .header-inner h4 {
                color: #fff; 
                font-size
                transition: all 0.3s ease;
            }

            .fixed  .header-inner h4 {
                color: #000; 
            }

            header .header-inner nav ul.nav-menu li a div {
                color: #000;
                transition: color 0.66s cubic-bezier(.645,.045,.355,1)
            }
            header .header-inner nav ul.nav-menu li.current_page_item a div {
                color: var(--accent-color);
            }
            header.inverted-header .header-inner nav ul.nav-menu li a div {
                color: #fff;
            }

            header.fixed .header-inner nav ul.nav-menu li a div {
                color: #000;
            }

            header .header-inner nav ul.nav-menu li.menu-item.cta-button a div {
                color: #fff;
            }


            /**
            * Mobile Nav
            */
            header nav.mobile-nav {
                position: fixed;
                display: flex;
                width: 100%;
                top: 0;
                overflow: hidden;

                /* background: #fff; */
                /* background: #2e2e2e; */
        
                height: 0svh;
                opacity: 0;

                -webkit-backdrop-filter: blur(0px);
                backdrop-filter: blur(0px);

                transition: opacity 0.66s cubic-bezier(.645,.045,.355,1), height 0s 0.66s, backdrop-filter 0.66s cubic-bezier(.645,.045,.355,1);
                /* transition: opacity 0.66s cubic-bezier(.645,.045,.355,1), height 0.66s cubic-bezier(.645,.045,.355,1); */

                --bg-one-opacity: 1;
                --bg-one-transformation-size: 66%;
                --bg-one-transformation-x: 11%;

                
            }
            header.expanded nav.mobile-nav {
                height: 100vh;
                /* height: 100svh; */
                opacity: 1;

                -webkit-backdrop-filter: blur(20px);
                backdrop-filter: blur(20px);

                transition: opacity 0.66s cubic-bezier(.645,.045,.355,1), height 0s 0s, backdrop-filter 0.66s cubic-bezier(.645,.045,.355,1);
                /* transition: opacity 0.66s cubic-bezier(.645,.045,.355,1), height 0.66s cubic-bezier(.645,.045,.355,1); */
            }

            header nav.mobile-nav .bg-gradient-1 {
                /* content: '';
                z-index: -1;
                width: 100vw;
                height: 150vh;
                position: absolute;
                margin-left: calc(-50vw + 50%);
                margin-top: -6%;
                
                background: radial-gradient(circle at 33% 50%, rgb(24 212 242 / 100%) 0%, #eee0 76%);

                background: radial-gradient(circle at 0% 50%, rgb(191 22 146 / 100%) 0%, #eee0 66%);
                opacity: 1;
                
                animation: animateBackground 6s infinite; */

                content: '';
                z-index: -1;
                opacity: 0;
                width: 150vw;
                height: 150vw;
                position: absolute;
                margin-left: calc(-50vw + 0%);
                margin-top: -50%;
                background: radial-gradient(circle, rgba(55, 187, 255, 1) 0%, rgba(238, 240, 245, 0) 66%);
                border-radius: 50%;
                /* filter: url(#displacementFilter); */

                /* animation: animateBackground 0.66s infinite; */
                /* transition: all 0.66s cubic-bezier(.645,.045,.355,1); */
            }
            header.expanded nav.mobile-nav .bg-gradient-1 {
                opacity: 1;
                margin-left: calc(-50vw + 50%);
                margin-top: 50%;
            }


            header nav.mobile-nav .bg-gradient-2 {
                content: '';
                z-index: -1;
                opacity: 0;
                width: 200vw;
                height: 200vw;
                position: absolute;
                margin-left: calc(-50vw - 75%);
                margin-top: -100%;
                background: radial-gradient(circle, rgb(191 22 146 / 66%) 0%, rgba(238, 240, 245, 0) 66%);
                border-radius: 50%;
                /* filter: url(#displacementFilter); */
                
                /* animation: animateBackground 0.66s infinite; */
                /* transition: all 0.66s cubic-bezier(.645,.045,.355,1); */
            }
            header.expanded nav.mobile-nav .bg-gradient-2 {
                opacity: 1;
                margin-left: calc(-50vw - 50%);
                margin-top: -50%;
            }

            @keyframes animateBackground {
                0% {
                    margin-left: calc(-50vw + 0%);
                    margin-top: -50%;
                }
                50% {
                    margin-left: calc(-50vw + 50%);
                    margin-top: 50%;
                }
                100% {
                    margin-left: calc(-50vw + 0%);
                    margin-top: -50%;
                } 
            }
            

            /* reset shadow */
            /* header.fixed.expanded .blur-wrapper {
                box-shadow: 0px 0px 0px 0px rgba(0, 0, 0, 0);
            } */

            header nav.mobile-nav .inner {
                z-index: 9999;
                position: relative;
                display: flex;
                flex-direction: column;
                align-items: center;
                align-self: center;
                justify-content: center;
                padding: 2rem;
                width: 100%;
                height: 100%;
                
                background: rgb(255 255 255 / 80%);
                
                /* -webkit-backdrop-filter: blur(0px); */
                /* backdrop-filter: blur(0px); */

                /* transition: all 0.66s; */
                /* cubic-bezier(.645,.045,.355,1) */
            }
            /* header nav.mobile-nav .inner {
                -webkit-backdrop-filter: blur(9px);
                backdrop-filter: blur(9px);
            } */
            nav.mobile-nav .inner .main-navigation {
                width: 100%;
                text-align: center;
            }
            nav.mobile-nav .inner .main-navigation ul {
                list-style: none;
                margin-left: 0;
            }
            nav.mobile-nav .inner .main-navigation li {
                margin: 0;
                padding: 0;
                padding-bottom: 2rem;
                list-style: none;
                /* new */ 
                width: fit-content;
                margin: auto;
            }
            
            nav.mobile-nav .inner .main-navigation li a {
                color: var(--accent-color-secondary);
            }
            nav.mobile-nav .inner .main-navigation li a div {
                text-align: center;
            }

            /** Set Logo to second img */
            header.expanded .header-inner img.default-logo {
                opacity: 0;
            }
            header.expanded .header-inner img.scrolled-logo {
                opacity: 1;
            }


            /* Hamburger Menu */
            header .hamburger {
                width: 36px;
                height: 30px;
                /* margin-top: -16px; */
                cursor: pointer;
                display: flex;
                position: relative;
                right: 0;
                top: 0;
            }

            /* Position absolute */ 
            header .hamburger {
                width: 36px;
                height: 30px;
                /* margin-top: -16px; */
                cursor: pointer;
                display: flex;
                /* position: absolute; */
                right: 10px;
                /* top: 13px; */
            }

            header .hamburger .line {
                display: block;
                position: absolute;
                width: 36px;
                height: 4px;
                border-radius: 2px;
                background-color: #11114a;
                transition: all .6s cubic-bezier(.645,.045,.355,1);
                -webkit-transition: all .6s cubic-bezier(.645,.045,.355,1);
            }
            header.inverted-header .hamburger .line {
                background-color: #fff;
            }
            header.fixed .hamburger .line,
            header.expanded .hamburger .line {
                background-color: var(--accent-color);
                background-color: #11114a;
            }

            header .hamburger .line:first-child {
                transform: rotate(0deg);
                -webkit-transform: rotate(0deg);
                top: 0;
                left: 0
            }

            header .hamburger .line:nth-child(2) {
                transform: rotate(0deg);
                -webkit-transform: rotate(0deg);
                top: 12px;
                left: 0
            }

            header .hamburger .line:nth-child(3) {
                transform: rotate(0deg);
                -webkit-transform: rotate(0deg);
                top: 24px;
                left: 0
            }


            /* expanded */ 
            header.expanded .hamburger .line:first-child {
                transform: rotate(45deg);
                -webkit-transform: rotate(45deg);
                margin-top: 11px
            }

            header.expanded .hamburger .line:nth-child(2) {
                opacity: 0;
                margin-top: 13px;
                top: -2px;
                left: 16px;
                width: 4px;
            }

            header.expanded .hamburger .line:last-child {
                transform: rotate(-45deg);
                -webkit-transform: rotate(-45deg);
                margin-top: -14px
            }



            /**
            * Tablet Styles
            */

            @media screen and (min-width: 1100px) {
                header.header-outer .desktop-nav {
                    display: block;
                }
                header.header-outer .mobile-nav, header.header-outer .hamburger {
                    display: none;
                }
            } 

            @media screen and (max-width: 1099px) {
                header.header-outer .desktop-nav {
                    display: none; 
                }

                header.header-outer .mobile-nav, header.header-outer .hamburger {
                    display: block;
                }
            }

            /* Mobile Styles */
            @media screen and (max-width: 699px) {
                header.header-outer .header-inner img {
                    max-width: 70vw;
                    max-height: 28px;
                }
            }



            /* header-spacer */
            .header-spacer {
                height: 107px;
                width: 100%;
            }
    </style>

</head>
<body data-scroll-container <?php body_class(); ?>>
<!-- <body> -->
<div class="scroll-wrapper">

<?php

   $frontend_settings_json_data = get_post_meta(get_the_ID(), '_page_builder_frontend_settings', true);

    // Verwenden von wp_unslash, um unnötige Escape-Zeichen zu entfernen
    $frontend_settings_unslashed_data = wp_unslash($frontend_settings_json_data);

    // Dekodieren der JSON-Daten in ein PHP-Array
    $frontend_settings = json_decode($frontend_settings_unslashed_data, true); 

    // echo '<p>frontend_settings: ' . json_encode($frontend_settings) . '</p>';
    // echo '<p>frontend_settings: ' .  wp_kses_post($frontend_settings['invertHeader']) . '</p>';

?>

<!-- <header class="header-outer <?php echo wp_kses_post($frontend_settings['invertHeader']) ? 'inverted-header' : ''; ?>"> -->
<header class="header-outer <?php echo !empty($frontend_settings['invertHeader']) ? 'inverted-header' : ''; ?>">
    <nav class="mobile-nav">
        <div class="bg-gradient-1"></div>
        <div class="bg-gradient-2"></div>
        <div class="inner">
            <?php
                // Zeigt das Hauptmenü an
                wp_nav_menu(array(
                    'theme_location' => 'primary',
                    'container_class' => 'main-navigation', // Container-Klasse hinzufügen
                    'menu_class' => 'nav-menu', // UL-Klasse hinzufügen
                    'walker' => new WPSE_Walker_Nav_Menu()
                ));
            ?>
            <!-- TODO: check if this causes problems in frontend -->
            <svg
                width="0"
                height="0"
                viewBox="0 0 220 220"
                xmlns="http://www.w3.org/2000/svg"
            >       
                <filter id="displacementFilter">
                    <feTurbulence
                    type="turbulence"
                    baseFrequency="0.05"
                    numOctaves="2"
                    result="turbulence" />
                    <feDisplacementMap
                    in2="turbulence"
                    in="SourceGraphic"
                    scale="50"
                    xChannelSelector="R"
                    yChannelSelector="G" />

                    <feGaussianBlur stdDeviation="30" />
                </filter>

                <circle cx="100" cy="100" r="100" style="filter: url(#displacementFilter)" />
            </svg>
        </div>
    </nav>

    <div class="header-wrapper">
        <div class="header-inner">
            <div class="blur-wrapper">
                <div class="logo">
                    <?php 
                        /*
                            $saved_settings = wp_unslash(get_option('grenzlotsen_page_builder_content'));
                            $saved_settings_array = json_decode($saved_settings, true); // Als assoziatives Array dekodieren
                        */
                        
                        // TODO: unbedingt in page.php auslagern und variable übergeben 
                        // Holen der gespeicherten JSON-Daten aus den Post-Metadaten
                        $json_data = get_option('grenzlotsen_page_builder_content');

                        // echo '<p>json_data:' . $json_data . '</p>';

                        // Verwenden von wp_unslash, um unnötige Escape-Zeichen zu entfernen
                        $unslashed_data = wp_unslash($json_data);

                        // echo '<p>unslashed_data:' . $unslashed_data . '</p>';

                        // Dekodieren der JSON-Daten in ein PHP-Array
                        $saved_settings_array = json_decode($unslashed_data, true); 

                        // echo '<p>saved_settings_array:' . $saved_settings_array . '</p>';

                        if ($saved_settings_array && isset($saved_settings_array['header'])) {
                            $header_settings = $saved_settings_array['header'];
                            // Jetzt kannst du $header_settings verwenden, z.B.:
                            // echo '<pre>';
                            // echo $header_settings['defaultLogo']['url'];
                            // echo '</pre>';
                            if (isset($header_settings['defaultLogo']['url'])) {
                                echo '<a href="/"><img src="' . $header_settings['scrolledLogo']['url'] . '" alt="Logo" class="scrolled-logo"></a>';
                                echo '<a href="/"><img src="' . $header_settings['defaultLogo']['url'] . '" alt="Logo" class="inverted-logo"></a>';
                            } else {
                                echo '<h4>' . bloginfo('name') . '</h4>';
                            }
                            
                        } else {
                            // echo 'Fehler beim Dekodieren des JSON oder "header" nicht gefunden.';
                            echo '<h4 style="font-size: 2rem; color: #000;">'; 
                            echo bloginfo('name');
                            echo '</h4>';
                        }
                    ?>
                    
                </div>
                <nav class="desktop-nav">
                    <!-- <?php wp_nav_menu(array('theme_location' => 'primary')); ?> -->

                    <!-- <hr> -->

                    <?php
                        // Zeigt das Hauptmenü an
                        wp_nav_menu(array(
                            'theme_location' => 'primary',
                            'container_class' => 'main-navigation', // Container-Klasse hinzufügen
                            'menu_class' => 'nav-menu', // UL-Klasse hinzufügen
                            'walker' => new WPSE_Walker_Nav_Menu()
                        ));
                    ?>
                </nav>

                <!-- <nav class="mobile-nav" data-astro-cid-pwmmw5ba=""> <nav role="navigation" class="toggle-mobile-nav bg-dsv-orange justify-center" data-astro-cid-mm23ho6g=""> 
                    <div data-astro-cid-mm23ho6g=""> 
                        <a href="/" class="" data-astro-cid-mm23ho6g="" data-auto-event-observed="true"> Über die Kampagne </a> 
                        <ul class="submenu w-auto whitespace-nowrap justify-center" data-astro-cid-mm23ho6g=""> 
                            <li class="p-0 " data-astro-cid-mm23ho6g="">
                                <a href="/#politischer-hintergrund" class="whitespace-nowrap" data-astro-cid-mm23ho6g="" data-auto-event-observed="true"> Politischer Hintergrund </a> 
                        </li>
                        <li class="p-0 " data-astro-cid-mm23ho6g=""> 
                            <a href="/#verbraucherumfrage" class="whitespace-nowrap" data-astro-cid-mm23ho6g="" data-auto-event-observed="true"> Verbraucherstimmen </a> 
                        </li><li class="p-0 " data-astro-cid-mm23ho6g=""> 
                            <a href="/#faktencheck-wissenschaft" class="whitespace-nowrap" data-astro-cid-mm23ho6g="" data-auto-event-observed="true"> Faktencheck Wissenschaft </a> 
                        </li>
                        <li class="p-0 " data-astro-cid-mm23ho6g=""> 
                            <a href="/#politisches-fruehstueck" class="whitespace-nowrap" data-astro-cid-mm23ho6g="" data-auto-event-observed="true"> Politisches Frühstück </a> 
                        </li> 
                    </ul> 
                    </div>
                    
                    <div data-astro-cid-mm23ho6g=""> 
                        <a href="/blog" class="" data-astro-cid-mm23ho6g="" data-auto-event-observed="true"> Blog &amp; Aktuelles </a>  
                    </div>
                </nav>   -->

                <div class="hamburger"> 
                    <span class="line"></span> 
                    <span class="line"></span> 
                    <span class="line"></span> 
                </div> 
                    
            </div>
        </div>
    </div>
</header>


<?php echo isset($frontend_settings['invertHeader']) && wp_kses_post($frontend_settings['invertHeader']) ? '' : '<div class="header-spacer"></div>'; ?>

<script> 
    // document.addEventListener('DOMContentLoaded', () => {
    //     const header = document.querySelector('.header-outer');

    //     window.onscroll = () => {
    //         console.log('onscroll')
    //         if (window.scrollY > 2 && !header.classList.contains('fixed')) {
    //             header.classList.add('fixed');
    //             console.log('fix added')
    //         } 
            
    //         if(window.scrollY <= 2 && header.classList.contains('fixed')) {
    //             header.classList.remove('fixed');
    //             console.log('fix removed')
    //         }
    //     }
    // });
    document.addEventListener('DOMContentLoaded', () => {
        const header = document.querySelector('.header-outer');
        const hamburger = document.querySelector('.hamburger');

        const isHeaderInverted = header.classList.contains('inverted-header');
        if (isHeaderInverted) {
            window.addEventListener('resize', () => {
                const windowWidth = window.innerWidth;

                if (windowWidth > 1100) {
                    header.classList.remove('expanded');
                }
            });
        }

        hamburger.addEventListener('click', () => {
            header.classList.toggle('expanded');
        })
    })
</script>


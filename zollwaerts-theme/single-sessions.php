<!-- Blog Post Template -->
<?php get_header(); ?>


<?php
    $json_data = get_post_meta(get_the_ID(), '_page_builder_blocks', true);

    // <PERSON>er<PERSON><PERSON> von wp_unslash, um unnötige Escape-Zeichen zu entfernen
    $unslashed_data = wp_unslash($json_data);

    // Dekodieren der JSON-Daten in ein PHP-Array
    $page_builder_blocks = json_decode($unslashed_data, true); 
?>


<style>
    .single-post .block-wrapper {
        padding-top: 50px;
        padding-bottom: 50px;
    }
</style>

<main>
    <?php
    while (have_posts()) : the_post();
        ?>
        <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
            

            <div class="entry-content block">
                <!-- Ausgabe von Autor und Kategorien -->
                <div class="post-header">
                    <div class="categories tagline">
                        Sessions
                    </div>

                    <h1><?php the_title(); ?></h1>
                    <?php 
                    $subtitle = get_post_meta(get_the_ID(), '_session_subtitle', true);
                    if ($subtitle) {
                        echo '<h2 class="session-subtitle">' . esc_html($subtitle) . '</h2>';
                    }
                    ?>

                    <!-- <?php
                        $title_big = get_post_meta(get_the_ID(), '_grenzlotsen_title_big', true);
                        $title_small = get_post_meta(get_the_ID(), '_grenzlotsen_title_small', true);

                        if ($title_big) {
                            echo '<h1>' . esc_html($title_big) . '</h1>';
                        } else {
                            echo '<h1>' . the_title() . '</h1>';
                        }

                        if ($title_small) {
                            echo '<h3>' . esc_html($title_small) . '</h3>';
                        }

                    ?> -->

                    <!-- <?php the_title('<h1 class="entry-title">', '</h1>'); ?> -->

                    <style>
                        .post-header h1 {
                            font-size: 54px;
                            line-height: 64px;
                            font-weight: 600;
                            margin-bottom: 0;
                            padding: 16px 0;
                        }
                        h2.session-subtitle {
                            padding: 0 !important;
                            margin-top: 0;
                        }
                        .post-header h3 {
                            font-size: 24px;
                            line-height: 36px;
                            font-weight: 600;
                            /* margin-bottom: 34px; */
                            padding: 16px 0;
                        }
                        .post-header .intro-text {
                            padding: 16px 0
                        }
                    </style>


                    <!-- Post Meta -->
                    <div class="session-meta">
                        <div class="session-details">

                            <div class="session-type">
                                <?php
                                // Get syssion types 
                                $session_types = wp_get_post_terms(get_the_ID(), 'session_type', array("fields" => "names"));
                                if (!empty($session_types)) {
                                    foreach ($session_types as $session_type) {
                                        echo '<span>' . esc_html($session_type) . '</span>';
                                    }
                                }

                                // Überprüfen, ob es sich um einen Partner handelt
                                $is_partner = get_post_meta(get_the_ID(), '_is_partner', true);
                                if ($is_partner) {
                                    echo '<span class="partner">Partner</span>';
                                }
                                ?>
                            </div>


                            <h2>Details</h2>

                            <div class="session-detail-list">
                                <div class="date">
                                    <?php
                                    // Datum aus der Datenbank abrufen
                                    $session_date = get_post_meta(get_the_ID(), '_session_date', true);

                                    if ($session_date) {
                                        // Datum ins TT.MM.JJJJ-Format konvertieren und anzeigen
                                        echo '<p>';
                                        echo '
                                                <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                    <path fill-rule="evenodd" d="M5 5a1 1 0 0 0 1-1 1 1 0 1 1 2 0 1 1 0 0 0 1 1h1a1 1 0 0 0 1-1 1 1 0 1 1 2 0 1 1 0 0 0 1 1h1a1 1 0 0 0 1-1 1 1 0 1 1 2 0 1 1 0 0 0 1 1 2 2 0 0 1 2 2v1a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V7a2 2 0 0 1 2-2ZM3 19v-7a1 1 0 0 1 1-1h16a1 1 0 0 1 1 1v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2Zm6.01-6a1 1 0 1 0-2 0 1 1 0 0 0 2 0Zm2 0a1 1 0 1 1 2 0 1 1 0 0 1-2 0Zm6 0a1 1 0 1 0-2 0 1 1 0 0 0 2 0Zm-10 4a1 1 0 1 1 2 0 1 1 0 0 1-2 0Zm6 0a1 1 0 1 0-2 0 1 1 0 0 0 2 0Zm2 0a1 1 0 1 1 2 0 1 1 0 0 1-2 0Z" clip-rule="evenodd"/>
                                                </svg>
                                            ';
                                        echo date('d.m.Y', strtotime($session_date));
                                        echo '</p>';
                                    }
                                    ?>
                                </div>


                                <div class="time">
                                    <?php 
                                    // Uhrzeit abrufen
                                    $start_time = get_post_meta(get_the_ID(), '_session_start_time', true);
                                    $end_time = get_post_meta(get_the_ID(), '_session_end_time', true);
                                    if ($start_time && $end_time) {
                                        echo '<p>';
                                        echo '  <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                    <path fill-rule="evenodd" d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm11-4a1 1 0 1 0-2 0v4a1 1 0 0 0 .293.707l3 3a1 1 0 0 0 1.414-1.414L13 11.586V8Z" clip-rule="evenodd"/>
                                                </svg>';
                                        echo esc_html($start_time) . ' - ' . esc_html($end_time);
                                        echo '</p>';
                                    }
                                    ?>
                                </div>

                                <div class="location track">
                                    <?php
                                    // Session Tracks (Locations) abrufen
                                    $locations = wp_get_post_terms(get_the_ID(), 'session_location', array("fields" => "names"));
                                    if (!empty($locations)) {
                                        echo '<p>';
                                        echo '  <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                    <path fill-rule="evenodd" d="M11.906 1.994a8.002 8.002 0 0 1 8.09 8.421 7.996 7.996 0 0 1-1.297 3.957.996.996 0 0 1-.133.204l-.108.129c-.178.243-.37.477-.573.699l-5.112 6.224a1 1 0 0 1-1.545 0L5.982 15.26l-.002-.002a18.146 18.146 0 0 1-.309-.38l-.133-.163a.999.999 0 0 1-.13-.202 7.995 7.995 0 0 1 6.498-12.518ZM15 9.997a3 3 0 1 1-5.999 0 3 3 0 0 1 5.999 0Z" clip-rule="evenodd"/>
                                                </svg>';
                                        echo implode(', ', $locations);
                                        echo '</p>';
                                    }
                                    ?>
                                </div>

                                <div class="target-audience track">
                                    <?php
                                    // Session Target Audience (Audience) abrufen
                                    $audiences = wp_get_post_terms(get_the_ID(), 'target_audience', array("fields" => "names"));
                                    if (!empty($audiences)) {
                                        echo '<p>';
                                        echo '  <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                    <path fill-rule="evenodd" d="M12 6a3.5 3.5 0 1 0 0 7 3.5 3.5 0 0 0 0-7Zm-1.5 8a4 4 0 0 0-4 4 2 2 0 0 0 2 2h7a2 2 0 0 0 2-2 4 4 0 0 0-4-4h-3Zm6.82-3.096a5.51 5.51 0 0 0-2.797-6.293 3.5 3.5 0 1 1 2.796 6.292ZM19.5 18h.5a2 2 0 0 0 2-2 4 4 0 0 0-4-4h-1.1a5.503 5.503 0 0 1-.471.762A5.998 5.998 0 0 1 19.5 18ZM4 7.5a3.5 3.5 0 0 1 5.477-2.889 5.5 5.5 0 0 0-2.796 6.293A3.501 3.501 0 0 1 4 7.5ZM7.1 12H6a4 4 0 0 0-4 4 2 2 0 0 0 2 2h.5a5.998 5.998 0 0 1 3.071-5.238A5.505 5.505 0 0 1 7.1 12Z" clip-rule="evenodd"/>
                                                </svg>';
                                        echo esc_html(implode(', ', $audiences));
                                        echo '</p>';
                                    }
                                    ?>
                                </div>
                            </div>
                            
                        </div>

                        <div class="session-feature-image">
                            <?php
                            if (has_post_thumbnail()) {
                                // Gibt das Beitragsbild mit einer Standardgröße aus
                                the_post_thumbnail('full', array('class' => 'session-thumbnail'));
                                // Alternativ: Wenn du eine bestimmte Größe oder zusätzliche Attribute brauchst
                                // echo get_the_post_thumbnail($post_id, 'medium', array('class' => 'session-thumbnail'));
                            }
                            ?>
                        </div>  
                    </div>
                    
                    
                    <style>
                        .session-meta {
                            background: #F3F6FA;
                            overflow: hidden; 
                            border-radius: 1rem;
                            margin: 2rem 0 4rem;
                            display: grid;
                            grid-template-columns: 1fr 1fr;
                            gap: 1rem;
                        } 
                        .session-meta .session-details {
                            padding: 2rem 3rem;
                        } 

                        .session-meta .session-details .session-detail-list p {
                            display: flex !important;
                            margin-bottom: 0.5rem;
                        }

                        .session-meta .session-details p svg {
                            margin-right: 1rem;
                            min-width: 24px;
                            min-height: 24px;
                            margin-top: 4px;
                        }
                        .session-meta .session-details p svg path {
                            fill: #AFAFC7;
                        }


                        .session-meta img {
                            display: block;
                            min-height: 100%;
                            /* width: auto; */
                            width: 100%;
                            height: auto;
                            object-fit: cover;
                        }

                        /* Post Meta (currently hidden) */
                        .post-meta {
                            display: flex;
                            jusity: start;
                            align-items: center;
                            gap: 1rem;
                            padding: 24px 0;
                        }

                        .post-meta p {
                            font-family: "Poppins-Regular";
                            font-size: 16px;
                            font-weight: 400;
                            font-style: Regular;
                            letter-spacing: 0px;
                            line-height: 30px;
                        }

                        .post-meta .author-info {
                            display: flex;
                            jusity: start;
                            align-items: center;
                            gap: 1rem;
                        }
                        .post-meta .author-info img {
                            border-radius: 999px;
                        }

                        .post-meta .vertical-divider {
                            height: 30px;
                            width: 2px;
                            background: #AFAFC7;
                        }


                        @media screen and (max-width: 999px) {
                            .session-meta {
                                display: flex;
                                flex-direction: column-reverse;
                            }
                        }


                        /* Session Type inkl. partner */ 
                        .session-type {
                            display: flex;
                            gap: 1rem;
                        }
                        .session-type span {
                            font-size: 14px;
                            color: #fff;
                            background: var(--accent-color-secondary);
                            padding: 0.25rem 0.75rem;
                            border-radius: 999px;
                        }

                        .session-type span.partner {
                            background: var(--accent-color);
                            color: #fff;
                        }
                    </style>


                    <?php
                        $intro_text = get_post_meta(get_the_ID(), '_grenzlotsen_intro', true);

                        if ($intro_text) {
                            echo '<div class="intro-text"><p>' . wp_kses_post($intro_text) . '</p></div>';
                        }

                        $image_id = get_post_meta(get_the_ID(), '_grenzlotsen_image', true);
                        $image_url = wp_get_attachment_url($image_id);

                        if ($image_url) {
                            echo '<div class="hero-img-wrapper"><img src="' . esc_url($image_url) . '" alt="' . esc_attr($title_big) . '"></div>';
                        }
                    ?>

                    <style>
                        .hero-img-wrapper {
                            padding: 76px 0 50px 0;
                        }

                        section.main-content {
                            padding-bottom: 8rem;
                        }

                        .session-meta-wrapper {
                            padding-bottom: 4rem;
                        }
                    </style>

                </div>
            </div>

            <!-- REAL CODE -->
            <section class="main-content">

                <!-- <div class="main-content-inner block">
                    <?php the_content(); ?>
                </div> -->

                <div class="page-builder-output">    
                    <?php include get_template_directory() . '/template-parts/page-builder.php'; ?>         
                </div>

                

                <!-- Meta Boxes Output -->
                <?php
                    $mein_text_input = get_post_meta(get_the_ID(), '_mein_text_input', true);
                    if ($mein_text_input) {
                        echo '<div class="mein-text-input">' . wp_kses_post($mein_text_input) . '</div>';
                    }
                ?>

                <?php
                wp_link_pages(array(
                    'before' => '<div class="page-links">' . esc_html__('Pages:', 'zollwaerts-theme'),
                    'after'  => '</div>',
                ));
                ?>
            </section> <!-- End "block" class -->

            <!-- Session Meta 
            <div class="session-meta-wrapper block">
                <div class="post-meta">
                    <div class="author-info">
                        <?php echo get_avatar( get_the_author_meta( 'ID' ), 64 ); // 64 ist die Größe des Gravatars ?>
                        <p>
                            <?php the_author(); ?>
                        </p>
                    </div>

                    <span class="vertical-divider"></span>

                    <div class="last-modified">
                        <?php 
                        // Zuletzt geändert Datum ausgeben
                        echo '<p>' . esc_html__('Zuletzt aktualisiert:', 'zollwaerts-theme') . ' ' . esc_html( get_the_modified_date() ) . '</p>';
                        ?>
                    </div>

                    <span class="vertical-divider"></span>

                    <div class="published">
                        <?php
                        // Publiziert Datum ausgeben
                        echo '<p>' . esc_html__('Publiziert:', 'zollwaerts-theme') . ' ' . esc_html( get_the_date() ) . '</p>';
                        ?>
                    </div>
                </div>
            </div>
            -->

        </article>

        <div class="more-events block">
            <?php
            // Aktuelle Session-ID abrufen, um sie von der Abfrage auszuschließen
            $current_session_id = get_the_ID();

            // Meta-Daten der aktuellen Session abrufen
            $current_date = get_post_meta($current_session_id, '_session_date', true);
            $current_audiences = wp_get_post_terms($current_session_id, 'target_audience', array("fields" => "ids"));
            $current_types = wp_get_post_terms($current_session_id, 'session_type', array("fields" => "ids"));

            // Alle Sessions abrufen
            $all_sessions = new WP_Query(array(
                'post_type' => 'sessions',
                'posts_per_page' => -1, // Alle Sessions abrufen
                'post__not_in' => array($current_session_id), // Schließt die aktuelle Session aus
                'meta_query' => array(
                    array(
                        'key' => '_no_link_to_detail',
                        'value' => '1',
                        'compare' => '!=' // Nur Sessions einschließen, die KEINEN "Kein Link zur Detailseite" haben
                    )
                )
            ));

            $relevant_sessions = array();

            if ($all_sessions->have_posts()) {
                while ($all_sessions->have_posts()) {
                    $all_sessions->the_post();
                    $relevance = 0;

                    // Datum überprüfen
                    $session_date = get_post_meta(get_the_ID(), '_session_date', true);
                    if ($session_date == $current_date) {
                        $relevance += 1;
                    }

                    // Target Audience überprüfen
                    $session_audiences = wp_get_post_terms(get_the_ID(), 'target_audience', array("fields" => "ids"));
                    if (!empty(array_intersect($current_audiences, $session_audiences))) {
                        $relevance += 3;
                    }

                    // Session Type überprüfen
                    $session_types = wp_get_post_terms(get_the_ID(), 'session_type', array("fields" => "ids"));
                    if (!empty(array_intersect($current_types, $session_types))) {
                        $relevance += 2;
                    }

                    // Wenn Relevanz vorhanden, Session zur Liste hinzufügen
                    if ($relevance > 0) {
                        $relevant_sessions[get_the_ID()] = $relevance;
                    }
                }

                // Nach Relevanz sortieren (absteigend)
                arsort($relevant_sessions);

                // Die Top 3 relevanten Sessions anzeigen
                $relevant_sessions = array_slice($relevant_sessions, 0, 3, true);

                if (!empty($relevant_sessions)) {
                    echo '<div class="related-sessions">';
                    echo '<h2>Weitere Sessions</h2>';
                    echo '<ul>';

                    foreach ($relevant_sessions as $session_id => $relevance) {

                        echo '<li><a href="' . get_permalink($session_id) . '">';

                            setup_postdata(get_post($session_id));
                            
                            // Beitragsbild
                            if (has_post_thumbnail($session_id)) {
                                echo '<div class="session-thumbnail">';
                                echo get_the_post_thumbnail($session_id, 'large');
                                echo '</div>';
                            }

                            echo '<div class="content">';

                                // Session Type
                                $session_types = wp_get_post_terms($session_id, 'session_type', array("fields" => "names"));
                                if (!empty($session_types)) {
                                    echo '<p>' . esc_html(implode(', ', $session_types)) . '</p>';
                                }

                                // Titel
                                echo '<h4>' . get_the_title($session_id) . '</h4>';


                                echo '<div class="key-informations">';
                                    // Datum
                                    $session_date = get_post_meta($session_id, '_session_date', true);
                                    if ($session_date) {
                                        echo '<p>';
                                        echo '  <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                    <path fill-rule="evenodd" d="M5 5a1 1 0 0 0 1-1 1 1 0 1 1 2 0 1 1 0 0 0 1 1h1a1 1 0 0 0 1-1 1 1 0 1 1 2 0 1 1 0 0 0 1 1h1a1 1 0 0 0 1-1 1 1 0 1 1 2 0 1 1 0 0 0 1 1 2 2 0 0 1 2 2v1a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V7a2 2 0 0 1 2-2ZM3 19v-7a1 1 0 0 1 1-1h16a1 1 0 0 1 1 1v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2Zm6.01-6a1 1 0 1 0-2 0 1 1 0 0 0 2 0Zm2 0a1 1 0 1 1 2 0 1 1 0 0 1-2 0Zm6 0a1 1 0 1 0-2 0 1 1 0 0 0 2 0Zm-10 4a1 1 0 1 1 2 0 1 1 0 0 1-2 0Zm6 0a1 1 0 1 0-2 0 1 1 0 0 0 2 0Zm2 0a1 1 0 1 1 2 0 1 1 0 0 1-2 0Z" clip-rule="evenodd"/>
                                                </svg>';
                                        echo date('d.m.Y', strtotime($session_date)) . '</p>';
                                        echo '</p>';
                                    }

                                    // Uhrzeit
                                    $start_time = get_post_meta($session_id, '_session_start_time', true);
                                    $end_time = get_post_meta($session_id, '_session_end_time', true);
                                    if ($start_time && $end_time) {
                                        echo '<p>';
                                        echo '  <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                    <path fill-rule="evenodd" d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm11-4a1 1 0 1 0-2 0v4a1 1 0 0 0 .293.707l3 3a1 1 0 0 0 1.414-1.414L13 11.586V8Z" clip-rule="evenodd"/>
                                                </svg>';
                                        echo esc_html($start_time) . ' - ' . esc_html($end_time);
                                        echo '</p>';
                                    }

                                    // Location (Session Track)
                                    $session_locations = wp_get_post_terms($session_id, 'session_location', array("fields" => "names"));
                                    if (!empty($session_locations)) {
                                        echo '<p>';
                                        echo '  <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                    <path fill-rule="evenodd" d="M11.906 1.994a8.002 8.002 0 0 1 8.09 8.421 7.996 7.996 0 0 1-1.297 3.957.996.996 0 0 1-.133.204l-.108.129c-.178.243-.37.477-.573.699l-5.112 6.224a1 1 0 0 1-1.545 0L5.982 15.26l-.002-.002a18.146 18.146 0 0 1-.309-.38l-.133-.163a.999.999 0 0 1-.13-.202 7.995 7.995 0 0 1 6.498-12.518ZM15 9.997a3 3 0 1 1-5.999 0 3 3 0 0 1 5.999 0Z" clip-rule="evenodd"/>
                                                </svg>';
                                        echo esc_html(implode(', ', $session_locations));
                                        echo '</p>';
                                    }

                                    // Target Audience
                                    $session_audiences = wp_get_post_terms($session_id, 'target_audience', array("fields" => "names"));
                                    if (!empty($session_audiences)) {
                                        echo '<p>';
                                        echo '  <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="currentColor" viewBox="0 0 24 24">
                                                    <path fill-rule="evenodd" d="M12 6a3.5 3.5 0 1 0 0 7 3.5 3.5 0 0 0 0-7Zm-1.5 8a4 4 0 0 0-4 4 2 2 0 0 0 2 2h7a2 2 0 0 0 2-2 4 4 0 0 0-4-4h-3Zm6.82-3.096a5.51 5.51 0 0 0-2.797-6.293 3.5 3.5 0 1 1 2.796 6.292ZM19.5 18h.5a2 2 0 0 0 2-2 4 4 0 0 0-4-4h-1.1a5.503 5.503 0 0 1-.471.762A5.998 5.998 0 0 1 19.5 18ZM4 7.5a3.5 3.5 0 0 1 5.477-2.889 5.5 5.5 0 0 0-2.796 6.293A3.501 3.501 0 0 1 4 7.5ZM7.1 12H6a4 4 0 0 0-4 4 2 2 0 0 0 2 2h.5a5.998 5.998 0 0 1 3.071-5.238A5.505 5.505 0 0 1 7.1 12Z" clip-rule="evenodd"/>
                                                </svg>';
                                        echo esc_html(implode(', ', $session_audiences));
                                        echo '</p>';
                                    }
                                    echo '</div>';
                                echo '</div>';

                            echo '</a>';
                        echo '</li>';
                    }

                    echo '</ul>';
                    echo '</div>';
                }
                wp_reset_postdata();
            }
            ?>

            <style>
                div.tagline {
                    color: var(--gray-40);
                    font-size: 1.5rem;
                    font-weight: 500;
                }

                .more-events.block {
                    margin-bottom: 6rem;
                }
                .related-sessions ul {
                    display: grid;
                    grid-template-columns: 1fr 1fr 1fr;
                    gap: 2rem;
                    margin-left: 0;
                }

                .related-sessions ul li {
                    list-style: none; 
                    border-radius: 1rem;
                    overflow: hidden;
                    border: solid 1px #EBEEF1;
                    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                    transition: box-shadow 0.3s ease;
                    margin-left: 0;
                    padding: 0;
                }

                .more-events.block .content {
                    padding: 1rem;
                }

                .related-sessions .content h4 {
                    margin-bottom: 1rem;
                }

                .more-events.block .content .key-informations p {
                    display: flex !important;
                    margin-bottom: 0.5rem;
                }

                .more-events.block .content .key-informations p svg {
                    margin-right: 0.5rem;
                    min-width: 24px;
                    min-height: 24px;
                    margin-top: 4px;
                }

                .more-events.block .content .key-informations p svg path {
                    fill: #AFAFC7;
                }

                @media screen and (max-width: 999px) {
                    .related-sessions ul {
                        grid-template-columns: 1fr;
                    }
                }


                /* Other Styles */
                /* ol */ 
                .single-post article .main-content ol {
                    margin-left: 1rem;
                }

                .single-post article .main-content ol li {
                    line-height: 2;
                    margin-left: 0.5rem;
                    padding-left: 1rem;
                    padding-bottom: 2rem;
                }
                /* .single-post article ol li:last-child {
                    padding-bottom: 0;
                }  */

                /* ul */
                ul {
                    margin-left: 1rem;
                }

                main ul li {
                    line-height: 2;
                    margin-left: 0.5rem;
                    padding-left: 1rem;
                    padding-bottom: 1rem;
                    position: relative;
                }
                ul.check_bullist li {
                    /* Entfernt die Standard-Punkte */
                    list-style: none; 
                }

                ul.check_bullist li::before {
                    content: '';
                    position: absolute;
                    left: -1.4rem;
                    top: 1.2rem;
                    transform: translateY(-50%);
                    width: 20px;
                    height: 20px;
                    background-size: contain;
                    background-repeat: no-repeat;
                    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><circle cx="10" cy="10" r="10" fill="red"/></svg>'); /* Dein SVG-Code hier */
                    background-image: url('data:image/svg+xml;utf8,%3Csvg%20width%3D%2220px%22%20height%3D%2220px%22%20viewBox%3D%220%200%2020%2020%22%20version%3D%221.1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%3E%3Ctitle%3Eicons%2Fchecker%3C%2Ftitle%3E%3Cg%20id%3D%22wissen---blog%22%20stroke%3D%22none%22%20stroke-width%3D%221%22%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20id%3D%22blog-detail%22%20transform%3D%22translate(-294%2C%20-2959)%22%20fill%3D%22%23BF1692%22%20fill-rule%3D%22nonzero%22%3E%3Cg%20id%3D%22section%22%20transform%3D%22translate(294%2C%20226)%22%3E%3Cg%20id%3D%22item%22%20transform%3D%22translate(0%2C%201912)%22%3E%3Cg%20id%3D%22icons%2Fchecker%22%20transform%3D%22translate(0%2C%20823.1875)%22%3E%3Cpolygon%20points%3D%220%208.52273215%202.85714205%205.68183038%207.14285647%209.94319646%2017.1428577%200%2020%202.84090177%207.14285647%2015.625%22%3E%3C%2Fpolygon%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E');
                }

                /* condensed check_bullist */
                ul.condensed li {
                    line-height: 2;
                    margin-left: 0.5rem;
                    padding-left: 1rem;
                    padding-bottom: 0.5rem;
                    position: relative;
                    font-size: 18px;
                }

                ul.condensed.check_bullist li::before {
                    background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='18px' height='18px' viewBox='0 0 18 18' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Eicons/check%3C/title%3E%3Cg id='wissen---ebooks' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='ebooks-detailseite' transform='translate(-294, -1672)' fill='%23BF1692'%3E%3Cg id='section' transform='translate(294, 1119)'%3E%3Cg id='list' transform='translate(0, 90)'%3E%3Cg id='item' transform='translate(0, 454)'%3E%3Cg id='icons/check' transform='translate(0, 9)'%3E%3Cpath d='M9,0 C13.9705627,0 18,4.02943725 18,9 C18,13.9705627 13.9705627,18 9,18 C4.02943725,18 0,13.9705627 0,9 C0,4.02943725 4.02943725,0 9,0 Z M12.4033616,5.29411765 L7.63865514,10.0107021 L5.59663827,7.9893106 L4.23529412,9.33690071 L7.63865514,12.7058824 L13.7647059,6.64170776 L12.4033616,5.29411765 Z'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
                }
            </style>

            </div>

        <?php
            // Wenn Kommentare erlaubt sind oder wir mindestens einen Kommentar haben, das Kommentar-Template laden
            if (comments_open() || get_comments_number()) :
                echo '<section class="comment-section block" style="margin-top: 300px;">';
                comments_template();
                echo '</section>';
            endif;
        ?>
        <?php
    endwhile; // Ende der Loop
    ?>
</main>

<?php get_template_part('global-modules/global-modals'); ?>

<?php 
    // get_sidebar(); 
?>
<?php get_footer(); ?>

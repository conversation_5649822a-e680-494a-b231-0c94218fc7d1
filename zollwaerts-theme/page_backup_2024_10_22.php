<?php get_header(); ?>


<?php
    if (is_preview()) {
        // Überprüfen, ob es eine Revision gibt
        $revision_id = wp_is_post_revision(get_the_ID());

        if ($revision_id) {
            // Hole die Meta-Daten aus der Revision
            $json_data = get_metadata('post', $revision_id, '_page_builder_blocks', true);
        } else {
            // Falls keine Revision vorhanden ist, hole die regulären Meta-Daten
            $json_data = get_post_meta(get_the_ID(), '_page_builder_blocks', true);
        }
    } else {
        // Keine Vorschau, reguläre Meta-Daten holen
        $json_data = get_post_meta(get_the_ID(), '_page_builder_blocks', true);
    }

    /*
    /** OLD AND WORKING BUT NOT WITH PREVIEW */
    /*
    $json_data = get_post_meta(get_the_ID(), '_page_builder_blocks', true);
    */

    // Ver<PERSON><PERSON> von wp_unslash, um unnötige Escape-Zeichen zu entfernen
    $unslashed_data = wp_unslash($json_data);

    // Dekodieren der JSON-Daten in ein PHP-Array
    $page_builder_blocks = json_decode($unslashed_data, true); 
?>

<?php get_template_part('global-modules/page-builder-modals'); ?>
<?php get_template_part('global-modules/global-modals'); ?>


<?php 
// $has_hero_block = false;

// // TODO: if-check if "$has_hero_block" is true
// function enqueue_threejs_scripts() {
//     $script_path = get_template_directory() . '/js/libraries/three.module.min.js';
//     $script_url = get_template_directory_uri() . '/js/libraries/three.module.min.js';
//     // wp_enqueue_script('custom-threejs', get_template_directory_uri() . '/js/custom-threejs.js', array('three-js'), null, true);

//     if (file_exists($script_path)) {
//         wp_enqueue_script('three-js', $script_url, array(), null, true);
//         echo '<script>console.log("threeJS available");</script>';
//     } else {
//         echo '<script>console.error("ThreeJS NOT FOUND");</script>';
//     }
// }
// add_action('wp_enqueue_scripts', 'enqueue_threejs_scripts');
?>

<main>
    <!-- <p style="width: 300px; height: 300px; background: red;">Direkter HTML-Inhalt zum Testen</p> -->

    <?php
    while (have_posts()) : the_post();
        ?>
        <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
            <!-- <header class="entry-header">
                <h1 class="entry-title"><?php the_title(); ?></h1>
            </header> -->
            <div class="entry-content">
                <?php 
                    // the_content(); 
                ?>

                <div class="page-builder-output">
                    <!-- Meta Boxes Output -->
                    <?php
                        // TODO: Das gibt es aktuell noch in der DB, wird aber im Page Builder nicht verwendet 
                        // if ($mein_text_input) {
                        //     echo '<div class="mein-text-input">' . wp_kses_post($mein_text_input) . '</div>';
                        // }

                        // Page Builder Blocks Output
                        /* Original Code der, abgesehen von den "n" bei Umbrüchen, funktionierte
                        $page_builder_blocks = get_post_meta( get_the_ID(), '_page_builder_blocks', true );
                        */

                        if ($page_builder_blocks && is_array($page_builder_blocks)) {
                            foreach ($page_builder_blocks as $block) {
                                // echo '<div style="border: solid red 2px;">';
                                // echo htmlspecialchars(json_encode($block));
                                // echo '</div>';

                                // Block überspringen, wenn 'disableModule' true ist
                                if (isset($block['disableModule']) && $block['disableModule'] === true) {
                                    continue; // Überspringt den aktuellen Block und geht zum nächsten
                                }

                                // check if block is flexible content
                                $is_flexible_content = $block['type'] === 'flexible-content';
                                $appearance = '';
                                if ($is_flexible_content && isset($block['advancedSettings']['appearance'])) {
                                    $appearance = $block['advancedSettings']['appearance'] ? $block['advancedSettings']['appearance'] : 'default';
                                }

                                // check if section is testimonial section
                                $is_testimonial_section = $block['type'] === 'testimonial-section';
                                if ($is_testimonial_section) {
                                    $appearance = 'gray-fullwidth';
                                }

                                echo '<div class="overflow-wrapper ' . $appearance . '">';

                                switch ( $block['type'] ) {
                                    case 'hero':
                                        $block_type = 'hero';
                                        $has_hero_block = true;
                                        break;
                                    case 'text-image':
                                        $block_type = 'text-image';
                                        break;
                                    case 'flexible-content':
                                        $block_type = 'flexible-content';
                                        break;
                                    case 'testimonial-section':  // Dein neuer Blocktyp für testimonial-section
                                        $block_type = 'testimonial-section';
                                        break;
                                    case 'featureGrid':  // Dein neuer Blocktyp für featureGrid
                                        $block_type = 'featureGrid';
                                        break;
                                    case 'imageGrid':  // Dein neuer Blocktyp für imageGrid
                                        $block_type = 'imageGrid';
                                        break;
                                    case 'post-list':  // Dein neuer Blocktyp für post-list
                                        $block_type = 'post-list';
                                        break;
                                    case 'success-story-teaser':  // Dein neuer Blocktyp für success-story-teaser
                                        $block_type = 'success-story-teaser';
                                        break;
                                    case 'cta-section':  // Dein neuer Blocktyp für cta-section
                                        $block_type = 'cta-section';
                                        break;
                                    case 'slider-section':  // Dein neuer Blocktyp für slider-section
                                        $block_type = 'slider-section';
                                        break;
                                    case 'sticky-text-image':  // Dein neuer Blocktyp für sticky-text-image
                                        $block_type = 'sticky-text-image';
                                        break;
                                    case 'pricing-table':  // Dein neuer Blocktyp für pricing-table
                                        $block_type = 'pricing-table';
                                        break;
                                    case 'agenda-overview':  // Dein neuer Blocktyp für agenda-overview
                                        $block_type = 'agenda-overview';
                                        break;
                                    case 'modal':  // Dein neuer Blocktyp für modal
                                        $block_type = 'modal';
                                        break;
                                    case 'raw-html':  // Dein neuer Blocktyp für HTML-Inhalte
                                        $block_type = 'raw-html';
                                        break;
                                    case 'headline':
                                        $block_type = 'headline';
                                        break;
                                    case 'paragraph':
                                        $block_type = 'paragraph';
                                        break;
                                    default:
                                        $block_type = '';
                                        break;
                                }
                                if ( $block_type ) {
                                    set_query_var( 'block', $block );

                                    // TODO: make actual fullwidth settings 
                                    // $is_fullwidth = $block['advancedSettings']['fullwidth'];
                                    $fullwidth_types = ['hero', 'slider-section'];
                                    $is_fullwidth = in_array($block_type, $fullwidth_types) ? 'fullwidth' : '';

                                    // echo '<p>' . $block_type . ' ' . $is_fullwidth . '</p>';


                                    $spacing_top = isset($block['advancedSettings']['spacing']['top']) ? $block['advancedSettings']['spacing']['top'] : 'default_value';
                                    $spacing_right = isset($block['advancedSettings']['spacing']['right']) ? $block['advancedSettings']['spacing']['right'] : 'default_value';
                                    $spacing_bottom = isset($block['advancedSettings']['spacing']['bottom']) ? $block['advancedSettings']['spacing']['bottom'] : 'default_value';
                                    $spacing_left = isset($block['advancedSettings']['spacing']['left']) ? $block['advancedSettings']['spacing']['left'] : 'default_value';

                                    $custom_spacing_top = isset($block['advancedSettings']['spacing']['topCustom']) ? $block['advancedSettings']['spacing']['topCustom'] : 'default_value';
                                    $custom_spacing_right = isset($block['advancedSettings']['spacing']['rightCustom']) ? $block['advancedSettings']['spacing']['rightCustom'] : 'default_value';
                                    $custom_spacing_bottom = isset($block['advancedSettings']['spacing']['bottomCustom']) ? $block['advancedSettings']['spacing']['bottomCustom'] : 'default_value';
                                    $custom_spacing_left = isset($block['advancedSettings']['spacing']['leftCustom']) ? $block['advancedSettings']['spacing']['leftCustom'] : 'default_value';


                                    $isSpacingTopCustom = $spacing_top === 'custom';
                                    $isSpacingRightCustom = $spacing_right === 'custom';
                                    $isSpacingBottomCustom = $spacing_bottom === 'custom';
                                    $isSpacingLeftCustom = $spacing_bottom === 'custom';

                                    $spacing_styles = '';
                                    if ($isSpacingTopCustom) {
                                        $spacing_styles .= "padding-top: {$custom_spacing_top}px; ";
                                    }
                                    if ($isSpacingRightCustom) {
                                        $spacing_styles .= "padding-right: {$custom_spacing_right}px; ";
                                    }
                                    if ($isSpacingBottomCustom) {
                                        $spacing_styles .= "padding-bottom: {$custom_spacing_bottom}px; ";
                                    }
                                    if ($isSpacingLeftCustom) {
                                        $spacing_styles .= "padding-left: {$custom_spacing_left}px; ";
                                    }
                                    ?>

                                    <div 
                                        class="block <?php echo $is_fullwidth . ' ' . 'top-spacing-' . $spacing_top ?> <?php echo 'right-spacing-' . $spacing_bottom ?> <?php echo 'bottom-spacing-' . $spacing_bottom ?> <?php echo 'left-spacing-' . $spacing_left ?>" 
                                    >
                                        <div class="block-wrapper" style="<?php echo esc_attr($spacing_styles); ?>">
                                            <?php get_template_part( 'template-parts/block', $block_type ); ?>
                                        </div>
                                    </div>
                                    <?php
                                }

                                echo '</div>';
                            
                            } 
                        } else {
                            echo '<div> <!-- NO BLOCKS --> </div>';
                        }
                    ?>



                    <!-- Row – New building blocks output -->
                    
                    <?php
                        if ($page_builder_blocks && is_array($page_builder_blocks)) {
                            foreach ($page_builder_blocks as $block) {
                                switch ( $block['type'] ) {
                                    case 'row':
                                        $block_type = 'row';
                                        // $has_hero_block = true;
                                        break;
                                }
                                if ( $block_type === 'row' ) {
                                    // set_query_var( 'block', $block );
                                    // echo '<div style="border: solid red 2px;">';
                                    // echo htmlspecialchars(json_encode($block));
                                    // echo '</div>';

                                    if ($block['cols'] && is_array($block['cols'])) {
                                        foreach ($block['cols'] as $col) {

                                            // echo '<div style="border: solid red 2px;">';
                                            // echo htmlspecialchars(json_encode($col));
                                            // echo '</div>';

                                            foreach ($col['content'] as $content) {
                                                // echo '<div style="border: solid red 2px;">';
                                                // echo htmlspecialchars(json_encode($content));
                                                // echo '</div>';

                                                switch ( $content['type'] ) {
                                                    case 'hero':
                                                        $content_type = 'hero';
                                                        $has_hero_content = true;
                                                        break;
                                                    case 'text-image':
                                                        $content_type = 'text-image';
                                                        break;
                                                    case 'featureGrid':  // Dein neuer contenttyp für featureGrid
                                                        $content_type = 'featureGrid';
                                                        break;
                                                    case 'imageGrid':  // Dein neuer contenttyp für imageGrid
                                                        $content_type = 'imageGrid';
                                                        break;
                                                    case 'cta-section':  // Dein neuer contenttyp für cta-section
                                                        $content_type = 'cta-section';
                                                        break;
                                                    case 'accordion':  // Dein neuer contenttyp für accordion
                                                        $content_type = 'accordion';
                                                        break;
                                                    case 'slider-section':  // Dein neuer contenttyp für slider-section
                                                        $content_type = 'slider-section';
                                                        break;
                                                    case 'sticky-text-image':  // Dein neuer contenttyp für sticky-text-image
                                                        $content_type = 'sticky-text-image';
                                                        break;
                                                    case 'post-list':  // Dein neuer Blocktyp für post-list
                                                        $block_type = 'post-list';
                                                        break;
                                                    case 'pricing-table':  // Dein neuer contenttyp für pricing-table
                                                        $content_type = 'pricing-table';
                                                        break;
                                                    case 'modal':  // Dein neuer contenttyp für modal
                                                        $content_type = 'modal';
                                                        break;
                                                    case 'raw-html':  // Dein neuer contenttyp für HTML-Inhalte
                                                        $content_type = 'raw-html';
                                                        break;
                                                    case 'headline':
                                                        $content_type = 'headline';
                                                        break;
                                                    case 'paragraph':
                                                        $content_type = 'paragraph';
                                                        break;
                                                    default:
                                                        $content_type = '';
                                                        break;
                                                }
                                                if ( $content_type ) {
                                                    set_query_var( 'block', $content );

                                                    // TODO: make actual fullwidth settings 
                                                    // $is_fullwidth = $block['advancedSettings']['fullwidth'];
                                                    $fullwidth_types = ['hero', 'slider-section'];
                                                    $is_fullwidth = in_array($content_type, $fullwidth_types) ? 'fullwidth' : '';

                                                    // echo '<p>' . $content_type . ' ' . $is_fullwidth . '</p>';


                                                    $spacing_top = $block['advancedSettings']['spacing']['top'];
                                                    $spacing_right = $block['advancedSettings']['spacing']['right'];
                                                    $spacing_bottom = $block['advancedSettings']['spacing']['bottom'];
                                                    $spacing_left = $block['advancedSettings']['spacing']['left'];
                                                    
                                                    $custom_spacing_top = $block['advancedSettings']['spacing']['topCustom'];
                                                    $custom_spacing_right = $block['advancedSettings']['spacing']['rightCustom'];
                                                    $custom_spacing_bottom = $block['advancedSettings']['spacing']['bottomCustom'];
                                                    $custom_spacing_left = $block['advancedSettings']['spacing']['leftCustom'];

                                                    $isSpacingTopCustom = $spacing_top === 'custom';
                                                    $isSpacingRightCustom = $spacing_right === 'custom';
                                                    $isSpacingBottomCustom = $spacing_bottom === 'custom';
                                                    $isSpacingLeftCustom = $spacing_bottom === 'custom';

                                                    $spacing_styles = '';
                                                    if ($isSpacingTopCustom) {
                                                        $spacing_styles .= "padding-top: {$custom_spacing_top}px; ";
                                                    }
                                                    if ($isSpacingRightCustom) {
                                                        $spacing_styles .= "padding-right: {$custom_spacing_right}px; ";
                                                    }
                                                    if ($isSpacingBottomCustom) {
                                                        $spacing_styles .= "padding-bottom: {$custom_spacing_bottom}px; ";
                                                    }
                                                    if ($isSpacingLeftCustom) {
                                                        $spacing_styles .= "padding-left: {$custom_spacing_left}px; ";
                                                    }
                                                    ?>

                                                    <div 
                                                        class="block <?php echo $is_fullwidth . ' ' . 'top-spacing-' . $spacing_top ?> <?php echo 'right-spacing-' . $spacing_bottom ?> <?php echo 'bottom-spacing-' . $spacing_bottom ?> <?php echo 'left-spacing-' . $spacing_left ?>" 
                                                    >
                                                        <div class="block-wrapper" style="<?php echo esc_attr($spacing_styles); ?>">
                                                            <?php get_template_part( 'template-parts/block', $content_type ); ?>
                                                        </div>
                                                    </div>
                                                    <?php
                                                }


                                            }

                                            /*
                                                
                                            */
                                    
                                        } 
                                    }
                                }
                            
                            } 
                        } 
                    ?>
                
                </div>

                <?php
                wp_link_pages(array(
                    'before' => '<div class="page-links">' . esc_html__('Pages:', 'zollwaerts-theme'),
                    'after'  => '</div>',
                ));
                ?>


            </div>
        </article>
        <?php
        // Wenn Kommentare erlaubt sind oder wir mindestens einen Kommentar haben, das Kommentar-Template laden
        if (comments_open() || get_comments_number()) :
            comments_template();
        endif;
    endwhile; // Ende der Loop
    ?>
</main>

<?php 
    // $useSidebar = get_field('use_sidebar');
    $useSidebar = false;
    if($useSidebar) {
        get_sidebar(); 
    }
?>

<?php get_footer(); ?>

</div>




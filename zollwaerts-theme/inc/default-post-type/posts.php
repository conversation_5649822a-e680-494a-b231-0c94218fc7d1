<?php
/**
 * Standart Beiträge/Posts
 */
// Meta boxses für "Beiträge"/"Posts" definieren 
// Metaboxen zu Beiträgen hinzufügen
function my_custom_post_meta_boxes() {
    add_meta_box(
        'grenzlotsen_post_intro_meta_box', // ID der Metabox
        __('Beitrags Intro', 'textdomain'), // Titel der Metabox
        'grenzlotsen_post_intro_meta_box_callback', // Callback-Funktion für die Inhalte
        'post', // Beitragstyp
        'normal', // Kontext (normal, side, etc.)
        'high' // Priorität (high, low, etc.)
    );
}
add_action('add_meta_boxes', 'my_custom_post_meta_boxes');

function grenzlotsen_post_intro_meta_box_callback($post) {
    // Daten abrufen
    $title_big = get_post_meta($post->ID, '_grenzlotsen_title_big', true);
    $title_small = get_post_meta($post->ID, '_grenzlotsen_title_small', true);
    $intro_text = get_post_meta($post->ID, '_grenzlotsen_intro', true);
    $image_id = get_post_meta($post->ID, '_grenzlotsen_image', true);
    $image_url = $image_id ? wp_get_attachment_url($image_id) : '';

    // Title Big
    echo '<label for="grenzlotsen_title_big_field">'.__('Title Big', 'textdomain').'</label>';
    echo '<input type="text" id="grenzlotsen_title_big_field" name="grenzlotsen_title_big_field" value="' . esc_attr($title_big) . '" class="widefat" />';
    echo '<br>';
    echo '<br>';
    
    // Title Small
    echo '<label for="grenzlotsen_title_small_field">'.__('Title Small', 'textdomain').'</label>';
    echo '<input type="text" id="grenzlotsen_title_small_field" name="grenzlotsen_title_small_field" value="' . esc_attr($title_small) . '" class="widefat" />';
    echo '<br>';
    echo '<br>';

    // Intro Text
    // echo '<label for="grenzlotsen_post_intro_field">'.__('Intro Text', 'textdomain').'</label>';
    echo '<p>Intro Text</p>';
    wp_editor($intro_text, 'grenzlotsen_post_intro_field', array(
        'textarea_name' => 'grenzlotsen_post_intro_field',
        'media_buttons' => false,
        'textarea_rows' => 10,
        'tinymce' => array(
            'toolbar1' => 'bold,italic,underline,|,bullist,numlist,|,link,unlink,|,grenzlotsen_button',
            'toolbar2' => '',
        ),
        'quicktags' => true
    ));

    // Bild hochladen
    // echo '<label for="grenzlotsen_image_field">'.__('Intro Image', 'textdomain').'</label>';
    // echo '<input type="hidden" id="grenzlotsen_image_field" name="grenzlotsen_image_field" value="' . esc_attr($image_id) . '" />';
    // echo '<div><img id="grenzlotsen_image_preview" src="' . esc_url($image_url) . '" style="max-width: 100%; height: auto; display: ' . ($image_url ? 'block' : 'none') . ';" /></div>';
    // echo '<button type="button" class="button" id="grenzlotsen_image_upload_button">'.__('Upload Image', 'textdomain').'</button>';
    // echo '<button type="button" class="button" id="grenzlotsen_image_remove_button" style="display: ' . ($image_url ? 'inline-block' : 'none') . ';">'.__('Remove Image', 'textdomain').'</button>';

    // JavaScript für Bild-Upload
    ?>
    <script>
    jQuery(document).ready(function($) {
        var file_frame;
        $('#grenzlotsen_image_upload_button').on('click', function(e) {
            e.preventDefault();

            if (file_frame) {
                file_frame.open();
                return;
            }

            file_frame = wp.media.frames.file_frame = wp.media({
                title: '<?php _e('Select or Upload Image', 'textdomain'); ?>',
                button: {
                    text: '<?php _e('Use this image', 'textdomain'); ?>',
                },
                multiple: false
            });

            file_frame.on('select', function() {
                var attachment = file_frame.state().get('selection').first().toJSON();
                $('#grenzlotsen_image_field').val(attachment.id);
                $('#grenzlotsen_image_preview').attr('src', attachment.url).show();
                $('#grenzlotsen_image_remove_button').show();
            });

            file_frame.open();
        });

        $('#grenzlotsen_image_remove_button').on('click', function(e) {
            e.preventDefault();
            $('#grenzlotsen_image_field').val('');
            $('#grenzlotsen_image_preview').hide();
            $('#grenzlotsen_image_remove_button').hide();
        });
    });
    </script>
    <?php
}

function save_my_custom_meta_box_data($post_id) {
    // Title Big
    if (array_key_exists('grenzlotsen_title_big_field', $_POST)) {
        update_post_meta(
            $post_id,
            '_grenzlotsen_title_big',
            sanitize_text_field($_POST['grenzlotsen_title_big_field'])
        );
    }

    // Title Small
    if (array_key_exists('grenzlotsen_title_small_field', $_POST)) {
        update_post_meta(
            $post_id,
            '_grenzlotsen_title_small',
            sanitize_text_field($_POST['grenzlotsen_title_small_field'])
        );
    }

    // Intro Text
    if (array_key_exists('grenzlotsen_post_intro_field', $_POST)) {
        update_post_meta(
            $post_id,
            '_grenzlotsen_intro',
            wp_kses_post($_POST['grenzlotsen_post_intro_field'])
        );
    }

    // Möglicherweise hier die Daten in default post content einfügen 

}
add_action('save_post', 'save_my_custom_meta_box_data');





/*** FILTER SCRIPT FOR POSTS ***/
function enqueue_post_filter_script() {
    // Nur auf Post-Archivseiten und einzelnen Post-Seiten laden
    if (is_post_type_archive('post') || is_singular('post')|| is_home()) {
        wp_enqueue_script('post-filter-script', get_template_directory_uri() . '/js/post-filter-script.js', array('jquery'), null, true);

        // Lokalisierung der AJAX-Daten für Posts
        wp_localize_script('post-filter-script', 'ajax_post_params', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'post_type' => 'post',
            'taxonomy' => 'category',
        ));
    }
}
add_action('wp_enqueue_scripts', 'enqueue_post_filter_script');

function filter_posts_by_category() {
    $category_id = isset($_POST['category_id']) && $_POST['category_id'] !== null ? intval($_POST['category_id']) : null;
    $paged = isset($_POST['paged']) ? intval($_POST['paged']) : "1"; // Aktuelle Seite von AJAX

    // WP_Query-Argumente für Posts
    $args = array(
        'post_type' => 'post',
        'posts_per_page' => 10,
        'paged' => $paged, // Paged für Pagination
        'post_status' => 'publish', // Nur veröffentlichte Beiträge
    );

    // Kategorienfilter
    if ($category_id) {
        $args['tax_query'] = array(
            array(
                'taxonomy' => 'category',
                'field' => 'term_id',
                'terms' => $category_id,
            ),
        );
    }

    // Query und Ausgabe
    $query = new WP_Query($args);
    ob_start();
    if ($query->have_posts()) :
        echo '<div class="blog post-list">';
            while ($query->have_posts()) : $query->the_post(); ?>
                <div class="single-post-item" style="view-transition-name: post-<?php the_ID(); ?>">
                    <a href="<?php the_permalink(); ?>">
                        <div class="single-post-item-inner">
                            <?php if (has_post_thumbnail()) : ?>
                                <div class="post-thumbnail">
                                    <?php the_post_thumbnail('blog-thumb'); ?>
                                </div>
                            <?php endif; ?>

                            <div class="post-meta">
                                <span class="post-date"><?php echo get_the_date('d. F Y'); ?></span>
                                <span> | </span>
                                <span class="post-author"><?php the_author(); ?></span>
                            </div>

                            <h2><?php the_title(); ?></h2>
                            <div class="post-excerpt"><?php the_excerpt(); ?></div>

                            <div class="read-more">
                                <p>Weiterlesen</p>
                                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="12" height="12">
                                    <path fill="#11114A" transform="matrix(-1 0 0 1 12 0.0812988)" d="M0.010917343 4.8662171L0.0017641115 4.9527488L0.010285715 5.0004244L0 5.0004597L0.0042217541 5.0822744C0.0051675173 5.0910196 0.0064603202 5.1014748 0.0083995117 5.1171575L0.035038877 5.236444L0.078554988 5.3484674L0.1172509 5.420835L0.16688135 5.4944906C0.1927705 5.5286436 0.22035234 5.5595098 0.25384098 5.5914679L4.5367041 9.7558832C4.8714046 10.081326 5.4141178 10.081379 5.7488871 9.7560034L5.8201995 9.6775103C6.0816836 9.3506546 6.0579653 8.8780003 5.7490106 8.5775919L2.9262857 5.8328538L11.142933 5.8328381C11.616307 5.832799 12 5.4597206 12 4.9995403L11.994232 4.9023609C11.944712 4.4879346 11.582367 4.1662769 11.142784 4.1663132L2.9254286 4.1663284L5.7489448 1.422472C6.083684 1.0970589 6.083684 0.56946623 5.7489486 0.24405697C5.4142137 -0.081352323 4.8715005 -0.081352323 4.5367656 0.24405697L0.2435506 4.4179473L0.1963734 4.4692841L0.15299548 4.5248733L0.11413158 4.5845737L0.081366442 4.6456332C0.074457817 4.659668 0.069259398 4.6720638 0.058862571 4.6968551L0.029049635 4.7849994L0.010917343 4.8662171Z"/>
                                </svg>
                            </div>
                        </div>
                    </a>
                </div>
            <?php endwhile;
        echo '</div>';

        // Pagination ausgeben
        echo '<div class="pagination">';
        echo paginate_links(array(
            'total' => $query->max_num_pages,
            'current' => $paged,
            'format' => '?paged=%#%', // Verwende ?paged= für Pagination
            'add_args' => array( // Füge den 'action'-Parameter hinzu
                'action' => 'filter_posts_by_category',
            ),
            'prev_text' => __( 'Vorherige', 'textdomain' ),
            'next_text' => __( 'Nächste', 'textdomain' ),
        ));
        echo '</div>';

    else :
        echo '<p>Keine Beiträge gefunden.</p>';
    endif;
    wp_reset_postdata();

    $output = ob_get_clean();
    if (empty($output)) {
        wp_send_json_error('Keine Inhalte gefunden');
    } else {
        echo $output;
    }

    wp_die();
}
add_action('wp_ajax_filter_posts_by_category', 'filter_posts_by_category');
add_action('wp_ajax_nopriv_filter_posts_by_category', 'filter_posts_by_category');
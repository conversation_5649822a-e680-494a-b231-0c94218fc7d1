<?php
/**
 * Add "Wiki" CPT 
 */
function create_wiki_cpt() {

    $labels = array(
        'name' => _x('Wiki-Listings', 'Post Type General Name', 'textdomain'),
        'singular_name' => _x('Wiki', 'Post Type Singular Name', 'textdomain'),
        'menu_name' => __('Wiki-Listings', 'textdomain'),
        'name_admin_bar' => __('Wiki', 'textdomain'),
        'archives' => __('Wiki Archives', 'textdomain'),
        'attributes' => __('Wiki Attributes', 'textdomain'),
        'parent_item_colon' => __('Parent Wiki:', 'textdomain'),
        'all_items' => __('All Wiki-Listings', 'textdomain'),
        'add_new_item' => __('Add New Wiki', 'textdomain'),
        'add_new' => __('Add New', 'textdomain'),
        'new_item' => __('New Wiki', 'textdomain'),
        'edit_item' => __('Edit Wiki', 'textdomain'),
        'update_item' => __('Update Wiki', 'textdomain'),
        'view_item' => __('View Wiki', 'textdomain'),
        'view_items' => __('View Wiki-Listings', 'textdomain'),
        'search_items' => __('Search Wiki', 'textdomain'),
        'not_found' => __('Not found', 'textdomain'),
        'not_found_in_trash' => __('Not found in Trash', 'textdomain'),
        'featured_image' => __('Featured Image', 'textdomain'),
        'set_featured_image' => __('Set featured image', 'textdomain'),
        'remove_featured_image' => __('Remove featured image', 'textdomain'),
        'use_featured_image' => __('Use as featured image', 'textdomain'),
        'insert_into_item' => __('Insert into Wiki', 'textdomain'),
        'uploaded_to_this_item' => __('Uploaded to this Wiki', 'textdomain'),
        'items_list' => __('Wiki-Listings list', 'textdomain'),
        'items_list_navigation' => __('Wiki-Listings list navigation', 'textdomain'),
        'filter_items_list' => __('Filter wiki list', 'textdomain'),
    );

    $args = array(
        'label' => __('Wiki', 'textdomain'),
        'description' => __('Post Type for Wiki-Listings', 'textdomain'),
        'labels' => $labels,
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt', 'revisions'),
        'taxonomies' => array(), // Keine Standard-Taxonomien
        // 'taxonomies' => array('category', 'post_tag'), // würde Kategorien und Tags von den default Posts/Beiträgen verwenden
        'hierarchical' => false,
        'public' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'menu_position' => 5,
        'menu_icon' => 'dashicons-welcome-learn-more',
        'show_in_admin_bar' => true,
        'show_in_nav_menus' => true,
        'can_export' => true,
        'has_archive' => true,
        'exclude_from_search' => false,
        'publicly_queryable' => true,
        'capability_type' => 'post',
        'show_in_rest' => true, // Gutenberg Unterstützung
        'rewrite' => array('slug' => 'wiki', 'with_front' => false), // Hier wird die Permalink-Struktur festgelegt
    );

    register_post_type('wiki', $args);

}
add_action('init', 'create_wiki_cpt', 0);


/**
 * Add Meta Box for Wiki Post Type
 */
function add_wiki_meta_box() {
    add_meta_box(
        'wiki_custom_meta_box', // ID der Meta Box
        __('Wiki Content Settings', 'textdomain'), // Titel der Meta Box
        'render_wiki_meta_box', // Callback-Funktion zum Anzeigen der Inhalte
        'wiki', // Post-Typ
        'normal', // Platzierung in der linken/main Spalte
        'high' // Hohe Priorität, um sie so weit oben wie möglich zu platzieren
    );
}
add_action('add_meta_boxes', 'add_wiki_meta_box');

/**
 * Render the Meta Box content
 */
function render_wiki_meta_box($post) {
    // Sicherheitstoken hinzufügen
    wp_nonce_field('save_wiki_meta_box_data', 'wiki_meta_box_nonce');

    // Vorhandene Werte abrufen
    $tagline = get_post_meta($post->ID, '_wiki_tagline', true);
    $headline = get_post_meta($post->ID, '_wiki_headline', true);
    $intro_text = get_post_meta($post->ID, '_wiki_intro_text', true);

    // Tagline Feld
    echo '<label for="wiki_tagline_field">'.__('Tagline', 'textdomain').'</label>';
    echo '<input type="text" id="wiki_tagline_field" name="wiki_tagline_field" value="' . esc_attr($tagline) . '" class="widefat" />';
    echo '<br><br>';

    // Headline Feld
    echo '<label for="wiki_headline_field">'.__('Headline', 'textdomain').'</label>';
    echo '<input type="text" id="wiki_headline_field" name="wiki_headline_field" value="' . esc_attr($headline) . '" class="widefat" />';
    echo '<br><br>';

    // Intro-Text TinyMCE Editor ohne Teeny-Mode
    echo '<label for="wiki_intro_text">' . __('Intro Text', 'textdomain') . '</label>';
    wp_editor($intro_text, 'wiki_intro_text', array(
        'textarea_name' => 'wiki_intro_text',
        'media_buttons' => false,
        'textarea_rows' => 10,  // Mehr Zeilen für den Editor
        'tinymce' => array(
            'toolbar1' => 'bold,italic,underline,|,bullist,numlist,|,link,unlink,|,undo,redo',  // Angepasste Toolbar
            'toolbar2' => '',
        ),
        'quicktags' => true // HTML-Bearbeitung ermöglichen
    ));
}

/**
 * Save the Meta Box data
 */
function save_wiki_meta_box_data($post_id) {
    // Sicherheitsüberprüfung
    if (!isset($_POST['wiki_meta_box_nonce']) || !wp_verify_nonce($_POST['wiki_meta_box_nonce'], 'save_wiki_meta_box_data')) {
        return;
    }

    // Überprüfung der Berechtigungen
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Speichern der Tagline
    if (isset($_POST['wiki_tagline_field'])) {
        update_post_meta($post_id, '_wiki_tagline', sanitize_text_field($_POST['wiki_tagline_field']));
    }

    // Speichern der Headline
    if (isset($_POST['wiki_headline_field'])) {
        update_post_meta($post_id, '_wiki_headline', sanitize_text_field($_POST['wiki_headline_field']));
    }

    // Intro-Text speichern
    if (isset($_POST['wiki_intro_text'])) {
        update_post_meta($post_id, '_wiki_intro_text', wp_kses_post($_POST['wiki_intro_text']));
    }
}
add_action('save_post', 'save_wiki_meta_box_data');



/************************ */
/** Move other meta boxes */
/************************ */
/**
 * Move Default Content Editor Above Yoast, but Below Meta Box
 */
function move_default_editor_to_middle() {
    remove_post_type_support('wiki', 'editor'); // Entfernt den Editor aus der Standardposition
    add_meta_box('custom_editor', __('Content', 'textdomain'), 'custom_wp_editor', 'wiki', 'normal', 'default'); // Fügt den Editor mit mittlerer Priorität wieder hinzu
}
add_action('add_meta_boxes', 'move_default_editor_to_middle');

function custom_wp_editor($post) {
    wp_editor($post->post_content, 'content', array('textarea_name' => 'content'));
}

/**
 * Move Excerpt Box Above Yoast SEO but Below Content Editor
 */
function move_excerpt_above_yoast() {
    remove_meta_box('postexcerpt', 'wiki', 'normal'); // Entfernt den Excerpt aus der Standardposition
    add_meta_box('postexcerpt', __('Excerpt'), 'post_excerpt_meta_box', 'wiki', 'normal', 'default'); // Fügt den Excerpt mit mittlerer Priorität wieder hinzu, über Yoast
}
add_action('add_meta_boxes', 'move_excerpt_above_yoast');


/**
 * Yoast SEO Priorität ganz unten
 */
function lower_yoast_seo_priority_to_bottom() {
    return 'low';
}
add_filter('wpseo_metabox_prio', 'lower_yoast_seo_priority_to_bottom');

/**
 * Revisionen Priorität ganz unten
 */
function move_revisions_meta_box() {
    // Entferne die Revisions-Meta-Box
    remove_meta_box('revisionsdiv', 'wiki', 'normal'); // 'wiki' durch deinen CPT ersetzen

    // Füge die Revisions-Meta-Box mit niedriger Priorität wieder hinzu
    add_meta_box('revisionsdiv', __('Revisions'), 'post_revisions_meta_box', 'wiki', 'normal', 'low'); // Setze die Priorität auf 'low'
}
add_action('add_meta_boxes', 'move_revisions_meta_box');



/***************************** */
/* Frontend -> archiv-wiki.php */
/***************************** */
function order_wiki_posts_alphabetically($query) {
    if (!is_admin() && $query->is_main_query() && is_post_type_archive('wiki')) {
        // Sortiere nach Titel in aufsteigender Reihenfolge
        $query->set('orderby', 'title');
        $query->set('order', 'ASC');
    }
}
add_action('pre_get_posts', 'order_wiki_posts_alphabetically');



/***************************** */
/* Frontend -> single-wiki.php */
/***************************** */

/** Filter content in Wiki-Frontend to enable ankerlinks */
function add_id_to_headings_for_wiki($content) {
    // Überprüfe, ob es sich um einen Post des Typs 'wiki' handelt
    if (get_post_type() == 'wiki') {
        $dom = new DOMDocument();
        @$dom->loadHTML(mb_convert_encoding($content, 'HTML-ENTITIES', 'UTF-8'));

        // Suche nach allen <h2> oder <h3> Tags (oder andere Ebenen)
        foreach ($dom->getElementsByTagName('h2') as $heading) {
            // Erstelle eine ID basierend auf dem Text des Headings
            $id = sanitize_title($heading->textContent);
            $heading->setAttribute('id', $id);
        }

        return $dom->saveHTML();
    }

    // Gib den ursprünglichen Content zurück, wenn es kein 'wiki'-Post ist
    return $content;
}
add_filter('the_content', 'add_id_to_headings_for_wiki');


/** Generate Sidebar Ankerlinks */
function generate_sidebar_anchors() {
    if (get_post_type() == 'wiki') {
        global $post;
        $content = $post->post_content;

        // Überschriften sammeln
        preg_match_all('/<h2>(.*?)<\/h2>/', $content, $matches);

        if (!empty($matches[1])) {
            echo '<ol class="anchor-links">';
            foreach ($matches[1] as $heading) {
                $id = sanitize_title($heading);
                echo '<li><a href="#' . esc_attr($id) . '">' . esc_html($heading) . '</a></li>';
            }
            echo '</ol>';
        }
    }
}



/******************************************* */
/* Frontend -> Editing wiki through Frontend */
/******************************************* */
/**
 * AJAX Handler for saving changes in Frontend
 */
function save_wiki_content_ajax_from_frontend() {
    // Sicherheitsüberprüfung
    check_ajax_referer('save_wiki_content_nonce', '_wpnonce');
    
    // Berechtigungsprüfung
    if (!current_user_can('edit_post', $_POST['post_id'])) {
        wp_send_json_error('No permission to edit this post');
    }
    
    // Inhalte speichern
    $post_id = intval($_POST['post_id']);
    $content = wp_kses_post($_POST['content']);  // Sanitize Content
    
    // Aktualisiere den Post-Inhalt
    wp_update_post(array(
        'ID' => $post_id,
        'post_content' => $content,
    ));
    
    wp_send_json_success('Content saved successfully');
}
add_action('wp_ajax_save_wiki_content', 'save_wiki_content_ajax_from_frontend');

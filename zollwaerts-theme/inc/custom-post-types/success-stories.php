<?php
// Custom Post Type 'Erfolgsgeschichten' registrieren
function cpt_erfolgsgeschichten() {
    $labels = array(
        'name'               => _x('Erfolgsgeschichten', 'post type general name'),
        'singular_name'      => _x('Erfolgsgeschichte', 'post type singular name'),
        'menu_name'          => _x('Erfolgsgeschichten', 'admin menu'),
        'name_admin_bar'     => _x('Erfolgsgeschichte', 'add new on admin bar'),
        'add_new'            => _x('Neue Erfolgsgeschichte hinzufügen', 'erfolgsgeschichte'),
        'add_new_item'       => __('Neue Erfolgsgeschichte hinzufügen'),
        'new_item'           => __('Neue Erfolgsgeschichte'),
        'edit_item'          => __('Erfolgsgeschichte bearbeiten'),
        'view_item'          => __('Erfolgsgeschichte ansehen'),
        'all_items'          => __('Alle Erfolgsgeschichten'),
        'search_items'       => __('Erfolgsgeschichten durchsuchen'),
        'parent_item_colon'  => __('Übergeordnete Erfolgsgeschichten:'),
        'not_found'          => __('Keine Erfolgsgeschichten gefunden.'),
        'not_found_in_trash' => __('Keine Erfolgsgeschichten im Papierkorb gefunden.'),
    );
    
    $args = array(
        'labels'             => $labels,
        'public'             => true,
        'publicly_queryable' => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'query_var'          => true,
        'rewrite'            => array('slug' => 'erfolgsgeschichten'),
        'capability_type'    => 'post',
        'has_archive'        => true,
        'hierarchical'       => false,
        'menu_position'      => 5,
        'supports'           => array('title', 'editor', 'thumbnail', 'excerpt', 'revisions'),
    );
    
    register_post_type('erfolgsgeschichten', $args);
}
add_action('init', 'cpt_erfolgsgeschichten');

// Custom Taxonomy 'Kategorien' für 'Erfolgsgeschichten' registrieren
function taxonomy_erfolgsgeschichten_kategorien() {
    $labels = array(
        'name'              => _x('Kategorien', 'taxonomy general name'),
        'singular_name'     => _x('Kategorie', 'taxonomy singular name'),
        'search_items'      => __('Kategorien durchsuchen'),
        'all_items'         => __('Alle Kategorien'),
        'parent_item'       => __('Übergeordnete Kategorie'),
        'parent_item_colon' => __('Übergeordnete Kategorie:'),
        'edit_item'         => __('Kategorie bearbeiten'),
        'update_item'       => __('Kategorie aktualisieren'),
        'add_new_item'      => __('Neue Kategorie hinzufügen'),
        'new_item_name'     => __('Neuer Kategoriename'),
        'menu_name'         => __('Kategorien'),
    );

    $args = array(
        'hierarchical'      => true,
        'labels'            => $labels,
        'show_ui'           => true,
        'show_admin_column' => true,
        'query_var'         => true,
        'rewrite'           => array('slug' => 'erfolgsgeschichten-kategorien'),
        'show_in_rest'      => true, // Dies macht die Taxonomie in der REST API verfügbar
        'rest_base'         => 'erfolgsgeschichten-kategorien', // Hier wird der Endpunkt für die REST API definiert
    );

    register_taxonomy('erfolgsgeschichten_kategorien', array('erfolgsgeschichten'), $args);
}
add_action('init', 'taxonomy_erfolgsgeschichten_kategorien');



// Meta Box für "Kunde" hinzufügen
function erfolgsgeschichten_kunde_meta_box() {
    add_meta_box(
        'kunde_meta_box',                 // ID der Meta Box
        'Kunde',                          // Titel der Meta Box
        'render_kunde_meta_box',          // Callback-Funktion
        'erfolgsgeschichten',             // Post Type
        'normal',                           // Platzierung: Sidebar
        'high'                            // Priorität: hoch 
    );
}
add_action('add_meta_boxes', 'erfolgsgeschichten_kunde_meta_box');

// Inhalt der Meta Box
function render_kunde_meta_box($post) {
    // Abrufen des gespeicherten Wertes
    $kunde = get_post_meta($post->ID, '_kunde_meta_key', true);
    // Sicherheits-Nonce hinzufügen
    wp_nonce_field('save_kunde_meta_box', 'kunde_meta_box_nonce');
    ?>
    <p>
        <label for="kunde_input">Kunde:</label>
        <input type="text" name="kunde_input" id="kunde_input" value="<?php echo esc_attr($kunde); ?>" style="width:100%;" />
    </p>
    <?php
}

// Speichern der Meta Box-Daten
function save_kunde_meta_box($post_id) {
    // Überprüfung der Nonce
    if (!isset($_POST['kunde_meta_box_nonce']) || !wp_verify_nonce($_POST['kunde_meta_box_nonce'], 'save_kunde_meta_box')) {
        return;
    }
    // Prüfen, ob der Benutzer berechtigt ist, diesen Post zu speichern
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }
    // Prüfen, ob der Eingabewert gesetzt ist
    if (isset($_POST['kunde_input'])) {
        $kunde = sanitize_text_field($_POST['kunde_input']);
        update_post_meta($post_id, '_kunde_meta_key', $kunde);
    }
}
add_action('save_post', 'save_kunde_meta_box');

// Sicherstellen, dass die Meta Box standardmäßig aktiviert ist
function default_screen_layout($user) {
    $user_meta_key = "meta-box-order_post";
    $user_meta_value = get_user_meta($user->ID, $user_meta_key, true);

    if (!$user_meta_value || empty($user_meta_value)) {
        // Meta-Box standardmäßig aktivieren
        update_user_meta($user->ID, $user_meta_key, array(
            'side' => 'kunde_meta_box,submitdiv',
        ));
    }
}
add_action('user_register', 'default_screen_layout');

/**
 * Add AJAX Filter for Categories
 * 
 * nutzt das selbe JS, wie der Blog zum Filtern der single CPTs mit AJAX
 */
function enqueue_success_stories_filter_script() {
    // Nur auf Archivseiten und einzelnen Seiten für den CPT 'erfolgsgeschichten' laden
    if (is_post_type_archive('erfolgsgeschichten') || is_singular('erfolgsgeschichten')) {
        wp_enqueue_script('post-filter-script', get_template_directory_uri() . '/js/post-filter-script.js', array('jquery'), null, true);

        // Lokalisierung der AJAX-Daten für Erfolgsgeschichten
        wp_localize_script('post-filter-script', 'ajax_post_params', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'post_type' => 'erfolgsgeschichten',
            'taxonomy' => 'erfolgsgeschichten_kategorien',
        ));
    }
}
add_action('wp_enqueue_scripts', 'enqueue_success_stories_filter_script');


function filter_success_stories_by_category() {
    $category_id = isset($_POST['category_id']) && $_POST['category_id'] !== null ? intval($_POST['category_id']) : null;
    $paged = isset($_POST['paged']) ? intval($_POST['paged']) : "1"; // Aktuelle Seite von AJAX

    // WP_Query-Argumente für Erfolgsgeschichten
    $args = array(
        'post_type' => 'erfolgsgeschichten',
        'posts_per_page' => 10,
        'paged' => $paged, // Paged für Pagination
        'post_status' => 'publish', // Nur veröffentlichte Beiträge
    );

    // Kategorienfilter
    if ($category_id) {
        $args['tax_query'] = array(
            array(
                'taxonomy' => 'erfolgsgeschichten_kategorien',
                'field' => 'term_id',
                'terms' => $category_id,
            ),
        );
    }

    // Query und Ausgabe
    $query = new WP_Query($args);
    ob_start();
    if ($query->have_posts()) :
        echo '<div class="blog erfolgsgeschichten post-list">';
        while ($query->have_posts()) : $query->the_post(); ?>
            <div class="single-post-item" style="view-transition-name: post-<?php the_ID(); ?>">
                <a href="<?php the_permalink(); ?>">
                    <div class="single-post-item-inner">
                        <?php if (has_post_thumbnail()) : ?>
                            <div class="post-thumbnail">
                                <?php the_post_thumbnail('blog-thumb'); ?>
                            </div>
                        <?php endif; ?>

                        <div class="post-meta">
                            <span class="post-date"><?php echo get_the_date('d. F Y'); ?></span>
                        </div>

                        <h2><?php the_title(); ?></h2>
                        <div class="post-excerpt"><?php the_excerpt(); ?></div>

                        <div class="read-more">
                            <p>Weiterlesen</p>
                            <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="12" height="12">
                                <path fill="#11114A" transform="matrix(-1 0 0 1 12 0.0812988)" d="M0.010917343 4.8662171L0.0017641115 4.9527488L0.010285715 5.0004244L0 5.0004597L0.0042217541 5.0822744C0.0051675173 5.0910196 0.0064603202 5.1014748 0.0083995117 5.1171575L0.035038877 5.236444L0.078554988 5.3484674L0.1172509 5.420835L0.16688135 5.4944906C0.1927705 5.5286436 0.22035234 5.5595098 0.25384098 5.5914679L4.5367041 9.7558832C4.8714046 10.081326 5.4141178 10.081379 5.7488871 9.7560034L5.8201995 9.6775103C6.0816836 9.3506546 6.0579653 8.8780003 5.7490106 8.5775919L2.9262857 5.8328538L11.142933 5.8328381C11.616307 5.832799 12 5.4597206 12 4.9995403L11.994232 4.9023609C11.944712 4.4879346 11.582367 4.1662769 11.142784 4.1663132L2.9254286 4.1663284L5.7489448 1.422472C6.083684 1.0970589 6.083684 0.56946623 5.7489486 0.24405697C5.4142137 -0.081352323 4.8715005 -0.081352323 4.5367656 0.24405697L0.2435506 4.4179473L0.1963734 4.4692841L0.15299548 4.5248733L0.11413158 4.5845737L0.081366442 4.6456332C0.074457817 4.659668 0.069259398 4.6720638 0.058862571 4.6968551L0.029049635 4.7849994L0.010917343 4.8662171Z"/>
                            </svg>
                        </div>
                    </div>
                </a>
            </div>
        <?php endwhile;
        echo '</div>';

        // Pagination ausgeben
        echo '<div class="pagination">';
        echo paginate_links(array(
            'total' => $query->max_num_pages,
            'current' => $paged,
            'format' => '?paged=%#%',
            'add_args' => array(
                'action' => 'filter_success_stories_by_category',
            ),
            'prev_text' => __( 'Vorherige', 'textdomain' ),
            'next_text' => __( 'Nächste', 'textdomain' ),
        ));
        echo '</div>';

    else :
        echo '<p>Keine Erfolgsgeschichten gefunden.</p>';
    endif;
    wp_reset_postdata();

    $output = ob_get_clean();
    if (empty($output)) {
        wp_send_json_error('Keine Inhalte gefunden');
    } else {
        echo $output;
    }

    wp_die();
}
add_action('wp_ajax_filter_success_stories_by_category', 'filter_success_stories_by_category');
add_action('wp_ajax_nopriv_filter_success_stories_by_category', 'filter_success_stories_by_category');

<?php
/**
 * Add "Events" CPT 
 */
function create_events_cpt() {

    $labels = array(
        'name' => _x('Events', 'Post Type General Name', 'textdomain'),
        'singular_name' => _x('Event', 'Post Type Singular Name', 'textdomain'),
        'menu_name' => __('Events', 'textdomain'),
        'name_admin_bar' => __('Event', 'textdomain'),
        'archives' => __('Event Archives', 'textdomain'),
        'attributes' => __('Event Attributes', 'textdomain'),
        'parent_item_colon' => __('Parent Event:', 'textdomain'),
        'all_items' => __('All Events', 'textdomain'),
        'add_new_item' => __('Add New Event', 'textdomain'),
        'add_new' => __('Add New', 'textdomain'),
        'new_item' => __('New Event', 'textdomain'),
        'edit_item' => __('Edit Event', 'textdomain'),
        'update_item' => __('Update Event', 'textdomain'),
        'view_item' => __('View Event', 'textdomain'),
        'view_items' => __('View Events', 'textdomain'),
        'search_items' => __('Search Event', 'textdomain'),
        'not_found' => __('Not found', 'textdomain'),
        'not_found_in_trash' => __('Not found in Trash', 'textdomain'),
        'featured_image' => __('Featured Image', 'textdomain'),
        'set_featured_image' => __('Set featured image', 'textdomain'),
        'remove_featured_image' => __('Remove featured image', 'textdomain'),
        'use_featured_image' => __('Use as featured image', 'textdomain'),
        'insert_into_item' => __('Insert into event', 'textdomain'),
        'uploaded_to_this_item' => __('Uploaded to this event', 'textdomain'),
        'items_list' => __('Events list', 'textdomain'),
        'items_list_navigation' => __('Events list navigation', 'textdomain'),
        'filter_items_list' => __('Filter events list', 'textdomain'),
    );

    $args = array(
        'label' => __('Event', 'textdomain'),
        'description' => __('Post Type for Events', 'textdomain'),
        'labels' => $labels,
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt', 'revisions'),
        'taxonomies' => array(), // Keine Standard-Taxonomien
        // 'taxonomies' => array('category', 'post_tag'), // würde Kategorien und Tags von den default Posts/Beiträgen verwenden
        'hierarchical' => false,
        'public' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'menu_position' => 5,
        'menu_icon' => 'dashicons-calendar', // Icon für das Menü im Admin-Bereich
        'show_in_admin_bar' => true,
        'show_in_nav_menus' => true,
        'can_export' => true,
        'has_archive' => true,
        'exclude_from_search' => false,
        'publicly_queryable' => true,
        'capability_type' => 'post',
        'show_in_rest' => true, // Gutenberg Unterstützung
        'rewrite' => array('slug' => 'events', 'with_front' => false), // Hier wird die Permalink-Struktur festgelegt
    );

    register_post_type('events', $args);

}
add_action('init', 'create_events_cpt', 0);


// Event taxonomy "Event Type"
function create_event_type_taxonomy() {

    $labels = array(
        'name' => _x('Event Types', 'taxonomy general name', 'textdomain'),
        'singular_name' => _x('Event Type', 'taxonomy singular name', 'textdomain'),
        'search_items' => __('Search Event Types', 'textdomain'),
        'all_items' => __('All Event Types', 'textdomain'),
        'parent_item' => __('Parent Event Type', 'textdomain'),
        'parent_item_colon' => __('Parent Event Type:', 'textdomain'),
        'edit_item' => __('Edit Event Type', 'textdomain'),
        'update_item' => __('Update Event Type', 'textdomain'),
        'add_new_item' => __('Add New Event Type', 'textdomain'),
        'new_item_name' => __('New Event Type Name', 'textdomain'),
        'menu_name' => __('Event Types', 'textdomain'),
    );

    $args = array(
        'labels' => $labels,
        'hierarchical' => true, // Setze auf `false`, wenn du keine hierarchische Struktur möchtest
        'public' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true,
        'show_in_quick_edit' => true,
        'show_admin_column' => true,
        'show_in_rest' => true, // Für Gutenberg- und REST-API-Unterstützung
    );

    register_taxonomy('event_type', array('events'), $args);

}
add_action('init', 'create_event_type_taxonomy', 0);

// Event taxonomy "Event Location"
function create_event_location_taxonomy() {

    $labels = array(
        'name' => _x('Event Locations', 'taxonomy general name', 'textdomain'),
        'singular_name' => _x('Event Location', 'taxonomy singular name', 'textdomain'),
        'search_items' => __('Search Event Locations', 'textdomain'),
        'all_items' => __('All Event Locations', 'textdomain'),
        'parent_item' => __('Parent Event Location', 'textdomain'),
        'parent_item_colon' => __('Parent Event Location:', 'textdomain'),
        'edit_item' => __('Edit Event Location', 'textdomain'),
        'update_item' => __('Update Event Location', 'textdomain'),
        'add_new_item' => __('Add New Event Location', 'textdomain'),
        'new_item_name' => __('New Event Location Name', 'textdomain'),
        'menu_name' => __('Event Locations', 'textdomain'),
    );

    $args = array(
        'labels' => $labels,
        'hierarchical' => true, // `true` macht die Taxonomie hierarchisch (wie Kategorien), `false` nicht-hierarchisch (wie Tags)
        'public' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true,
        'show_in_quick_edit' => true,
        'show_admin_column' => true,
        'show_in_rest' => true, // Für Gutenberg- und REST-API-Unterstützung
    );

    register_taxonomy('event_location', array('events'), $args);

}
add_action('init', 'create_event_location_taxonomy', 0);


// Event taxonomy "Target Audience"
function create_target_audience_taxonomy() {

    $labels = array(
        'name' => _x('Target Audiences', 'taxonomy general name', 'textdomain'),
        'singular_name' => _x('Target Audience', 'taxonomy singular name', 'textdomain'),
        'search_items' => __('Search Target Audiences', 'textdomain'),
        'all_items' => __('All Target Audiences', 'textdomain'),
        'parent_item' => __('Parent Target Audience', 'textdomain'),
        'parent_item_colon' => __('Parent Target Audience:', 'textdomain'),
        'edit_item' => __('Edit Target Audience', 'textdomain'),
        'update_item' => __('Update Target Audience', 'textdomain'),
        'add_new_item' => __('Add New Target Audience', 'textdomain'),
        'new_item_name' => __('New Target Audience Name', 'textdomain'),
        'menu_name' => __('Target Audience', 'textdomain'),
    );

    $args = array(
        'labels' => $labels,
        'hierarchical' => true, // `true` macht die Taxonomie hierarchisch (wie Kategorien), `false` nicht-hierarchisch (wie Tags)
        'public' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true,
        'show_in_quick_edit' => true,
        'show_admin_column' => true,
        'show_in_rest' => true,
    );

    register_taxonomy('target_audience', array('events'), $args);

}
add_action('init', 'create_target_audience_taxonomy', 0);



/** Event Meta Box for "Event Subtitle" */
// Meta Box für den Subtitle hinzufügen
function add_event_subtitle_meta_box() {
    add_meta_box(
        'event_subtitle_meta_box', // ID der Meta Box
        __('Event Subtitle', 'textdomain'), // Titel der Meta Box
        'render_event_subtitle_meta_box', // Callback, um die Inhalte der Meta Box anzuzeigen
        'events', // Post-Typ
        'side', // Position
        'default' // Priorität
    );
}
add_action('add_meta_boxes', 'add_event_subtitle_meta_box');

// Inhalt der Meta Box "Subtitle" anzeigen
function render_event_subtitle_meta_box($post) {
    // Sicherheitstoken hinzufügen
    wp_nonce_field('save_event_subtitle_meta_box_data', 'event_subtitle_meta_box_nonce');

    // Aktuellen Wert des Subtitles abrufen
    $value = get_post_meta($post->ID, '_event_subtitle', true);

    // Textfeld für den Subtitle anzeigen
    echo '<label for="event_subtitle">' . __('Subtitle', 'textdomain') . '</label>';
    echo '<input type="text" id="event_subtitle" name="event_subtitle" value="' . esc_attr($value) . '" style="width:100%;" />';
}

// Speichern der Meta Box-Daten
function save_event_subtitle_meta_box_data($post_id) {
    // Sicherheitsüberprüfung
    if (!isset($_POST['event_subtitle_meta_box_nonce']) || !wp_verify_nonce($_POST['event_subtitle_meta_box_nonce'], 'save_event_subtitle_meta_box_data')) {
        return;
    }

    // Überprüfung der Berechtigungen
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Speichern des Subtitles
    if (isset($_POST['event_subtitle'])) {
        $subtitle = sanitize_text_field($_POST['event_subtitle']);
        update_post_meta($post_id, '_event_subtitle', $subtitle);
    }
}
add_action('save_post', 'save_event_subtitle_meta_box_data');




// Event Meta Box for "global Event"/"All Tracks"
// add global_event_meta_box
function add_global_event_meta_box() {
    add_meta_box(
        'global_event_meta_box', // ID der Meta Box
        __('Global Event', 'textdomain'), // Titel der Meta Box
        'render_global_event_meta_box', // Callback, um die Inhalte der Meta Box anzuzeigen
        'events', // Post-Typ
        'side', // Position
        'default' // Priorität
    );
}
add_action('add_meta_boxes', 'add_global_event_meta_box');

// render global_event_meta_box
function render_global_event_meta_box($post) {
    // Sicherheitstoken hinzufügen
    wp_nonce_field('save_global_event_meta_box_data', 'global_event_meta_box_nonce');

    // Aktuellen Wert der Checkbox abrufen
    $value = get_post_meta($post->ID, '_global_event', true);

    // Checkbox anzeigen
    echo '<label for="global_event">';
    echo '<input type="checkbox" id="global_event" name="global_event" value="1"' . checked(1, $value, false) . ' />';
    echo __('This event belongs to all tracks', 'textdomain');
    echo '</label>';
}

// save global_event_meta_box
function save_global_event_meta_box_data($post_id) {
    // Sicherheitsüberprüfung
    if (!isset($_POST['global_event_meta_box_nonce']) || !wp_verify_nonce($_POST['global_event_meta_box_nonce'], 'save_global_event_meta_box_data')) {
        return;
    }

    // Überprüfung der Berechtigungen
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Speichern der Checkbox-Daten
    $is_global_event = isset($_POST['global_event']) ? 1 : 0;
    update_post_meta($post_id, '_global_event', $is_global_event);
}
add_action('save_post', 'save_global_event_meta_box_data');




// Meta Box für "Kein Link zur Detailseite" hinzufügen
function add_no_link_to_detail_meta_box() {
    add_meta_box(
        'no_link_to_detail_meta_box', // ID der Meta Box
        __('Kein Link zur Detailseite', 'textdomain'), // Titel der Meta Box
        'render_no_link_to_detail_meta_box', // Callback, um die Inhalte der Meta Box anzuzeigen
        'events', // Post-Typ
        'side', // Position
        'default' // Priorität
    );
}
add_action('add_meta_boxes', 'add_no_link_to_detail_meta_box');


// Inhalt der Meta Box "Kein Link zur Detailseite" anzeigen
function render_no_link_to_detail_meta_box($post) {
    // Sicherheitstoken hinzufügen
    wp_nonce_field('save_no_link_to_detail_meta_box_data', 'no_link_to_detail_meta_box_nonce');

    // Aktuellen Wert der Checkbox abrufen
    $value = get_post_meta($post->ID, '_no_link_to_detail', true);

    // Checkbox anzeigen
    echo '<label for="no_link_to_detail">';
    echo '<input type="checkbox" id="no_link_to_detail" name="no_link_to_detail" value="1"' . checked(1, $value, false) . ' />';
    echo __('Kein Link zur Detailseite', 'textdomain');
    echo '</label>';
}

// Speichern der Meta Box-Daten "Kein Link zur Detailseite"
function save_no_link_to_detail_meta_box_data($post_id) {
    // Sicherheitsüberprüfung
    if (!isset($_POST['no_link_to_detail_meta_box_nonce']) || !wp_verify_nonce($_POST['no_link_to_detail_meta_box_nonce'], 'save_no_link_to_detail_meta_box_data')) {
        return;
    }

    // Überprüfung der Berechtigungen
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Speichern der Checkbox-Daten
    $no_link_to_detail = isset($_POST['no_link_to_detail']) ? 1 : 0;
    update_post_meta($post_id, '_no_link_to_detail', $no_link_to_detail);
}
add_action('save_post', 'save_no_link_to_detail_meta_box_data');


/*** Meta box "Featured/Highlight" ***/
// Meta Box für "Featured Event" hinzufügen
function add_featured_event_meta_box() {
    add_meta_box(
        'add_featured_event_meta_box', // ID der Meta Box
        __('Highlight Event', 'textdomain'), // Titel der Meta Box
        'render_add_featured_event_meta_box', // Callback, um die Inhalte der Meta Box anzuzeigen
        'events', // Post-Typ
        'side', // Position
        'default' // Priorität
    );
}
add_action('add_meta_boxes', 'add_featured_event_meta_box');

// Inhalt der Meta Box "Featured Event" anzeigen
function render_add_featured_event_meta_box($post) {
    // Sicherheitstoken hinzufügen
    wp_nonce_field('save_featured_event_to_meta_box_data', 'add_featured_event_meta_box_nonce');

    // Aktuellen Wert der Checkbox abrufen
    $value = get_post_meta($post->ID, '_is_featured_event', true);

    // Checkbox anzeigen
    echo '<label for="is_featured_event">';
    echo '<input type="checkbox" id="is_featured_event" name="is_featured_event" value="1"' . checked(1, $value, false) . ' />';
    echo __('Dieses Event hervorheben', 'textdomain');
    echo '</label>';
}

// Speichern der Meta Box-Daten für "Featured Event"
function save_featured_event_to_meta_box_data($post_id) {
    // Sicherheitsüberprüfung
    if (!isset($_POST['add_featured_event_meta_box_nonce']) || !wp_verify_nonce($_POST['add_featured_event_meta_box_nonce'], 'save_featured_event_to_meta_box_data')) {
        return;
    }

    // Überprüfung der Berechtigungen
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Speichern der Checkbox-Daten
    $is_featured_event = isset($_POST['is_featured_event']) ? 1 : 0;
    update_post_meta($post_id, '_is_featured_event', $is_featured_event);
}
add_action('save_post', 'save_featured_event_to_meta_box_data');



/*** Create custom Meta box for "Timing" ***/
// Meta Box hinzufügen
function add_event_time_meta_box() {
    add_meta_box(
        'event_time_meta_box', // ID der Meta Box
        __('Event Timing', 'textdomain'), // Titel der Meta Box
        'render_event_time_meta_box', // Callback, um die Inhalte der Meta Box anzuzeigen
        'events', // Post-Typ
        'side', // Position
        'high' // Priorität
    );
}
add_action('add_meta_boxes', 'add_event_time_meta_box');

// Inhalt der Meta Box "Timing" anzeigen
function render_event_time_meta_box($post) {
    // Sicherheitstoken hinzufügen
    wp_nonce_field('save_event_time_meta_box_data', 'event_time_meta_box_nonce');

    // Aktuelle Werte abrufen
    $start_time = get_post_meta($post->ID, '_event_start_time', true);
    $end_time = get_post_meta($post->ID, '_event_end_time', true);
    $event_date = get_post_meta($post->ID, '_event_date', true);

    // HTML für die Meta Box
    echo '<label for="event_date">' . __('Event Date:', 'textdomain') . '</label>';
    echo '<input type="date" id="event_date" name="event_date" value="' . esc_attr($event_date) . '" size="25" />';

    echo '<br><br>';

    echo '<label for="event_start_time">' . __('Start Time:', 'textdomain') . '</label>';
    echo '<input type="time" id="event_start_time" name="event_start_time" value="' . esc_attr($start_time) . '" size="25" />';

    echo '<br><br>';

    echo '<label for="event_end_time">' . __('End Time:', 'textdomain') . '</label>';
    echo '<input type="time" id="event_end_time" name="event_end_time" value="' . esc_attr($end_time) . '" size="25" />';
}

// Speichern der Meta Box-Daten Timing
function save_event_time_meta_box_data($post_id) {
    // Sicherheitsüberprüfung
    if (!isset($_POST['event_time_meta_box_nonce']) || !wp_verify_nonce($_POST['event_time_meta_box_nonce'], 'save_event_time_meta_box_data')) {
        return;
    }

    // Überprüfung der Berechtigungen
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Speichern des Datums
    if (isset($_POST['event_date'])) {
        update_post_meta($post_id, '_event_date', sanitize_text_field($_POST['event_date']));
    }

    // Speichern der Startzeit
    if (isset($_POST['event_start_time'])) {
        update_post_meta($post_id, '_event_start_time', sanitize_text_field($_POST['event_start_time']));
    }

    // Speichern der Endzeit
    if (isset($_POST['event_end_time'])) {
        update_post_meta($post_id, '_event_end_time', sanitize_text_field($_POST['event_end_time']));
    }
}
add_action('save_post', 'save_event_time_meta_box_data');



/*** AJAX-Handler für die Validierung der Event-Zeiten ***/
function load_event_validation_script($hook) {
    // Stelle sicher, dass das Script nur auf den relevanten Seiten geladen wird
    if ($hook != 'post.php' && $hook != 'post-new.php') {
        return;
    }

    // Überprüfe, ob es sich um den Custom Post Type "events" handelt
    global $post;
    if ($post->post_type != 'events') {
        return;
    }

    // Lade das JavaScript
    wp_enqueue_script('event-validation-script', get_template_directory_uri() . '/js/event-validation.js', array('jquery'), null, true);

    // Sende die Nonce an das JavaScript
    wp_localize_script('event-validation-script', 'ajax_object', array(
        'security' => wp_create_nonce('validate_event_time_overlap')
    ));
}
add_action('admin_enqueue_scripts', 'load_event_validation_script');


// AJAX-Handler für die Validierung der Event-Zeiten
function validate_event_time_overlap_ajax() {
    // Sicherheitsüberprüfung
    check_ajax_referer('validate_event_time_overlap', 'security');

    // Post-Daten aus dem AJAX-Request
    $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;
    $location_id = isset($_POST['location_id']) ? intval($_POST['location_id']) : 0;
    $start_time = isset($_POST['start_time']) ? sanitize_text_field($_POST['start_time']) : '';
    $end_time = isset($_POST['end_time']) ? sanitize_text_field($_POST['end_time']) : '';
    $event_date = isset($_POST['event_date']) ? sanitize_text_field($_POST['event_date']) : '';


    // Überprüfe auf Überschneidungen
    $args = array(
        'post_type' => 'events',
        'post_status' => 'publish',
        'posts_per_page' => -1,
        'post__not_in' => array($post_id), // Schließt den aktuellen Post aus
        'tax_query' => array(
            array(
                'taxonomy' => 'event_location',
                'field' => 'term_id',
                'terms' => $location_id,
            ),
        ),
        'meta_query' => array(
            'relation' => 'AND',
            array(
                'key' => '_event_date',
                'value' => $event_date,
                'compare' => '=', // Überprüft, ob das Datum übereinstimmt
                'type' => 'DATE'
            ),
            array(
                'key' => '_event_start_time',
                'value' => $end_time,
                'compare' => '<',
                'type' => 'TIME'
            ),
            array(
                'key' => '_event_end_time',
                'value' => $start_time,
                'compare' => '>',
                'type' => 'TIME'
            ),
        ),
    );

    $overlapping_events = new WP_Query($args);

    if ($overlapping_events->have_posts()) {
        // Fehlernachricht zurückgeben
        wp_send_json_error(__('Error: There is a time conflict with another event in the same location.'));
    } else {
        wp_send_json_success();
    }

    wp_die(); // Beendet AJAX-Request
}
add_action('wp_ajax_validate_event_time_overlap', 'validate_event_time_overlap_ajax');


// Felder zu Quick Edit hinzufügen
function add_custom_quick_edit_fields($column_name, $post_type) {
    if ($post_type == 'events') {
        switch ($column_name) {
            case 'global_event':
                ?>
                <fieldset class="inline-edit-col-right">
                    <div class="inline-edit-col">
                        <label class="alignleft">
                            <span class="title"><?php _e('Global Event', 'textdomain'); ?></span>
                            <span class="input-text-wrap">
                                <input type="checkbox" name="global_event" value="1">
                            </span>
                        </label>
                    </div>
                </fieldset>
                <?php
                break;
            case 'no_link_to_detail':
                ?>
                <fieldset class="inline-edit-col-right">
                    <div class="inline-edit-col">
                        <label class="alignleft">
                            <span class="title"><?php _e('Kein Link zur Detailseite', 'textdomain'); ?></span>
                            <span class="input-text-wrap">
                                <input type="checkbox" name="no_link_to_detail" value="1">
                            </span>
                        </label>
                    </div>
                </fieldset>
                <?php
                break;
        }
    }
}
add_action('quick_edit_custom_box', 'add_custom_quick_edit_fields', 10, 2);



// Werte für Quick Edit bereitstellen
function enqueue_custom_quick_edit_script() {
    wp_enqueue_script('event-quick-edit', get_template_directory_uri() . '/js/event-quick-edit.js', array('jquery'), '', true);
}
add_action('admin_enqueue_scripts', 'enqueue_custom_quick_edit_script');


// Quick Edit-Daten speichern
function save_custom_quick_edit_data($post_id) {
    if (isset($_POST['global_event'])) {
        update_post_meta($post_id, '_global_event', 1);
    } else {
        update_post_meta($post_id, '_global_event', 0);
    }

    if (isset($_POST['no_link_to_detail'])) {
        update_post_meta($post_id, '_no_link_to_detail', 1);
    } else {
        update_post_meta($post_id, '_no_link_to_detail', 0);
    }
}
add_action('save_post', 'save_custom_quick_edit_data');


// Spalten hinzufügen
function add_custom_columns($columns) {
    $columns['global_event'] = __('Global Event', 'textdomain');
    $columns['no_link_to_detail'] = __('Kein Link zur Detailseite', 'textdomain');
    return $columns;
}
add_filter('manage_events_posts_columns', 'add_custom_columns');


// Inhalte der Spalten füllen und IDs hinzufügen
function custom_columns_content($column, $post_id) {
    switch ($column) {
        case 'global_event':
            $value = get_post_meta($post_id, '_global_event', true);
            echo '<span id="global_event-' . $post_id . '">' . ($value ? __('Yes', 'textdomain') : __('No', 'textdomain')) . '</span>';
            break;
        case 'no_link_to_detail':
            $value = get_post_meta($post_id, '_no_link_to_detail', true);
            echo '<span id="no_link_to_detail-' . $post_id . '">' . ($value ? __('Yes', 'textdomain') : __('No', 'textdomain')) . '</span>';
            break;
    }
}
add_action('manage_events_posts_custom_column', 'custom_columns_content', 10, 2);



// Filter and Sort Events for archive 
// Pefilter and sort events
function sort_events_by_date_and_time($query) {
    if (!is_admin() && $query->is_main_query() && is_post_type_archive('events')) {
        // Sortiere nach dem Event-Datum (_event_date) und Event-Startzeit (_event_start_time)
        $query->set('meta_key', '_event_date'); // Primär nach _event_date sortieren
        $query->set('orderby', array(
            'meta_value' => 'ASC',  // Sortiere die Events aufsteigend nach Datum
            'meta_value_num' => 'ASC', // Sortiere die Events aufsteigend nach Zeit
        ));
        $query->set('meta_type', 'DATE');  // Stelle sicher, dass das Meta-Feld als Datum behandelt wird

        // Optional: Nur zukünftige Events anzeigen
        $today = date('Ymd');  // Das heutige Datum im Format JJJJMMTT
        $query->set('meta_query', array(
            array(
                'key' => '_event_date',
                'value' => $today,
                'compare' => '>=',  // Nur Events ab heute anzeigen
                'type' => 'DATE'
            )
        ));
    }
}
add_action('pre_get_posts', 'sort_events_by_date_and_time');






/*** Filter Script for Events ***/
function enqueue_event_filter_script() {
    wp_enqueue_script('event-filter-script', get_template_directory_uri() . '/js/event-filter-script.js', array('jquery'), null, true);

    // Lokalisierung der AJAX-Daten für Events
    wp_localize_script('event-filter-script', 'ajax_event_params', array(
        'ajaxurl' => admin_url('admin-ajax.php'),
        'post_type' => 'events',
        'taxonomy' => 'event_type',
    ));
}
add_action('wp_enqueue_scripts', 'enqueue_event_filter_script');

function filter_events_by_category() {
    // Holen der übermittelten Kategorie-ID
    $category_id = isset($_POST['category_id']) && $_POST['category_id'] !== null ? intval($_POST['category_id']) : null;

    $featured_event_id = null;
    $featured_event_html = '';

    // Featured Event abfragen
    $featured_event_args = array(
        'post_type' => 'events',
        'posts_per_page' => 1,
        'meta_key' => '_event_date',
        'orderby' => array(
            'meta_value' => 'ASC',
            'meta_value_num' => 'ASC'
        ),
        'meta_type' => 'DATE',
        'meta_query' => array(
            array(
                'key' => '_event_date',
                'value' => date('Ymd'),
                'compare' => '>=',
                'type' => 'DATE'
            ),
            array(
                'key' => '_is_featured_event',
                'value' => 1,
                'compare' => '='
            )
        )
    );

    // Füge Kategorienfilter für das Featured Event hinzu
    if ($category_id) {
        $featured_event_args['tax_query'] = array(
            array(
                'taxonomy' => 'event_type',
                'field' => 'term_id',
                'terms' => $category_id,
            )
        );
    }

    $featured_event_query = new WP_Query($featured_event_args);

    // Wenn wir kein Featured Event finden, suchen wir ein Fallback
    if ($featured_event_query->have_posts()) {
        $featured_event_query->the_post();
        $featured_event_id = get_the_ID();
        
        // Puffer öffnen, um das HTML abzufangen
        ob_start();
        get_template_part('template-parts/event-archive/featured-event'); // Ausgabe des HTML für das Featured Event
        $featured_event_html = ob_get_clean(); // HTML in eine Variable speichern
        wp_reset_postdata();
    } else {
        // Fallback-Logik
        $fallback_event_args = array(
            'post_type' => 'events',
            'posts_per_page' => 1,
            'meta_key' => '_event_date',
            'orderby' => array(
                'meta_value' => 'ASC',
                'meta_value_num' => 'ASC'
            ),
            'meta_type' => 'DATE',
            'meta_query' => array(
                array(
                    'key' => '_event_date',
                    'value' => date('Ymd'),
                    'compare' => '>=',
                    'type' => 'DATE'
                )
            )
        );

        if ($category_id) {
            $fallback_event_args['tax_query'] = array(
                array(
                    'taxonomy' => 'event_type',
                    'field' => 'term_id',
                    'terms' => $category_id,
                )
            );
        }

        $fallback_event_query = new WP_Query($fallback_event_args);

        if ($fallback_event_query->have_posts()) {
            $fallback_event_query->the_post();
            $featured_event_id = get_the_ID();
            
            // Puffer öffnen, um das HTML abzufangen
            ob_start();
            get_template_part('template-parts/event-archive/featured-event');
            $featured_event_html = ob_get_clean();
            wp_reset_postdata();
        }
    }

    // Restliche Events abrufen, ohne das Featured Event
    $args = array(
        'post_type' => 'events',
        'posts_per_page' => 100,
        'meta_key' => '_event_date',
        'orderby' => array(
            'meta_value' => 'ASC',
            'meta_value_num' => 'ASC'
        ),
        'meta_type' => 'DATE',
        'meta_query' => array(
            array(
                'key' => '_event_date',
                'value' => date('Ymd'),
                'compare' => '>=',
                'type' => 'DATE'
            )
        ),
        'post__not_in' => $featured_event_id ? array($featured_event_id) : array()
    );

    if ($category_id) {
        $args['tax_query'] = array(
            array(
                'taxonomy' => 'event_type',
                'field' => 'term_id',
                'terms' => $category_id,
            )
        );
    }

    $query = new WP_Query($args);

    ob_start(); // Puffer öffnen, um HTML der restlichen Events abzufangen

    if ($query->have_posts()) {
        while ($query->have_posts()) {
            $query->the_post();
            get_template_part('template-parts/event-archive/event-item'); // Ausgabe der Event-Items
        }
    } else {
        echo '<p>Keine Veranstaltungen gefunden.</p>';
    }

    $events_html = ob_get_clean(); // HTML der restlichen Events in eine Variable speichern

    // Rückgabe des Featured Events und der restlichen Events als JSON
    wp_send_json_success(array(
        'featured_event' => $featured_event_html,
        'events' => $events_html
    ));

    wp_reset_postdata();
    wp_die();
}
add_action('wp_ajax_filter_events_by_category', 'filter_events_by_category');
add_action('wp_ajax_nopriv_filter_events_by_category', 'filter_events_by_category');

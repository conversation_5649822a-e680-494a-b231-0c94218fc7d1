<?php
/**
 * Add "Ressources" CPT (includes e-books, reports etc.)
 */
function create_ressources_archiv_cpt() {

    $labels = array(
        'name' => _x('Ressources', 'Post Type General Name', 'textdomain'),
        'singular_name' => _x('Ressources', 'Post Type Singular Name', 'textdomain'),
        'menu_name' => __('Ressources', 'textdomain'),
        'name_admin_bar' => __('Ressources', 'textdomain'),
        'archives' => __('Ressourceses', 'textdomain'),
        'attributes' => __('Ressources Attributes', 'textdomain'),
        'parent_item_colon' => __('Parent Ressources:', 'textdomain'),
        'all_items' => __('Alle Ressources', 'textdomain'),
        'add_new_item' => __('Add New Ressources', 'textdomain'),
        'add_new' => __('Add New', 'textdomain'),
        'new_item' => __('New Ressources', 'textdomain'),
        'edit_item' => __('Edit Ressources', 'textdomain'),
        'update_item' => __('Update Ressources', 'textdomain'),
        'view_item' => __('View Ressources', 'textdomain'),
        'view_items' => __('View Ressourcess', 'textdomain'),
        'search_items' => __('Search Ressources', 'textdomain'),
        'not_found' => __('Not found', 'textdomain'),
        'not_found_in_trash' => __('Not found in Trash', 'textdomain'),
        'featured_image' => __('Featured Image', 'textdomain'),
        'set_featured_image' => __('Set featured image', 'textdomain'),
        'remove_featured_image' => __('Remove featured image', 'textdomain'),
        'use_featured_image' => __('Use as featured image', 'textdomain'),
        'insert_into_item' => __('Insert into Ressources', 'textdomain'),
        'uploaded_to_this_item' => __('Uploaded to this Ressources', 'textdomain'),
        'items_list' => __('Ressourcess list', 'textdomain'),
        'items_list_navigation' => __('Ressourcess list navigation', 'textdomain'),
        'filter_items_list' => __('Filter Ressourcess list', 'textdomain'),
    );

    $args = array(
        'label' => __('Ressources', 'textdomain'),
        'description' => __('Post Type for Ressources', 'textdomain'),
        'labels' => $labels,
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt', 'revisions'),
        'taxonomies' => array(), // Keine Standard-Taxonomien
        'hierarchical' => false,
        'public' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'menu_position' => 5,
        'menu_icon' => 'dashicons-media-document', // Icon für das Menü im Admin-Bereich
        'show_in_admin_bar' => true,
        'show_in_nav_menus' => true,
        'can_export' => true,
        'has_archive' => true,
        'exclude_from_search' => false,
        'publicly_queryable' => true,
        'capability_type' => 'post',
        'show_in_rest' => true, // Gutenberg Unterstützung
        'rewrite' => array('slug' => 'ressources-archiv', 'with_front' => false), // Permalink-Struktur
    );

    register_post_type('ressources_archiv', $args);
}
add_action('init', 'create_ressources_archiv_cpt', 0);


/**
 * Register custom taxonomy for the "Ressources" CPT
 */
function create_ressources_category_taxonomy() {
    $labels = array(
        'name' => _x('Kategorien', 'taxonomy general name', 'textdomain'),
        'singular_name' => _x('Kategorie', 'taxonomy singular name', 'textdomain'),
        'search_items' => __('Search Kategorien', 'textdomain'),
        'all_items' => __('All Kategorien', 'textdomain'),
        'parent_item' => __('Parent Kategorie', 'textdomain'),
        'parent_item_colon' => __('Parent Kategorie:', 'textdomain'),
        'edit_item' => __('Edit Kategorie', 'textdomain'),
        'update_item' => __('Update Kategorie', 'textdomain'),
        'add_new_item' => __('Add New Kategorie', 'textdomain'),
        'new_item_name' => __('New Kategorie Name', 'textdomain'),
        'menu_name' => __('Kategorien', 'textdomain'),
    );

    $args = array(
        'hierarchical' => true,
        'labels' => $labels,
        'show_ui' => true,
        'show_admin_column' => true,
        'query_var' => true,
        'rewrite' => array('slug' => 'ressources-kategorie'),
        'show_in_rest' => true, // Gutenberg Unterstützung
    );

    register_taxonomy('ressources_kategorie', array('ressources_archiv'), $args);
}
add_action('init', 'create_ressources_category_taxonomy', 0);


/* Meta Box für "Highlight" Checkbox in der Sidebar */
function ressources_add_highlight_meta_box() {
    add_meta_box(
        'ressources_highlight_meta_box',        // Unique ID
        'Highlight',                            // Box title
        'ressources_highlight_meta_box_html',   // Content callback, must be of type callable
        'ressources_archiv',                    // Post type (dein CPT)
        'side',                                 // Context (zeigt es in der Sidebar an)
        'high'                                  // Priority (hoch, damit es oben in der Sidebar erscheint)
    );
}
add_action('add_meta_boxes', 'ressources_add_highlight_meta_box');

/* HTML für die Meta Box */
function ressources_highlight_meta_box_html($post) {
    $value = get_post_meta($post->ID, '_ressources_highlight', true);
    ?>
    <label for="ressources_highlight_checkbox">
        <input type="checkbox" name="ressources_highlight_checkbox" id="ressources_highlight_checkbox" value="1" <?php checked($value, '1'); ?>>
        Als Highlight markieren
    </label>
    <?php
}

/* Speichert den Wert der Checkbox */
function ressources_save_highlight_meta_box_data($post_id) {
    if (array_key_exists('ressources_highlight_checkbox', $_POST)) {
        update_post_meta($post_id, '_ressources_highlight', '1');
    } else {
        delete_post_meta($post_id, '_ressources_highlight');
    }
}
add_action('save_post', 'ressources_save_highlight_meta_box_data');



/* Meta Box für den Intro-Text mit TinyMCE */
function ressources_add_intro_meta_box() {
    add_meta_box(
        'ressources_intro_meta_box',        // Unique ID
        'Intro Text',                       // Box title
        'ressources_intro_meta_box_html',   // Content callback
        'ressources_archiv',                // Post type
        'normal',                           // Context (normal für breite Box)
        'high'                              // Priority (hoch, damit es möglichst weit oben erscheint)
    );
}
add_action('add_meta_boxes', 'ressources_add_intro_meta_box');

/* HTML für die Meta Box mit TinyMCE */
function ressources_intro_meta_box_html($post) {
    $value = get_post_meta($post->ID, '_ressources_intro_text', true);
    // TinyMCE Editor
    wp_editor(
        $value,                           // Der gespeicherte Inhalt
        'ressources_intro_text',          // ID des Editors
        array(
            'textarea_name' => 'ressources_intro_text',  // Name des Textarea-Felds
            'media_buttons' => false,                    // Medien-Upload-Button deaktivieren
            'textarea_rows' => 10,                       // Anzahl der sichtbaren Zeilen
            'teeny'        => false,                      // Kleinerer TinyMCE-Editor
            'quicktags'    => true                       // Quicktags ermöglichen (HTML)
        )
    );
}

/* Speichern der Meta Box-Daten */
function ressources_save_intro_meta_box_data($post_id) {
    if (array_key_exists('ressources_intro_text', $_POST)) {
        // Speichern des Wertes aus dem Editor, wobei die Ausgabe bereinigt wird
        update_post_meta(
            $post_id,
            '_ressources_intro_text',
            wp_kses_post($_POST['ressources_intro_text'])
        );
    }
}
add_action('save_post', 'ressources_save_intro_meta_box_data');

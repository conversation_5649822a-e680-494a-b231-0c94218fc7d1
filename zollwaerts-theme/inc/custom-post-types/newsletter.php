<?php
/**
 * Add "Newsletter Archiv" CPT
 */
function create_newsletter_archiv_cpt() {

    $labels = array(
        'name' => _x('Newsletter Archiv', 'Post Type General Name', 'textdomain'),
        'singular_name' => _x('Newsletter', 'Post Type Singular Name', 'textdomain'),
        'menu_name' => __('Newsletter Archiv', 'textdomain'),
        'name_admin_bar' => __('Newsletter', 'textdomain'),
        'archives' => __('Newsletter Archives', 'textdomain'),
        'attributes' => __('Newsletter Attributes', 'textdomain'),
        'parent_item_colon' => __('Parent Newsletter:', 'textdomain'),
        'all_items' => __('All Newsletters', 'textdomain'),
        'add_new_item' => __('Add New Newsletter', 'textdomain'),
        'add_new' => __('Add New', 'textdomain'),
        'new_item' => __('New Newsletter', 'textdomain'),
        'edit_item' => __('Edit Newsletter', 'textdomain'),
        'update_item' => __('Update Newsletter', 'textdomain'),
        'view_item' => __('View Newsletter', 'textdomain'),
        'view_items' => __('View Newsletters', 'textdomain'),
        'search_items' => __('Search Newsletter', 'textdomain'),
        'not_found' => __('Not found', 'textdomain'),
        'not_found_in_trash' => __('Not found in Trash', 'textdomain'),
        'featured_image' => __('Featured Image', 'textdomain'),
        'set_featured_image' => __('Set featured image', 'textdomain'),
        'remove_featured_image' => __('Remove featured image', 'textdomain'),
        'use_featured_image' => __('Use as featured image', 'textdomain'),
        'insert_into_item' => __('Insert into Newsletter', 'textdomain'),
        'uploaded_to_this_item' => __('Uploaded to this Newsletter', 'textdomain'),
        'items_list' => __('Newsletters list', 'textdomain'),
        'items_list_navigation' => __('Newsletters list navigation', 'textdomain'),
        'filter_items_list' => __('Filter Newsletters list', 'textdomain'),
    );

    $args = array(
        'label' => __('Newsletter', 'textdomain'),
        'description' => __('Post Type for Newsletter Archiv', 'textdomain'),
        'labels' => $labels,
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt', 'revisions'),
        'taxonomies' => array(), // Keine Standard-Taxonomien
        'hierarchical' => false,
        'public' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'menu_position' => 5,
        'menu_icon' => 'dashicons-email', // Icon für das Menü im Admin-Bereich
        'show_in_admin_bar' => true,
        'show_in_nav_menus' => true,
        'can_export' => true,
        'has_archive' => true,
        'exclude_from_search' => false,
        'publicly_queryable' => true,
        'capability_type' => 'post',
        'show_in_rest' => true, // Gutenberg Unterstützung
        'rewrite' => array('slug' => 'newsletter-archiv', 'with_front' => false), // Permalink-Struktur
    );

    register_post_type('newsletter_archiv', $args);

}
add_action('init', 'create_newsletter_archiv_cpt', 0);





/*** FILTER SCRIPT FOR Newsletter ***/
function enqueue_newsletter_filter_script() {
    if (is_post_type_archive('newsletter_archiv') || is_singular('newsletter_archiv')) {
        wp_enqueue_script('newsletter-filter-script', get_template_directory_uri() . '/js/newsletter-filter-script.js', array('jquery'), null, true);

        // Lokalisierung der AJAX-Daten für Posts
        wp_localize_script('newsletter-filter-script', 'ajax_newsletter_params', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'post_type' => 'newsletter_archiv',
            'taxonomy' => 'category',
        ));
    }
}
add_action('wp_enqueue_scripts', 'enqueue_newsletter_filter_script');

function filter_newsletter_by_category() {
    $category_id = isset($_POST['category_id']) && $_POST['category_id'] !== null ? intval($_POST['category_id']) : null;
    $paged = isset($_POST['paged']) ? intval($_POST['paged']) : 1; // Aktuelle Seite von AJAX

    // WP_Query-Argumente für Posts
    $args = array(
        'post_type' => 'newsletter_archiv',
        'posts_per_page' => 10,
        'paged' => $paged, // Paged für Pagination
        'post_status' => 'publish', // Nur veröffentlichte Beiträge
    );

    // Kategorienfilter
    if ($category_id) {
        $args['tax_query'] = array(
            array(
                'taxonomy' => 'category',
                'field' => 'term_id',
                'terms' => $category_id,
            ),
        );
    }

    // Query und Ausgabe
    $query = new WP_Query($args);
    ob_start();
    if ($query->have_posts()) :
        echo '<div class="blog newsletter-list">';
            while ($query->have_posts()) : $query->the_post(); ?>
                <div class="single-newsletter-item" style="view-transition-name: newsletter-<?php the_ID(); ?>">
                    <a href="<?php the_permalink(); ?>">
                        <div class="single-newsletter-item-inner">
                            <?php if (has_post_thumbnail()) : ?>
                                <div class="post-thumbnail">
                                    <?php the_post_thumbnail('blog-thumb'); ?>
                                </div>
                            <?php endif; ?>

                            <div class="post-meta">
                                <span class="post-date"><?php echo get_the_date('d. F Y'); ?></span>
                                <span> | </span>
                                <span class="post-author"><?php the_author(); ?></span>
                            </div>

                            <h2><?php the_title(); ?></h2>
                            <div class="post-excerpt"><?php the_excerpt(); ?></div>

                            <div class="read-more">
                                <p>Weiterlesen</p>
                            </div>
                        </div>
                    </a>
                </div>
            <?php endwhile;
        echo '</div>';

        // Pagination ausgeben
        echo '<div class="pagination">';
        echo paginate_links(array(
            'total' => $query->max_num_pages,
            'current' => $paged,
            'format' => '?paged=%#%', // Verwende ?paged= für Pagination
            // 'add_args' => array( // Füge den 'action'-Parameter hinzu
            //     'action' => 'filter_newsletter_by_category',
            // ),
            'prev_text' => __( 'Vorherige', 'textdomain' ),
            'next_text' => __( 'Nächste', 'textdomain' ),
        ));
        echo '</div>';

    else :
        echo '<p>Keine Beiträge gefunden.</p>';
    endif;
    wp_reset_postdata();

    $output = ob_get_clean();
    if (empty($output)) {
        wp_send_json_error('Keine Inhalte gefunden');
    } else {
        echo $output;
    }

    wp_die();
}
add_action('wp_ajax_filter_newsletter_by_category', 'filter_newsletter_by_category');
add_action('wp_ajax_nopriv_filter_newsletter_by_category', 'filter_newsletter_by_category');
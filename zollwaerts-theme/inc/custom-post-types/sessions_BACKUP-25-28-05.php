<?php
/**
 * Add "Sessions" CPT 
 */
function create_sessions_cpt() {

    $labels = array(
        'name' => _x('Sessions', 'Post Type General Name', 'textdomain'),
        'singular_name' => _x('Session', 'Post Type Singular Name', 'textdomain'),
        'menu_name' => __('Sessions', 'textdomain'),
        'name_admin_bar' => __('Session', 'textdomain'),
        'archives' => __('Session Archives', 'textdomain'),
        'attributes' => __('Session Attributes', 'textdomain'),
        'parent_item_colon' => __('Parent Session:', 'textdomain'),
        'all_items' => __('All Sessions', 'textdomain'),
        'add_new_item' => __('Add New Session', 'textdomain'),
        'add_new' => __('Add New', 'textdomain'),
        'new_item' => __('New Session', 'textdomain'),
        'edit_item' => __('Edit Session', 'textdomain'),
        'update_item' => __('Update Session', 'textdomain'),
        'view_item' => __('View Session', 'textdomain'),
        'view_items' => __('View Sessions', 'textdomain'),
        'search_items' => __('Search Session', 'textdomain'),
        'not_found' => __('Not found', 'textdomain'),
        'not_found_in_trash' => __('Not found in Trash', 'textdomain'),
        'featured_image' => __('Featured Image', 'textdomain'),
        'set_featured_image' => __('Set featured image', 'textdomain'),
        'remove_featured_image' => __('Remove featured image', 'textdomain'),
        'use_featured_image' => __('Use as featured image', 'textdomain'),
        'insert_into_item' => __('Insert into session', 'textdomain'),
        'uploaded_to_this_item' => __('Uploaded to this session', 'textdomain'),
        'items_list' => __('Sessions list', 'textdomain'),
        'items_list_navigation' => __('Sessions list navigation', 'textdomain'),
        'filter_items_list' => __('Filter sessions list', 'textdomain'),
    );

    $args = array(
        'label' => __('Session', 'textdomain'),
        'description' => __('Post Type for Sessions', 'textdomain'),
        'labels' => $labels,
        'supports' => array('title', 'editor', 'thumbnail', 'excerpt', 'revisions'),
        'taxonomies' => array(), // Keine Standard-Taxonomien
        'hierarchical' => false,
        'public' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'menu_position' => 5,
        'menu_icon' => 'dashicons-calendar', // Icon für das Menü im Admin-Bereich
        'show_in_admin_bar' => true,
        'show_in_nav_menus' => true,
        'can_export' => true,
        'has_archive' => true,
        'exclude_from_search' => false,
        'publicly_queryable' => true,
        'capability_type' => 'post',
        'show_in_rest' => true, // Gutenberg Unterstützung
        'rewrite' => array('slug' => 'sessions', 'with_front' => false), // Hier wird die Permalink-Struktur festgelegt
    );

    register_post_type('sessions', $args);

}
add_action('init', 'create_sessions_cpt', 0);


/**
 * Session Taxonomien
 */
// Session taxonomy "Session Location"
function create_session_location_taxonomy() {

    $labels = array(
        'name' => _x('Session Locations', 'taxonomy general name', 'textdomain'),
        'singular_name' => _x('Session Location', 'taxonomy singular name', 'textdomain'),
        'search_items' => __('Search Session Locations', 'textdomain'),
        'all_items' => __('All Session Locations', 'textdomain'),
        'parent_item' => __('Parent Session Location', 'textdomain'),
        'parent_item_colon' => __('Parent Session Location:', 'textdomain'),
        'edit_item' => __('Edit Session Location', 'textdomain'),
        'update_item' => __('Update Session Location', 'textdomain'),
        'add_new_item' => __('Add New Session Location', 'textdomain'),
        'new_item_name' => __('New Session Location Name', 'textdomain'),
        'menu_name' => __('Session Locations', 'textdomain'),
    );

    $args = array(
        'labels' => $labels,
        'hierarchical' => true, // `true` macht die Taxonomie hierarchisch (wie Kategorien), `false` nicht-hierarchisch (wie Tags)
        'public' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true,
        'show_in_quick_edit' => true,
        'show_admin_column' => true,
        'show_in_rest' => true, // Für Gutenberg- und REST-API-Unterstützung
    );

    register_taxonomy('session_location', array('sessions'), $args);

}
add_action('init', 'create_session_location_taxonomy', 0);


// Session taxonomy "Target Audience"
function create_session_target_audience_taxonomy() {

    $labels = array(
        'name' => _x('Target Audiences', 'taxonomy general name', 'textdomain'),
        'singular_name' => _x('Target Audience', 'taxonomy singular name', 'textdomain'),
        'search_items' => __('Search Target Audiences', 'textdomain'),
        'all_items' => __('All Target Audiences', 'textdomain'),
        'parent_item' => __('Parent Target Audience', 'textdomain'),
        'parent_item_colon' => __('Parent Target Audience:', 'textdomain'),
        'edit_item' => __('Edit Target Audience', 'textdomain'),
        'update_item' => __('Update Target Audience', 'textdomain'),
        'add_new_item' => __('Add New Target Audience', 'textdomain'),
        'new_item_name' => __('New Target Audience Name', 'textdomain'),
        'menu_name' => __('Target Audience', 'textdomain'),
    );

    $args = array(
        'labels' => $labels,
        'hierarchical' => true, // `true` macht die Taxonomie hierarchisch (wie Kategorien), `false` nicht-hierarchisch (wie Tags)
        'public' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true,
        'show_in_quick_edit' => true,
        'show_admin_column' => true,
        'show_in_rest' => true,
    );

    register_taxonomy('target_audience', array('sessions'), $args);

}
add_action('init', 'create_session_target_audience_taxonomy', 0);


// Session Meta Box for "global Session"/"All Tracks"
// add global_session_meta_box
function add_global_session_meta_box() {
    add_meta_box(
        'global_session_meta_box', // ID der Meta Box
        __('Global Session', 'textdomain'), // Titel der Meta Box
        'render_global_session_meta_box', // Callback, um die Inhalte der Meta Box anzuzeigen
        'sessions', // Post-Typ
        'side', // Position
        'default' // Priorität
    );
}
add_action('add_meta_boxes', 'add_global_session_meta_box');

// render global_session_meta_box
function render_global_session_meta_box($post) {
    // Sicherheitstoken hinzufügen
    wp_nonce_field('save_global_session_meta_box_data', 'global_session_meta_box_nonce');

    // Aktuellen Wert der Checkbox abrufen
    $value = get_post_meta($post->ID, '_global_session', true);

    // Checkbox anzeigen
    echo '<label for="global_session">';
    echo '<input type="checkbox" id="global_session" name="global_session" value="1"' . checked(1, $value, false) . ' />';
    echo __('This session belongs to all tracks', 'textdomain');
    echo '</label>';
}

// save global_session_meta_box
function save_global_session_meta_box_data($post_id) {
    // Sicherheitsüberprüfung
    if (!isset($_POST['global_session_meta_box_nonce']) || !wp_verify_nonce($_POST['global_session_meta_box_nonce'], 'save_global_session_meta_box_data')) {
        return;
    }

    // Überprüfung der Berechtigungen
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Speichern der Checkbox-Daten
    $is_global_session = isset($_POST['global_session']) ? 1 : 0;
    update_post_meta($post_id, '_global_session', $is_global_session);
}
add_action('save_post', 'save_global_session_meta_box_data');


// Session taxonomy "Session Type"
function create_session_type_taxonomy() {

    $labels = array(
        'name' => _x('Session Types', 'taxonomy general name', 'textdomain'),
        'singular_name' => _x('Session Type', 'taxonomy singular name', 'textdomain'),
        'search_items' => __('Search Session Types', 'textdomain'),
        'all_items' => __('All Session Types', 'textdomain'),
        'parent_item' => __('Parent Session Type', 'textdomain'),
        'parent_item_colon' => __('Parent Session Type:', 'textdomain'),
        'edit_item' => __('Edit Session Type', 'textdomain'),
        'update_item' => __('Update Session Type', 'textdomain'),
        'add_new_item' => __('Add New Session Type', 'textdomain'),
        'new_item_name' => __('New Session Type Name', 'textdomain'),
        'menu_name' => __('Session Types', 'textdomain'),
    );

    $args = array(
        'labels' => $labels,
        'hierarchical' => true, // Setze auf `false`, wenn du keine hierarchische Struktur möchtest
        'public' => true,
        'show_ui' => true,
        'show_in_menu' => true,
        'show_in_nav_menus' => true,
        'show_tagcloud' => true,
        'show_in_quick_edit' => true,
        'show_admin_column' => true,
        'show_in_rest' => true, // Für Gutenberg- und REST-API-Unterstützung
    );

    register_taxonomy('session_type', array('sessions'), $args);

}
add_action('init', 'create_session_type_taxonomy', 0);


// Create custom Meta box for "Timing"
// Meta Box hinzufügen
function add_session_time_meta_box() {
    add_meta_box(
        'session_time_meta_box', // ID der Meta Box
        __('Session Timing', 'textdomain'), // Titel der Meta Box
        'render_session_time_meta_box', // Callback, um die Inhalte der Meta Box anzuzeigen
        'sessions', // Post-Typ
        'side', // Position
        'high' // Priorität
    );
}
add_action('add_meta_boxes', 'add_session_time_meta_box');

// Inhalt der Meta Box "Timing" anzeigen
function render_session_time_meta_box($post) {
    // Sicherheitstoken hinzufügen
    wp_nonce_field('save_session_time_meta_box_data', 'session_time_meta_box_nonce');

    // Aktuelle Werte abrufen
    $start_time = get_post_meta($post->ID, '_session_start_time', true);
    $end_time = get_post_meta($post->ID, '_session_end_time', true);
    $session_date = get_post_meta($post->ID, '_session_date', true);

    // HTML für die Meta Box
    echo '<label for="session_date">' . __('Session Date:', 'textdomain') . '</label>';
    echo '<input type="date" id="session_date" name="session_date" value="' . esc_attr($session_date) . '" size="25" />';

    echo '<br><br>';

    echo '<label for="session_start_time">' . __('Start Time:', 'textdomain') . '</label>';
    echo '<input type="time" id="session_start_time" name="session_start_time" value="' . esc_attr($start_time) . '" size="25" />';

    echo '<br><br>';

    echo '<label for="session_end_time">' . __('End Time:', 'textdomain') . '</label>';
    echo '<input type="time" id="session_end_time" name="session_end_time" value="' . esc_attr($end_time) . '" size="25" />';
}


// Meta Box für "Kein Link zur Detailseite" hinzufügen
function add_no_link_to_session_detail_meta_box() {
    add_meta_box(
        'no_link_to_detail_meta_box', // ID der Meta Box
        __('Kein Link zur Detailseite', 'textdomain'), // Titel der Meta Box
        'render_no_link_to_session_detail_meta_box', // Callback, um die Inhalte der Meta Box anzuzeigen
        'sessions', // Post-Typ
        'side', // Position
        'default' // Priorität
    );
}
add_action('add_meta_boxes', 'add_no_link_to_session_detail_meta_box');

// Inhalt der Meta Box "Kein Link zur Detailseite" anzeigen
function render_no_link_to_session_detail_meta_box($post) {
    // Sicherheitstoken hinzufügen
    wp_nonce_field('save_no_link_to_session_detail_meta_box_data', 'no_link_to_detail_meta_box_nonce');

    // Aktuellen Wert der Checkbox abrufen
    $value = get_post_meta($post->ID, '_no_link_to_detail', true);

    // Checkbox anzeigen
    echo '<label for="no_link_to_detail">';
    echo '<input type="checkbox" id="no_link_to_detail" name="no_link_to_detail" value="1"' . checked(1, $value, false) . ' />';
    echo __('Kein Link zur Detailseite', 'textdomain');
    echo '</label>';
}


// Session Leaders 
// Meta Box "Session Leaders" hinzufügen
function add_session_leader_meta_box() {
    add_meta_box(
        'session_leader_meta_box',
        __('Session Leaders', 'textdomain'),
        'render_session_leader_meta_box',
        'sessions',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'add_session_leader_meta_box');

// Rendering der "Session Leaders" Meta Box
function render_session_leader_meta_box($post) {
    wp_nonce_field('save_session_leader_meta_box_data', 'session_leader_meta_box_nonce');

    // Vorhandene Werte abrufen
    $leaders = get_post_meta($post->ID, '_session_leaders', true);
    if (!is_array($leaders)) {
        $leaders = array();
    }

    // Container für alle Leader
    echo '<div id="session-leaders-container">';
    
    // Vorhandene Leader anzeigen
    if (!empty($leaders)) {
        foreach ($leaders as $index => $leader) {
            render_leader_fields($index, $leader);
        }
    }
    
    echo '</div>';

    // Button zum Hinzufügen weiterer Leader
    echo '<button type="button" class="button" id="add-session-leader">' . __('Weiteren Speaker hinzufügen', 'textdomain') . '</button>';

    // JavaScript für dynamisches Hinzufügen/Entfernen
    ?>
    <script>
    jQuery(document).ready(function($) {
        var leaderIndex = <?php echo count($leaders); ?>;

        // Funktion zum Hinzufügen eines neuen Leader-Sets
        $('#add-session-leader').click(function() {
            var template = `
                <div class="leader-section">
                    <h4>Speaker ${leaderIndex + 1}</h4>
                    <p>
                        <label for="session_leader_name_${leaderIndex}">Name des Speakers</label><br>
                        <input type="text" id="session_leader_name_${leaderIndex}" 
                               name="session_leaders[${leaderIndex}][name]" value="" size="30" />
                    </p>
                    <p>
                        <label>Bild des Speakers</label><br>
                        <input type="hidden" name="session_leaders[${leaderIndex}][image_id]" 
                               class="leader-image-id" value="" />
                        <img class="leader-image-preview" src="" style="max-width: 240px; height: auto; display: none; margin-bottom: 10px;" />
                        <br>
                        <button type="button" class="button upload-leader-image">Bild auswählen</button>
                        <button type="button" class="button remove-leader-image" style="display: none;">Bild entfernen</button>
                    </p>
                    <button type="button" class="button remove-leader">Speaker entfernen</button>
                    <hr>
                </div>
            `;
            $('#session-leaders-container').append(template);
            initializeMediaUploader();
            leaderIndex++;
        });

        // Leader entfernen
        $(document).on('click', '.remove-leader', function() {
            $(this).closest('.leader-section').remove();
        });

        // Medien-Uploader initialisieren
        function initializeMediaUploader() {
            $('.upload-leader-image').each(function() {
                var $button = $(this);
                var $section = $button.closest('.leader-section');
                var $imageId = $section.find('.leader-image-id');
                var $preview = $section.find('.leader-image-preview');
                var $removeButton = $section.find('.remove-leader-image');

                $button.click(function(e) {
                    e.preventDefault();
                    var mediaUploader = wp.media({
                        title: '<?php _e("Wähle ein Bild für den Speaker", "textdomain"); ?>',
                        button: {
                            text: '<?php _e("Bild auswählen", "textdomain"); ?>'
                        },
                        multiple: false
                    });

                    mediaUploader.on('select', function() {
                        var attachment = mediaUploader.state().get('selection').first().toJSON();
                        $imageId.val(attachment.id);
                        $preview.attr('src', attachment.url).show();
                        $removeButton.show();
                    });

                    mediaUploader.open();
                });

                $removeButton.click(function() {
                    $imageId.val('');
                    $preview.attr('src', '').hide();
                    $removeButton.hide();
                });
            });
        }

        // Initial vorhandene Media-Uploader initialisieren
        initializeMediaUploader();
    });
    </script>
    <?php
}

// Hilfsfunktion zum Rendern der Leader-Felder
function render_leader_fields($index, $leader) {
    $name = isset($leader['name']) ? $leader['name'] : '';
    $position = isset($leader['position']) ? $leader['position'] : ''; // Neues Feld
    $image_id = isset($leader['image_id']) ? $leader['image_id'] : '';
    $image_url = $image_id ? wp_get_attachment_url($image_id) : '';
    
    ?>
    <div class="leader-section">
        <h4>Speaker <?php echo $index + 1; ?></h4>
        <p>
            <label for="session_leader_name_<?php echo $index; ?>"><?php _e('Name des Speakers', 'textdomain'); ?></label><br>
            <input type="text" id="session_leader_name_<?php echo $index; ?>" 
                   name="session_leaders[<?php echo $index; ?>][name]" 
                   value="<?php echo esc_attr($name); ?>" size="30" />
        </p>
        <p>
            <label for="session_leader_position_<?php echo $index; ?>"><?php _e('Position/Unternehmen', 'textdomain'); ?></label><br>
            <input type="text" id="session_leader_position_<?php echo $index; ?>" 
                   name="session_leaders[<?php echo $index; ?>][position]" 
                   value="<?php echo esc_attr($position); ?>" size="30" />
        </p>
        <p>
            <label><?php _e('Bild des Speakers', 'textdomain'); ?></label><br>
            <input type="hidden" name="session_leaders[<?php echo $index; ?>][image_id]" 
                   class="leader-image-id" value="<?php echo esc_attr($image_id); ?>" />
            <img class="leader-image-preview" 
                 src="<?php echo esc_url($image_url); ?>" 
                 style="<?php echo $image_url ? 'display:block;' : 'display:none;'; ?> max-width: 240px; height: auto; margin-bottom: 10px;" />
            <button type="button" class="button upload-leader-image"><?php _e('Bild auswählen', 'textdomain'); ?></button>
            <button type="button" class="button remove-leader-image" style="<?php echo $image_url ? 'display:inline-block;' : 'display:none;'; ?>"><?php _e('Bild entfernen', 'textdomain'); ?></button>
        </p>
        <button type="button" class="button remove-leader"><?php _e('Speaker entfernen', 'textdomain'); ?></button>
        <hr>
    </div>
    <?php
}

// Speichern der Meta Box-Daten
function save_session_leader_meta_box_data($post_id) {
    if (!isset($_POST['session_leader_meta_box_nonce']) || 
        !wp_verify_nonce($_POST['session_leader_meta_box_nonce'], 'save_session_leader_meta_box_data')) {
        return;
    }

    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Speichern der Leader-Daten
    if (isset($_POST['session_leaders'])) {
        $leaders = array_values(array_filter($_POST['session_leaders'], function($leader) {
            return !empty($leader['name']) || !empty($leader['image_id']);
        }));
        update_post_meta($post_id, '_session_leaders', $leaders);
    } else {
        delete_post_meta($post_id, '_session_leaders');
    }
}
add_action('save_post', 'save_session_leader_meta_box_data');



// Speichern der Meta Box-Daten
function save_no_link_to_session_detail_meta_box_data($post_id) {
    // Sicherheitsüberprüfung
    if (!isset($_POST['no_link_to_detail_meta_box_nonce']) || !wp_verify_nonce($_POST['no_link_to_detail_meta_box_nonce'], 'save_no_link_to_session_detail_meta_box_data')) {
        return;
    }

    // Überprüfung der Berechtigungen
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Speichern der Checkbox-Daten
    $no_link_to_detail = isset($_POST['no_link_to_detail']) ? 1 : 0;
    update_post_meta($post_id, '_no_link_to_detail', $no_link_to_detail);
}
add_action('save_post', 'save_no_link_to_session_detail_meta_box_data');


// Speichern der Meta Box-Daten
function save_session_time_meta_box_data($post_id) {
    // Sicherheitsüberprüfung
    if (!isset($_POST['session_time_meta_box_nonce']) || !wp_verify_nonce($_POST['session_time_meta_box_nonce'], 'save_session_time_meta_box_data')) {
        return;
    }

    // Überprüfung der Berechtigungen
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Speichern des Datums
    if (isset($_POST['session_date'])) {
        update_post_meta($post_id, '_session_date', sanitize_text_field($_POST['session_date']));
    }

    // Speichern der Startzeit
    if (isset($_POST['session_start_time'])) {
        update_post_meta($post_id, '_session_start_time', sanitize_text_field($_POST['session_start_time']));
    }

    // Speichern der Endzeit
    if (isset($_POST['session_end_time'])) {
        update_post_meta($post_id, '_session_end_time', sanitize_text_field($_POST['session_end_time']));
    }
}
add_action('save_post', 'save_session_time_meta_box_data');

function load_session_validation_script($hook) {
    // Stelle sicher, dass das Script nur auf den relevanten Seiten geladen wird
    if ($hook != 'post.php' && $hook != 'post-new.php') {
        return;
    }

    // Überprüfe, ob es sich um den Custom Post Type "sessions" handelt
    global $post;
    if ($post->post_type != 'sessions') {
        return;
    }

    // Lade das JavaScript
    wp_enqueue_script('session-validation-script', get_template_directory_uri() . '/js/session-validation.js', array('jquery'), null, true);

    // Sende die Nonce an das JavaScript
    wp_localize_script('session-validation-script', 'ajax_object', array(
        'security' => wp_create_nonce('validate_session_time_overlap')
    ));
}
add_action('admin_enqueue_scripts', 'load_session_validation_script');

// AJAX-Handler für die Validierung der Session-Zeiten
function validate_session_time_overlap_ajax() {
    // Sicherheitsüberprüfung
    check_ajax_referer('validate_session_time_overlap', 'security');

    // Post-Daten aus dem AJAX-Request
    $post_id = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;
    $location_id = isset($_POST['location_id']) ? intval($_POST['location_id']) : 0;
    $start_time = isset($_POST['start_time']) ? sanitize_text_field($_POST['start_time']) : '';
    $end_time = isset($_POST['end_time']) ? sanitize_text_field($_POST['end_time']) : '';
    $session_date = isset($_POST['session_date']) ? sanitize_text_field($_POST['session_date']) : '';


    // Überprüfe auf Überschneidungen
    $args = array(
        'post_type' => 'sessions',
        'post_status' => 'publish',
        'posts_per_page' => -1,
        'post__not_in' => array($post_id), // Schließt den aktuellen Post aus
        'tax_query' => array(
            array(
                'taxonomy' => 'session_location',
                'field' => 'term_id',
                'terms' => $location_id,
            ),
        ),
        'meta_query' => array(
            'relation' => 'AND',
            array(
                'key' => '_session_date',
                'value' => $session_date,
                'compare' => '=', // Überprüft, ob das Datum übereinstimmt
                'type' => 'DATE'
            ),
            array(
                'key' => '_session_start_time',
                'value' => $end_time,
                'compare' => '<',
                'type' => 'TIME'
            ),
            array(
                'key' => '_session_end_time',
                'value' => $start_time,
                'compare' => '>',
                'type' => 'TIME'
            ),
        ),
    );

    $overlapping_sessions = new WP_Query($args);

    if ($overlapping_sessions->have_posts()) {
        // Fehlernachricht zurückgeben
        wp_send_json_error(__('Error: There is a time conflict with another session in the same location.'));
    } else {
        wp_send_json_success();
    }

    wp_die(); // Beendet AJAX-Request
}
add_action('wp_ajax_validate_session_time_overlap', 'validate_session_time_overlap_ajax');


// Felder zu Quick Edit hinzufügen
function add_session_custom_quick_edit_fields($column_name, $post_type) {
    if ($post_type == 'sessions') {
        switch ($column_name) {
            case 'global_session':
                ?>
                <fieldset class="inline-edit-col-right">
                    <div class="inline-edit-col">
                        <label class="alignleft">
                            <span class="title"><?php _e('Global Event', 'textdomain'); ?></span>
                            <span class="input-text-wrap">
                                <input type="checkbox" name="global_session" value="1">
                            </span>
                        </label>
                    </div>
                </fieldset>
                <?php
                break;
            case 'no_link_to_detail':
                ?>
                <fieldset class="inline-edit-col-right">
                    <div class="inline-edit-col">
                        <label class="alignleft">
                            <span class="title"><?php _e('Kein Link zur Detailseite', 'textdomain'); ?></span>
                            <span class="input-text-wrap">
                                <input type="checkbox" name="no_link_to_detail" value="1">
                            </span>
                        </label>
                    </div>
                </fieldset>
                <?php
                break;
        }
    }
}
add_action('quick_edit_custom_box', 'add_session_custom_quick_edit_fields', 10, 2);



// Werte für Quick Edit bereitstellen
function enqueue_session_custom_quick_edit_script() {
    wp_enqueue_script('session-quick-edit', get_template_directory_uri() . '/js/session-quick-edit.js', array('jquery'), '', true);
}
add_action('admin_enqueue_scripts', 'enqueue_session_custom_quick_edit_script');


// Quick Edit-Daten speichern
function save_session_custom_quick_edit_data($post_id) {
    if (isset($_POST['global_session'])) {
        update_post_meta($post_id, '_global_session', 1);
    } else {
        update_post_meta($post_id, '_global_session', 0);
    }

    if (isset($_POST['no_link_to_detail'])) {
        update_post_meta($post_id, '_no_link_to_detail', 1);
    } else {
        update_post_meta($post_id, '_no_link_to_detail', 0);
    }
}
add_action('save_post', 'save_session_custom_quick_edit_data');


// Spalten hinzufügen
function add_session_custom_columns($columns) {
    $columns['global_session'] = __('Global Event', 'textdomain');
    $columns['no_link_to_detail'] = __('Kein Link zur Detailseite', 'textdomain');
    return $columns;
}
add_filter('manage_sessions_posts_columns', 'add_session_custom_columns');


// Inhalte der Spalten füllen und IDs hinzufügen
function custom_session_columns_content($column, $post_id) {
    switch ($column) {
        case 'global_session':
            $value = get_post_meta($post_id, '_global_session', true);
            echo '<span id="global_session-' . $post_id . '">' . ($value ? __('Yes', 'textdomain') : __('No', 'textdomain')) . '</span>';
            break;
        case 'no_link_to_detail':
            $value = get_post_meta($post_id, '_no_link_to_detail', true);
            echo '<span id="no_link_to_detail-' . $post_id . '">' . ($value ? __('Yes', 'textdomain') : __('No', 'textdomain')) . '</span>';
            break;
    }
}
add_action('manage_sessions_posts_custom_column', 'custom_session_columns_content', 10, 2);








// Meta Box für "Partner" hinzufügen
function add_partner_meta_box() {
    add_meta_box(
        'partner_meta_box', // ID der Meta Box
        __('Partner', 'textdomain'), // Titel der Meta Box
        'render_partner_meta_box', // Callback, um die Inhalte der Meta Box anzuzeigen
        'sessions', // Post-Typ
        'side', // Position
        'default' // Priorität
    );
}
add_action('add_meta_boxes', 'add_partner_meta_box');

// Inhalt der Meta Box "Partner" anzeigen
function render_partner_meta_box($post) {
    // Sicherheitstoken hinzufügen
    wp_nonce_field('save_partner_meta_box_data', 'partner_meta_box_nonce');

    // Aktuellen Wert der Checkbox abrufen
    $value = get_post_meta($post->ID, '_is_partner', true);

    // Checkbox anzeigen
    echo '<label for="is_partner">';
    echo '<input type="checkbox" id="is_partner" name="is_partner" value="1"' . checked(1, $value, false) . ' />';
    echo __('Diese Session ist von einem Partner', 'textdomain');
    echo '</label>';
}

// Speichern der Meta Box-Daten
function save_partner_meta_box_data($post_id) {
    // Sicherheitsüberprüfung
    if (!isset($_POST['partner_meta_box_nonce']) || !wp_verify_nonce($_POST['partner_meta_box_nonce'], 'save_partner_meta_box_data')) {
        return;
    }

    // Überprüfung der Berechtigungen
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }

    // Speichern der Checkbox-Daten
    $is_partner = isset($_POST['is_partner']) ? 1 : 0;
    update_post_meta($post_id, '_is_partner', $is_partner);
}
add_action('save_post', 'save_partner_meta_box_data');

// Spalte für Partner in der Übersicht hinzufügen
function add_partner_column($columns) {
    $columns['is_partner'] = __('Partner', 'textdomain');
    return $columns;
}
add_filter('manage_sessions_posts_columns', 'add_partner_column');

// Inhalte der Partner-Spalte füllen
function partner_column_content($column, $post_id) {
    if ($column == 'is_partner') {
        $value = get_post_meta($post_id, '_is_partner', true);
        echo '<span id="is_partner-' . $post_id . '">' . ($value ? __('Yes', 'textdomain') : __('No', 'textdomain')) . '</span>';
    }
}
add_action('manage_sessions_posts_custom_column', 'partner_column_content', 10, 2);

// Quick Edit für Partner-Checkbox
function add_partner_quick_edit_field($column_name, $post_type) {
    if ($post_type == 'sessions' && $column_name == 'is_partner') {
        ?>
        <fieldset class="inline-edit-col-right">
            <div class="inline-edit-col">
                <label class="alignleft">
                    <span class="title"><?php _e('Partner', 'textdomain'); ?></span>
                    <span class="input-text-wrap">
                        <input type="checkbox" name="is_partner" value="1">
                    </span>
                </label>
            </div>
        </fieldset>
        <?php
    }
}
add_action('quick_edit_custom_box', 'add_partner_quick_edit_field', 10, 2);

// Quick Edit-Daten für Partner speichern
function save_partner_quick_edit_data($post_id) {
    if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
        return;
    }
    
    if (!current_user_can('edit_post', $post_id)) {
        return;
    }
    
    if (isset($_POST['is_partner'])) {
        update_post_meta($post_id, '_is_partner', 1);
    } else {
        // Nur aktualisieren, wenn das Feld im Formular vorhanden war (Quick Edit wurde verwendet)
        if (isset($_POST['post_type']) && $_POST['post_type'] == 'sessions') {
            update_post_meta($post_id, '_is_partner', 0);
        }
    }
}
add_action('save_post', 'save_partner_quick_edit_data');

// Stelle sicher, dass die Partner-Daten für Quick Edit verfügbar sind
// Diese Funktion ergänzt das bestehende quick-edit JavaScript
// Füge diesen Code zu session-quick-edit.js hinzu:
/*
// Partner-Wert für Quick Edit abrufen und setzen
var isPartner = $('#is_partner-' + postId).text() === 'Yes' ? true : false;
$('input[name="is_partner"]').prop('checked', isPartner);
*/

// Metabox für Session Details hinzufügen
function add_session_meta_boxes() {
    add_meta_box(
        'session_details',
        __('Session Details', 'textdomain'),
        'render_session_meta_box',
        'sessions',
        'normal',
        'high'
    );
}
add_action('add_meta_boxes', 'add_session_meta_boxes');

// Render Metabox
function render_session_meta_box($post) {
    // Nonce für Sicherheitsüberprüfung
    wp_nonce_field('session_meta_box', 'session_meta_box_nonce');

    // Werte abrufen
    $subtitle = get_post_meta($post->ID, '_session_subtitle', true);
    
    ?>
    <p>
        <label for="session_subtitle"><?php _e('Untertitel', 'textdomain'); ?></label><br>
        <input type="text" id="session_subtitle" name="session_subtitle" 
               value="<?php echo esc_attr($subtitle); ?>" style="width: 100%;" />
    </p>
    <?php
    // Hier folgen die anderen bereits existierenden Felder...
}

// Speichern der Metadaten
function save_session_meta($post_id) {
    // Sicherheitsüberprüfungen...
    
    // Subtitle speichern
    if (isset($_POST['session_subtitle'])) {
        update_post_meta($post_id, '_session_subtitle', sanitize_text_field($_POST['session_subtitle']));
    }
    
    // Hier folgt das Speichern der anderen bereits existierenden Felder...
}
add_action('save_post_sessions', 'save_session_meta');

/* TODO: unbedingt lokal einbinden */
/* @import url("https://fonts.googleapis.com/css2?family=Barlow:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Oswald:wght@200..700&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"); */

/* Poppins Thin */
@font-face {
	font-family: "Poppins";
	font-style: normal;
	font-weight: 100;
	src: url("../assets/fonts/Poppins-Thin.ttf") format("truetype");
}

/* Poppins Extra Light */
@font-face {
	font-family: "Poppins";
	font-style: normal;
	font-weight: 200;
	src: url("../assets/fonts/Poppins-ExtraLight.ttf") format("truetype");
}

/* Poppins Light */
@font-face {
	font-family: "Poppins";
	font-style: normal;
	font-weight: 300;
	src: url("../assets/fonts/Poppins-Light.ttf") format("truetype");
}

/* Poppins Regular */
@font-face {
	font-family: "Poppins";
	font-style: normal;
	font-weight: 400;
	src: url("../assets/fonts/Poppins-Regular.ttf") format("truetype");
}

/* Poppins Medium */
@font-face {
	font-family: "Poppins";
	font-style: normal;
	font-weight: 500;
	src: url("../assets/fonts/Poppins-Medium.ttf") format("truetype");
}

/* Poppins SemiBold */
@font-face {
	font-family: "Poppins";
	font-style: normal;
	font-weight: 600;
	src: url("../assets/fonts/Poppins-SemiBold.ttf") format("truetype");
}

/* Poppins Bold */
@font-face {
	font-family: "Poppins";
	font-style: normal;
	font-weight: 700;
	src: url("../assets/fonts/Poppins-Bold.ttf") format("truetype");
}

/* Poppins ExtraBold */
@font-face {
	font-family: "Poppins";
	font-style: normal;
	font-weight: 800;
	src: url("../assets/fonts/Poppins-ExtraBold.ttf") format("truetype");
}

/* Poppins Black */
@font-face {
	font-family: "Poppins";
	font-style: normal;
	font-weight: 900;
	src: url("../assets/fonts/Poppins-Black.ttf") format("truetype");
}

/* Poppins Thin Italic */
@font-face {
	font-family: "Poppins";
	font-style: italic;
	font-weight: 100;
	src: url("../assets/fonts/Poppins-ThinItalic.ttf") format("truetype");
}

/* Poppins Extra Light Italic */
@font-face {
	font-family: "Poppins";
	font-style: italic;
	font-weight: 200;
	src: url("../assets/fonts/Poppins-ExtraLightItalic.ttf") format("truetype");
}

/* Poppins Light Italic */
@font-face {
	font-family: "Poppins";
	font-style: italic;
	font-weight: 300;
	src: url("../assets/fonts/Poppins-LightItalic.ttf") format("truetype");
}

/* Poppins Regular Italic */
@font-face {
	font-family: "Poppins";
	font-style: italic;
	font-weight: 400;
	src: url("../assets/fonts/Poppins-Italic.ttf") format("truetype");
}

/* Poppins Medium Italic */
@font-face {
	font-family: "Poppins";
	font-style: italic;
	font-weight: 500;
	src: url("../assets/fonts/Poppins-MediumItalic.ttf") format("truetype");
}

/* Poppins SemiBold Italic */
@font-face {
	font-family: "Poppins";
	font-style: italic;
	font-weight: 600;
	src: url("../assets/fonts/Poppins-SemiBoldItalic.ttf") format("truetype");
}

/* Poppins Bold Italic */
@font-face {
	font-family: "Poppins";
	font-style: italic;
	font-weight: 700;
	src: url("../assets/fonts/Poppins-BoldItalic.ttf") format("truetype");
}

/* Poppins ExtraBold Italic */
@font-face {
	font-family: "Poppins";
	font-style: italic;
	font-weight: 800;
	src: url("../assets/fonts/Poppins-ExtraBoldItalic.ttf") format("truetype");
}

/* Poppins Black Italic */
@font-face {
	font-family: "Poppins";
	font-style: italic;
	font-weight: 900;
	src: url("../assets/fonts/Poppins-BlackItalic.ttf") format("truetype");
}

/**
BASIC VIEW Transitions 
*/

/*
* View transitions 
*/
/* Transition for the content change */
@media (prefers-reduced-motion: no-preference) {
	@view-transition {
		navigation: auto;
	}

	/* Globale Einstellungen für View Transitions */
	/* ::view-transition-group(*), */
	::view-transition-old(*),
	::view-transition-new(*) {
		animation-duration: 0.5s;
		animation-timing-function: ease-in-out;
	}

	/* View Transition Animations */
	/* ::view-transition-group(*) {
		animation-duration: 0.5s;
		animation-timing-function: var(--effect-transition-timing-function);
	} */

	@keyframes scale-out {
		to {
			scale: 0;
			opacity: 0;
		}
	}

	::view-transition-old(*):only-child {
		animation-name: scale-out;
		animation-duration: 0.5s;
	}

	@keyframes scale-in {
		from {
			scale: 0;
			opacity: 0;
		}
		to {
			scale: 1;
			opacity: 1;
		}
	}

	::view-transition-new(*):only-child {
		animation-name: scale-in;
		animation-duration: 0.5s;
		/* animation-delay: 0.25s; */
	}

	/* Body/full site transition */
	/* @keyframes slide-from-right {
		from { transform: translateX(30px); }
	}

	@keyframes slide-to-left {
		to { transform: translateX(-30px); }
	}

	::view-transition-old(root) {
		animation: 0.5s ease both slide-to-left, fade-out;
	}

	::view-transition-new(root) {
		animation: 0.5s ease both slide-from-right, fade-in;
	} */

	/* Preserve aspect ratio */

	@keyframes fade-in {
		to {
			/* transform: rotate(180deg); */
			opacity: 1;
		}
	}
	@keyframes fade-out {
		to {
			/* transform: rotate(180deg); */
			opacity: 0;
		}
	}

	::view-transition-old(post-hero-image),
	::view-transition-old(post-hero-image):only-child,
	::view-transition-new(post-hero-image),
	::view-transition-new(post-hero-image):only-child {
		/* Use normal blending,
		  so the new view sits on top and obscures the old view */
		mix-blend-mode: normal;
		/* Make the height the same as the group,
		 meaning the view size might not match its aspect-ratio. */
		height: 100%;
		/* Clip any overflow of the view */
		overflow: clip;
	}

	::view-transition-old(post-hero-image),
	::view-transition-new(post-hero-image) {
		/* Prevent the default animation,
		so both views remain opacity:1 throughout the transition */
		/* animation: do-nothing; */
		/* animation: do-nothing !important; */
		animation-duration: 0.5s !important;
	}

	::view-transition-old(post-hero-image):only-child {
		/* Prevent the default animation,
		so both views remain opacity:1 throughout the transition */
		animation: fade-out;
		animation-fill-mode: forwards;
		/* animation-name: scale-out !important; */
		animation-duration: 0.5s !important;
	}

	::view-transition-new(post-hero-image):only-child {
		animation: fade-in;
		animation-fill-mode: forwards;
		/* animation-name: scale-in !important; */
		animation-duration: 0.5s !important;
	}

	/* The old view is the thumbnail */
	::view-transition-old(post-hero-image) {
		/* animation-duration: 0.5s !important; */
		mix-blend-mode: normal !important;
		/* Maintain the aspect ratio of the view,
		  by shrinking it to fit within the bounds of the element */
		object-fit: contain;
	}

	/* The new view is the full image */
	::view-transition-new(post-hero-image) {
		/* animation-duration: 0.5s !important; */
		mix-blend-mode: normal !important;
		/* Maintain the aspect ratio of the view,
	  by growing it to cover the bounds of the element */
		object-fit: cover;
	}
}

/* END View Transitions */

/** Hack for Anker links */
:target::before {
	content: "";
	display: block;
	height: 92px; /* Höhe des Headers */
	margin-top: -92px; /* Gleiche Höhe wie der Header */
}

/* ADMIN BAR */
html {
	margin-top: 0px !important;
	width: 100%;
	overflow-anchor: none;
	/* overflow-x: hidden; */
}

/*
#wpadminbar {
	background: transparent;
}

div#wpadminbar:hover {
	background: #1d2327;
}

li#wp-admin-bar-wp-logo {
	opacity: 1 !important;
	background: #1d2327 !important;
}

li#wp-admin-bar-site-name {
	background: #1d2327 !important;
	opacity: 1 !important;
}

ul#wp-admin-bar-root-default li {
	opacity: 0;
}
ul#wp-admin-bar-root-default:hover li {
	opacity: 1 !important;
} 
*/
body:has(#wpadminbar) {
	padding-top: 32px;
}

/** TODO: here i try to fix the jump on page load while scroll */
/* @media screen and (max-width: 999px) {
	html {
		overflow: hidden;
	}

	body {
		height: 100% !important;
		height: 100vh !important;
		width: 100vw !important;
		position: fixed !important;
		overflow-y: scroll;
		-webkit-overflow-scrolling: touch;
		background: #def;
	}
} */

/* CSS Reset */
*,
body * {
	margin: 0;
	padding: 0;
	box-sizing: border-box; /* Stellt sicher, dass Padding und Border in der Gesamtbreite des Elements enthalten sind */
}

body {
	position: relative;
	max-width: 100vw;

	font-family: "Poppins", sans-serif;
	font-size: 18px;

	overflow-x: hidden;

	--content-width: 1400px;
	--default-block-padding: 4rem; /* applies an ".block" for top and bottom padding */
	--content-padding: 4rem; /* applies for ".block .block-inner" for left and right padding */

	--accent-color: #bf1692;
	--accent-color-secondary: #11114a;
	--accent-color-tertiary: #606081;

	/* New color vars */
	--gray-40: #606081;
	--gray-20: #ebeef1;
	--gray-10: #f3f6fa;
	--brombeer: #bf1692;

	--light-gray: #afafc7;

	--effect-transition-timing-function: cubic-bezier(0.645, 0.045, 0.355, 1);
}

@media screen and (max-width: 699px) {
	body {
		--content-padding: 1rem;
		--default-block-padding: 3rem;
	}
}

body.modal-open {
	overflow: hidden;
	transition: overflow 0s 0.66s ease;
}

.scroll-wrapper {
	min-height: 100vh;

	/* TODO: be carefull */
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}

main {
	max-width: 100vw;
	/* overflow: hidden; */
}

footer {
	bottom: 0;
	position: relative;
	align-self: flex-end;
	width: 100%;
}

img {
	width: 100%;
	height: auto;
	/* this could make problems on some elements */
	display: block;
}

/**
 * Utility Classes 
 */
.grid {
	display: grid;
	gap: 36px;
}

.grid-start {
	align-items: start;
}

/**
 * Fonts 
 */

h1,
h2,
h3,
h4,
h5,
h6 {
	overflow-wrap: break-word;
	white-space: normal;
	word-break: break-word;
	hyphens: auto;
	text-wrap: balance;
}

h1 {
	color: var(--accent-color-secondary);
	font-size: 3.5rem;
	font-weight: 600;
	font-style: normal;
	letter-spacing: 0px;
	text-align: left;
	line-height: 4.5rem;
	padding-bottom: 1rem;
}

h2 {
	font-size: 2rem;
	line-height: 1.5;
	font-weight: 600;
	/* margin-bottom: 40px; */
	margin: 2rem 0 1rem 0;
	color: var(--accent-color-secondary);
}

h3 {
	font-size: 24px;
	font-weight: 500;
	color: var(--accent-color-secondary);
	text-wrap: balance;
	/* padding-top: 1rem; */
	padding-bottom: 1rem;
}
h3:first-child {
	padding-top: 0;
}

h4 {
	color: var(--accent-color-secondary);
	font-size: 1.2rem;
	font-weight: 500;
}

h5 {
	color: var(--accent-color-secondary);
	font-size: 1rem;
	font-weight: 600;
	text-transform: uppercase;
	margin: 2rem 0 1rem 0;
	margin-top: 2rem;
}

h6 {
	/* TODO: in accent colors aufnehmen */
	color: var(--accent-color-secondary);
	font-size: 0.8rem;
	font-weight: 600;
}

/* Tagline, heading und paragraph */
.block-inner p.tagline,
p.tagline {
	color: var(--gray-40);
	/* margin-bottom: 20px; */
	/* font-size: 1.2rem; */
	font-size: 1.5rem;
	font-weight: 500;
}

/* TODO: add additional h tags */
.tagline + h1,
.tagline + h2 {
	margin: 0 0 1rem 0;
}

a {
	color: #000;
	text-decoration: none;
}

p {
	font-size: 18px;
	line-height: 36px;
	margin-bottom: 1rem;
	text-wrap: pretty;
}

a.post-edit-link {
	border: solid 1px var(--gray-40);
	border-radius: 5px;
	padding: 1rem 2rem;
	background: #fff;
	margin-top: 2rem;
	display: block;
}

@media screen and (max-width: 790px) {
	h1 {
		font-size: 2.2rem;
		letter-spacing: 0px;
		text-align: left;
		line-height: 3rem;
		padding-bottom: 1rem;
	}

	.block-inner p.tagline,
	p.tagline {
		padding-bottom: 0;
		margin-bottom: 0.5rem;
		font-size: 1.2rem;
	}
}

svg image {
	transform-origin: center center;
}

/****************/
/* Basic Layout */
/****************/
main.main-content h1:first-child,
main.main-content h2:first-child,
main.main-content h3:first-child,
main.main-content h4:first-child,
main.main-content h5:first-child,
main.main-content h6:first-child {
	margin-top: -10px;
}

.entry-content {
	margin-bottom: 0rem;
}

aside {
	display: block;
	position: sticky;
	height: fit-content;
	padding: 2rem;
	border-radius: 16px;
	border: 1px solid rgba(235, 238, 241, 1);
	box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.05);
	opacity: 1;
	background-color: rgba(255, 255, 255, 1);
}
aside ul,
aside ol {
	margin-left: 1rem;
}
aside ul li,
aside ol li {
	padding-left: 0.5rem;
	margin-bottom: 0.5rem;
	font-size: 1rem;
}

p.gray {
	color: var(--gray-40);
}

.intro *,
p.module-intro,
p.intro {
	padding-bottom: 2rem;
	margin-bottom: 0;
	color: var(--gray-40);
	font-size: 1.2rem;
}

.intro *:last-child {
	padding-bottom: 1rem;
}

@media screen and (max-width: 699px) {
	h2 {
		font-size: 1.5rem;
	}

	p {
		font-size: 1rem;
		line-height: 1.75;
	}
}

/** 
 * Global Stuff 
 */

.block ul {
	margin-left: 20px;
}

/**
 * Modules 
 */

/* global blocks */
.page-builder-output .overflow-wrapper:last-child {
	overflow: hidden;
}

.block {
	max-width: var(--content-width);
	padding-left: var(--content-padding);
	padding-right: var(--content-padding);
	margin: 0 auto;
}

/* Hero block-wrapper */
.block.fullwidth {
	max-width: none;
	padding-left: 0;
	padding-right: 0;
	margin: auto;

	margin: auto;
	overflow: hidden;
}
.block.fullwidth .block-wrapper {
	padding-top: 0;
	overflow: hidden;
}

.overflow-wrapper:first-child .block .block-wrapper {
	padding-top: 0;
	/* margin-top: -1rem; */
}

.block .block-wrapper {
	padding-top: var(--default-block-padding);
	padding-bottom: var(--default-block-padding);
}

.block.top-spacing-none .block-wrapper {
	padding-top: 0;
}
.block.right-spacing-none .block-wrapper {
	padding-right: 0;
}
.block.bottom-spacing-none .block-wrapper {
	padding-bottom: 0;
}
.block.left-spacing-none .block-wrapper {
	padding-left: 0;
}

.block-inner {
	margin: 0 auto;
}

/* fullwidth section */
.block.fullwidth .block-inner {
	padding: 0;
	position: relative;
	overflow: hidden;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
	width: 100%;
	max-width: none;
}
.block.fullwidth .content-wrapper {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	max-width: var(--content-width);
	padding: var(--content-padding);
	max-width: var(--content-width);
	width: 100%;
	box-sizing: border-box;
	z-index: 1;
	/* color: #fff; */
	position: relative;
	margin: auto;

	/* TODO: fullwidth provisorisch */
	/* max-width: calc(100vw - 16px);
	box-sizing: border-box; */
}

/* Text Image Module */
.text-image-block {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 30px;
}

/* Text Content */
.text-content {
	display: flex;
	flex-direction: column;
	justify-content: center;
}

/**
 * for all Grid Items 
 */
.grid-item {
	max-width: 100%;
	hyphens: auto;
	word-wrap: break-word;
	word-break: break-word;
}

/* featureGrid */
.block-inner.feature-grid {
	counter-reset: item-counter; /* Counter initialisieren */
	/* counter-increment: item-counter; */
}
.block-inner.feature-grid .grid .grid-item {
	padding: 1.5rem;
	border-radius: 16px;
	border: 1px solid rgba(235, 238, 241, 1);
	box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.05);
	opacity: 1;
	background-color: rgba(255, 255, 255, 1);

	display: flex;
	flex-direction: column;
	justify-content: flex-start;
	align-items: start;
}

.block-inner.feature-grid .grid .grid-item .icon-wrapper {
	/* padding: 1rem 3rem; */
	/* max-width: calc(132px + 6rem); */

	/* TODO: height rausnehmen: Bild/Icon should have same aspect ratio */
	/* height: 142px; */

	--number-opacity: 0;
	--number-transformation-x: 50px;

	margin: auto;
	text-align: center;
	position: relative;
}

.block-inner.feature-grid .grid .grid-item .icon-wrapper.use-number:before {
	content: "1";
	content: counter(item-counter);

	position: absolute;
	font-size: 5rem;
	line-height: 5rem;
	color: var(--accent-color);
	left: 32%;

	opacity: var(--number-opacity);
	transform: translate(0px, var(--number-transformation-x));

	counter-increment: item-counter;
}

.block-inner.feature-grid h4 {
	padding-top: 20px;
}

.block-inner.feature-grid .grid .grid-item .paragraph {
	flex-grow: 1;
}

.block-inner.feature-grid .grid-item p {
	font-size: 0.9rem;
	padding-top: 20px;
	line-height: 1.75;
}

.block-inner.feature-grid .grid .grid-item .icon-wrapper img {
	max-width: 66%;
	margin: auto;
	padding-bottom: 1rem;
}

/* MODAL */
.modal {
	opacity: 0;
	position: fixed;
	z-index: -1;
	width: 100%;
	max-width: 100vw;
	height: 100vh;
	grid-template-columns: 1fr;
	background: #2e2e2e;
	justify-content: center;
	align-items: center;
	overflow: hidden;
	overflow-y: scroll;

	transition: opacity 0.66s cubic-bezier(0.5, 0.02, 0.5, 1), z-index 0s 0.66s ease;
}

.modal.active {
	overflow: auto;
	overflow-x: hidden;
	overflow-y: scroll;
	opacity: 1 !important;
	z-index: 9999;
	height: 100dvh;
	padding-bottom: 4rem;

	transition: opacity 0.66s cubic-bezier(0.5, 0.02, 0.5, 1), z-index 0s 0s ease;
}

/* TODO: muss wahrscheinlich weg, wenn ELOPAGE iframe reinkommt */
.modal {
	background: #ffffff;
	width: 100vw;
}

.modal:before {
	content: "";
	z-index: 1;
	width: 100vw;
	height: 100%;
	background: aliceblue;
	position: absolute;
	background: radial-gradient(circle at 12% 0%, rgb(191 22 146 / 40%) 0%, #eee0 66%), radial-gradient(circle at 31% 36%, rgb(24 212 242 / 33%) 0%, #eee0 36%);
	opacity: 1;

	transition: opacity 0.66s cubic-bezier(0.5, 0.02, 0.5, 1);
}
.modal.active:before {
	content: "";
	z-index: 1;
	width: 100vw;
	height: 100%;
	background: aliceblue;
	position: fixed;
	/* position: absolute; */
	background: radial-gradient(circle at 12% 0%, rgb(191 22 146 / 40%) 0%, #eee0 66%), radial-gradient(circle at 31% 36%, rgb(24 212 242 / 33%) 0%, #eee0 36%);
	opacity: 1;
}
.modal .close-modal-button svg {
	color: #000 !important;
}
/* TODO: END of new gradiend look */

.modal .modal-header {
	display: flex;
	justify-content: end;
	width: 100%;
	margin: auto;
	padding: 20px 0px;
	position: fixed;
	z-index: 999;
}

.modal .close-modal-button {
	width: calc(var(--content-width) + 80px);
	padding: var(--content-padding);
	padding-bottom: 0;
	margin: auto;
	display: flex;
	justify-content: end;
	cursor: pointer;
	z-index: 99999;
}

.modal .close-modal-button svg {
	color: #fff;
}

/* .modal .modal-wrapper {
	padding: calc(4rem + 70px) 0;
	color: #fff;
} */
.modal .modal-wrapper {
	padding: calc(4rem + 70px) 0;
	color: #fff;
	max-width: 1200px;
	margin: auto;
	z-index: 9;
	position: relative;
	color: #000;
	max-width: var(--content-width);
	padding: var(--content-padding);
	padding-top: calc(4rem + 70px);
}

@media screen and (max-width: 999px) {
	/* Hero */
	.block-inner.hero.background-hero .content-wrapper {
		padding-top: 200px !important;
		padding-bottom: 200px !important;
	}
	.block-inner.hero.background-hero .content-wrapper .headline-wrapper h1 {
		font-size: 3rem !important;
	}

	/* CTA */
	.block-inner.cta-section .content-wrapper {
		grid-template-columns: 1fr !important;
		padding: 1.5rem !important;
	}

	/* Feature Grid */
	.block-inner.feature-grid .grid {
		grid-template-columns: 1fr 1fr !important;
	}

	/* Image Grid */
	.block-inner.image-grid .grid {
		grid-template-columns: 1fr 1fr !important;
	}

	/* Footer */
	footer .footer-body {
		grid-template-columns: 1fr !important;
	}
}

@media screen and (max-width: 699px) {
	/* Hero */
	.block-inner.hero .content-wrapper.video-hero {
		padding-top: 160px !important;
		padding-bottom: 160px !important;
	}
	.block-inner.hero .content-wrapper .headline-wrapper h1 {
		font-size: 2rem !important;
		margin-bottom: 0;
		padding-bottom: 0;
	}

	.block-inner.hero.background-hero .content-wrapper .headline-wrapper h1 {
		font-size: 1.8rem !important;
	}

	/* CTA */
	.block-inner.cta-section .cta-buttons {
		margin-top: 30px;
	}
	.block-inner.cta-section .content {
		display: grid !important;
		grid-template-columns: 1fr !important;
	}

	/* Text-Image */
	.block-inner.text-image-block {
		grid-template-columns: 1fr !important;
	}

	/* Feature Grid */
	.block-inner.feature-grid .grid {
		grid-template-columns: 1fr !important;
	}
	.block-inner.feature-grid .grid .grid-item .icon-wrapper.use-number:before {
		font-size: 64px;
		line-height: 64px;
	}

	/* Image Grid */
	.block-inner.image-grid .grid {
		grid-template-columns: 1fr !important;
	}

	/* Footer */
	footer .footer-body {
		grid-template-columns: 1fr !important;
	}
}

/** Styling ZOHO Forms */
button.zf-submitColor {
	outline: none;
	border: solid 2px var(--accent-color);
	border-radius: 4px;
	width: 100%;
	padding: 1rem;
	margin-top: 2rem;
	background: var(--accent-color);
	color: #fff;
	font-size: 1rem;
	font-family: "Poppins";
	cursor: pointer;

	transition: all 300ms cubic-bezier(0.5, 0.02, 0.5, 1);
}

button.zf-submitColor:hover {
	color: var(--accent-color);
	background: transparent;
}

/***
 * PAGINATION 
 */
.pagination {
	width: 100%;
	display: flex;
	justify-content: center;
	gap: 1rem;
	margin-top: 2rem;
}

.pagination a,
.pagination span {
	font-weight: 500;
}

span.page-numbers.current {
	color: var(--accent-color);
}

.pagination a.prev,
.pagination a.next {
	font-weight: 400;
}

/**
 * Forms
 */

form label p:last-child {
	padding: 0;
	margin: 0;
}
form a {
	text-decoration: underline;
}
.form-flex.checkbox input {
	width: 1rem;
}
@media screen and (max-width: 999px) {
	.zf-templateWrapper input {
		width: 100%;
		height: auto !important;
		padding: 12px !important;
		border-radius: 4px;
		border: solid 2px #ebeef1;
		outline: none;
		font-size: 1rem !important;
	}
}

/** WIKI */
ul.wiki-list {
	margin-left: 0;
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 2rem;
}
ul.wiki-list li.wiki-item {
	list-style: none;
}
ul.wiki-list li.wiki-item a {
	display: block;
	height: 100%;
	padding: 2rem;
	background: var(--gray-10);
	border-radius: 1rem;
}

ul.wiki-list li.wiki-item p.wiki-headline {
	margin-top: 1rem;
	margin-bottom: 0.25rem;
}

/** Custom Post Type Stuff */
.intro {
	margin-top: 1rem;
}

.post-header .categories p {
	pointer-events: none;
	color: var(--gray-40);
	font-size: 22px;
	font-weight: 600;
	padding: 0;
	margin: 0;
}

/*** Blockquote ***/
blockquote {
	margin-top: 72px;
	color: var(--gray-40);
}

blockquote:before {
	content: "";
	height: 56px;
	position: absolute;
	width: 56px;
	background-image: url("data:image/svg+xml,%3Csvg width='55px' height='55px' viewBox='0 0 55 55' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Eicons/quote%3C/title%3E%3Cg id='warum-GL-–-success-story' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='success-story-–-detailpage' transform='translate(-294, -3056)' fill='%23BF1692' fill-rule='nonzero'%3E%3Cg id='section' transform='translate(294, 3056)'%3E%3Cg id='icons/quote' transform='translate(0, 6.0156)'%3E%3Cpath d='M12.8333333,43.828125 C19.1666667,43.828125 24.1666667,38.6224856 24.1666667,32.4093032 C24.1666667,26.1961207 19.8333333,21.8301006 14.3333333,21.8301006 C13.1666667,21.8301006 11.8333333,21.9980244 11.5,22.1659483 C12.5,16.4565374 18.1666667,9.57165948 23.5,6.38110632 L15.5,0 C6.33333333,6.54903017 0,16.9603089 0,28.5470546 C0,38.2866379 6,43.828125 12.8333333,43.828125 Z M43.6666667,43.828125 C50,43.828125 55,38.6224856 55,32.4093032 C55,26.1961207 50.6666667,21.8301006 45.1666667,21.8301006 C44,21.8301006 42.6666667,21.9980244 42.3333333,22.1659483 C43.3333333,16.4565374 49,9.57165948 54.3333333,6.38110632 L46.3333333,0 C37.1666667,6.54903017 30.8333333,16.9603089 30.8333333,28.5470546 C30.8333333,38.2866379 36.8333333,43.828125 43.6666667,43.828125 Z'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
	background-repeat: no-repeat;
	background-size: contain;
	margin-top: -72px;
}

blockquote h2 {
	margin-bottom: 1rem !important;
	margin-top: 1rem !important;
}

blockquote h2,
blockquote h3,
blockquote h4,
blockquote h5,
blockquote h6 {
	margin-bottom: 1rem !important;
	margin-top: 1rem !important;
}

blockquote h2:first-child,
blockquote h3:first-child,
blockquote h4:first-child,
blockquote h5:first-child,
blockquote h6:first-child {
	margin-top: 0 !important;
}

blockquote h2:last-child,
blockquote h3:last-child,
blockquote h4:last-child,
blockquote h5:last-child,
blockquote h6:last-child {
	margin-bottom: 0 !important;
}

/** Flexible Content Items */
.content-item.tinymce-output h2:first-child {
	margin-top: 0;
}

.content-item.tinymce-output p:last-child {
	padding-bottom: 0;
	margin-bottom: 0;
}

.content-item.tinymce-output h1:last-child,
.content-item.tinymce-output h2:last-child,
.content-item.tinymce-output h3:last-child,
.content-item.tinymce-output h4:last-child,
.content-item.tinymce-output h5:last-child,
.content-item.tinymce-output h6:last-child,
.content-item.tinymce-output p:last-child {
	margin: 0;
	padding: 0;
}

table {
	width: 100%;
}

/* ul */
main .tinymce-output ul {
	margin-left: 1rem;
}

main .tinymce-output ul li {
	line-height: 2;
	margin-left: 0.5rem;
	padding-left: 1rem;
	padding-bottom: 1rem;
	position: relative;
}
main .tinymce-output ul.check_bullist li {
	/* Entfernt die Standard-Punkte */
	list-style: none;
}

main .tinymce-output ul.check_bullist li::before {
	content: "";
	position: absolute;
	left: -1.4rem;
	top: 1.2rem;
	transform: translateY(-50%);
	width: 20px;
	height: 20px;
	background-size: contain;
	background-repeat: no-repeat;
	background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><circle cx="10" cy="10" r="10" fill="red"/></svg>'); /* Dein SVG-Code hier */
	background-image: url("data:image/svg+xml;utf8,%3Csvg%20width%3D%2220px%22%20height%3D%2220px%22%20viewBox%3D%220%200%2020%2020%22%20version%3D%221.1%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%3E%3Ctitle%3Eicons%2Fchecker%3C%2Ftitle%3E%3Cg%20id%3D%22wissen---blog%22%20stroke%3D%22none%22%20stroke-width%3D%221%22%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%3E%3Cg%20id%3D%22blog-detail%22%20transform%3D%22translate(-294%2C%20-2959)%22%20fill%3D%22%23BF1692%22%20fill-rule%3D%22nonzero%22%3E%3Cg%20id%3D%22section%22%20transform%3D%22translate(294%2C%20226)%22%3E%3Cg%20id%3D%22item%22%20transform%3D%22translate(0%2C%201912)%22%3E%3Cg%20id%3D%22icons%2Fchecker%22%20transform%3D%22translate(0%2C%20823.1875)%22%3E%3Cpolygon%20points%3D%220%208.52273215%202.85714205%205.68183038%207.14285647%209.94319646%2017.1428577%200%2020%202.84090177%207.14285647%2015.625%22%3E%3C%2Fpolygon%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
}

article .page-builder-output ul.multi-image-list,
article .main-content ul.multi-image-list {
	list-style: none;
	margin-left: 0;
	padding-left: 0;
	position: relative;
	opacity: 0;
	/* padding-top: 4%; */
	/* display: flex; */
	/* align-items: center; */
}

article .page-builder-output ul.multi-image-list.calculated,
article .main-content ul.multi-image-list.calculated {
	opacity: 1;
}

ul.multi-image-list .image-item {
	display: flex;
	align-items: center;
}

@media screen and (max-width: 999px) {
	article .page-builder-output ul.multi-image-list,
	article .main-content ul.multi-image-list {
		padding-top: 0%;
	}
}

article .page-builder-output ul.multi-image-list li,
article .main-content ul.multi-image-list li {
	position: absolute;
	width: 100%;
}

ul.multi-image-list li .image-item svg svg {
	display: block;
}

/** CTA Buttons */
a.cta-button.cta-secondary div {
	background: var(--gray-20);
	color: var(--accent-color-secondary);
	border-color: var(--gray-20);
}
a.cta-button.cta-secondary:hover div {
	background: var(--accent-color-secondary);
	color: #fff;
	border-color: var(--accent-color-secondary);
}

.block a.cta-button {
	margin-top: 0;
	display: inline-block;
	margin-right: 1rem;
	margin-bottom: 1rem;
}
.block a.cta-button {
	margin-top: 0;
	display: inline-block;
}

.block a.cta-button:last-child {
	margin-right: 0;
	margin-bottom: 0;
}

span.cta-content {
	font-size: 1rem;
}

/* CASE -> in purple gradient block */
.block-inner.purple-gradient a.cta-button:hover div {
	color: #fff;
}
.block-inner.purple-gradient a.cta-button.cta-secondary:hover div {
	border-color: var(--gray-20);
}

@media screen and (max-width: 699px) {
	body .block a.cta-button {
		margin-top: 0;
		display: block;
		margin-right: 0;
		margin-bottom: 1rem;
		width: 100%;
	}
	li.menu-item.cta-button a div,
	body a.cta-button div {
		padding: 0.5rem 0.5rem 0.5rem 0.5rem;
		width: 100%;
	}
	span.cta-content {
		font-size: 16px;
	}
}

/*
 * Lightbox CSS 
 * Gilt für Hero lightbox und Image-Grid
 */
/** Lightbox CSS */
.lightbox-modal {
	position: fixed;
	z-index: 9999;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.9);
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 1rem;
}

.lightbox-close {
	position: absolute;
	/* top: 2rem; */
	right: 0px;
	color: white;
	font-size: 2rem;
	cursor: pointer;
}

.lightbox-content {
	/* max-width: 90%; */
	/* max-height: 90%; */
	max-width: var(--content-width);
	padding: var(--content-padding);
	/* padding-top: calc(4rem + 70px); */
	position: relative;
}

.lightbox-content img,
.lightbox-content video {
	width: 100%;
	height: auto;
	display: block;
	max-height: 80vh;
	object-fit: contain;
}

.lightbox-button-container {
	display: flex;
	justify-content: center;
}

.lightbox-button-container button.navigate {
	width: 3rem;
	border-radius: 4px;
	aspect-ratio: 1 / 1;
	margin-top: 1rem;
}

/*
 * 
 * Overflow Wrapper Styles
 * 
 * 
 */
/* Purple Gradient Fullwidth */
.overflow-wrapper.purple-gradient-fullwidth {
	background: linear-gradient(65deg, rgba(97, 19, 107, 1) 0%, rgba(17, 17, 74, 1) 22%, rgba(17, 17, 74, 1) 58%, rgba(151, 21, 129, 1) 100%);
}

.overflow-wrapper.purple-gradient-fullwidth h1,
.overflow-wrapper.purple-gradient-fullwidth h2,
.overflow-wrapper.purple-gradient-fullwidth h3,
.overflow-wrapper.purple-gradient-fullwidth h4,
.overflow-wrapper.purple-gradient-fullwidth h5,
.overflow-wrapper.purple-gradient-fullwidth h6,
.overflow-wrapper.purple-gradient-fullwidth p,
.overflow-wrapper.purple-gradient-fullwidth a,
.overflow-wrapper.purple-gradient-fullwidth label {
	color: #fff;
}

.overflow-wrapper.purple-gradient-fullwidth .single-q-and-a .button-wrapper .expand-icon-elem {
	background: #fff;
}

/* Gray Fullwidth */
.overflow-wrapper.gray-fullwidth {
	background: var(--gray-10);
	margin-top: 4rem;
	margin-bottom: 4rem;
	padding: 4rem 0;
}

/*
 *
 * SLIDER 
 * 
 */

.slider-section .content-wrapper {
	margin-top: 2rem;
}

/**
* Slider styles
*/
.nw-slide {
	/* overflow: hidden; */
	width: 100%;
}

.nw-slide__wrapper {
	cursor: grab;
}

ul.nw-slide__list {
	display: flex;
	position: relative;
	margin-left: 0;

	transition: all 0.66s cubic-bezier(0.77, 0.2, 0.05, 1);
}
/* TODO: list transformation 
ul.nw-slide__list {
    display: flex;
    position: relative;
    transform: translate(calc(-33.333% - 36px), 0px);
}
*/

li.nw-slide__item {
	/* min-width: calc(33.3333% - ((36px * 2) / 3));
    margin-right: 36px; */
	border-radius: 16px;
	list-style: none;
	position: relative;
	cursor: grab;
	user-select: none;
}

.nw-slide__wrapper li.nw-slide__item.has-link {
	cursor: pointer;
}
.nw-slide__wrapper.is-dragging li.nw-slide__item.has-link {
	cursor: grabbing;
}

li.nw-slide__item .item-content {
	pointer-events: none;
	z-index: 1;
	position: relative;
	padding: 0;
	-webkit-user-select: none; /* Chrome, Safari */
	-moz-user-select: none; /* Firefox */
	-ms-user-select: none; /* Internet Explorer/Edge */
	user-select: none; /* Standard syntax */

	padding: 1rem;
	padding-bottom: 0;
}

li.nw-slide__item .item-content img {
	pointer-events: none;
	-webkit-user-select: none; /* Chrome, Safari */
	-moz-user-select: none; /* Firefox */
	-ms-user-select: none; /* Internet Explorer/Edge */
	user-select: none; /* Standard syntax */
	border-radius: 1rem;
}

.nw-slide li.nw-slide__item h6 {
	color: var(--gray-40);
	font-size: 1rem;
	font-weight: 500;
	padding-top: 1rem;
	padding-bottom: 0.5rem;
}

.nw-slide li.nw-slide__item h4 {
	font-size: 1.5rem;
	font-weight: 500;
}

li.nw-slide__item .background {
	background-color: var(--accent-color-secondary);
	position: relative;
	width: 100%;
	height: 60%;
	border-radius: 16px;

	overflow: hidden;
	position: absolute;
	z-index: 1;
}

/* odd */
li.nw-slide__item:nth-child(odd) .background:before {
	content: "";
	background: radial-gradient(circle at 100% 0, rgb(191 22 146 / 66%) 0%, #eee0 66%);
	position: absolute;
	width: 100%;
	height: 100%;
}
/* even */
li.nw-slide__item:nth-child(even) .background:before {
	content: "";
	background: radial-gradient(circle at 100% 0, rgb(39 126 185 / 66%) 0%, #eee0 66%);
	position: absolute;
	width: 100%;
	height: 100%;
}

/* Slider indicator */
.slider-indicator {
	margin-top: 3rem;
	display: flex;
	justify-content: right;
}
.slider-indicator-item {
	width: 30px;
	height: 6px;
	display: block;
	background: #afafc7;
	margin: 0px 1%;
	cursor: pointer;

	/* new style */
	height: 10px;
	border-radius: 4px;

	transition: all 0.66s cubic-bezier(0.77, 0.2, 0.05, 1);
}

.slider-indicator-item.active {
	width: 70px;
	background: var(--accent-color);
}

.slider-indicator-item * {
	pointer-events: none;
}

.slider-indicator-item:first-child {
	margin-left: 0;
}
.slider-indicator-item:last-child {
	margin-right: 0;
}

.nw-slide__cta {
	display: flex;
	justify-content: end;
	width: 100%;
	padding-top: 3rem;
}

jQuery(document).ready(function ($) {
	$(".category-button").on("click", function () {
		// Entferne die 'active'-K<PERSON>e von allen Buttons
		$(".category-button").removeClass("active")

		// Füge die 'active'-Klasse zum geklickten Button hinzu
		$(this).addClass("active")

		var categoryId = $(this).data("category-id") // Hol die Kategorie-ID
		console.log("Kategorie ID:", categoryId) // Debugging
		console.log("ajax_params: ", ajax_params)

		$.ajax({
			url: ajax_params.ajaxurl, // ajaxurl wird von wp_localize_script bereitgestellt
			type: "POST", // Sende die Anfrage als POST
			data: {
				action: "filter_posts_by_category", // Der action-Name, der in WordPress verwendet wird, um die Anfrage zu verarbeiten
				category_id: categoryId !== "all" ? categoryId : null, // Keine Filterung bei "Alle"
				// category_id: categoryId, // Die ID der Kategorie, die gesendet wird
			},
			success: function (response) {
				// Check if the View Transitions API is supported
				if (document.startViewTransition) {
					// Begin the transition
					document.startViewTransition(() => {
						// Replace the content with new filtered posts
						$("#post-list").html(response)
					})
				} else {
					// Fallback if the API is not supported
					$("#post-list").html(response)
				}
			},
			error: function (xhr, status, error) {
				console.error("Status: " + status)
				console.error("Error: " + error)
				console.error(xhr.responseText)
			},
		})
	})
})

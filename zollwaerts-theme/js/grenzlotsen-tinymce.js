;(function () {
	tinymce.create("tinymce.plugins.grenzlotsen_plugin", {
		init: function (editor, url) {
			editor.addButton("grenzlotsen_button", {
				text: "Text-Bild",
				icon: false,
				onclick: function () {
					editor.windowManager.open({
						title: "Text-Bild-Element hinzufügen",
						body: [
							{
								type: "textbox",
								name: "image",
								label: "Bild URL",
							},
							{
								type: "textbox",
								name: "text",
								label: "Text",
								multiline: true,
								minWidth: 300,
								minHeight: 100,
							},
						],
						onsubmit: function (e) {
							editor.insertContent('<div class="text-image-element"><img src="' + e.data.image + '" alt=""/><p>' + e.data.text + "</p></div>")
						},
					})
				},
			})
		},
		createControl: function (n, cm) {
			return null
		},
	})
	tinymce.PluginManager.add("grenzlotsen_button", tinymce.plugins.grenzlotsen_plugin)
})()

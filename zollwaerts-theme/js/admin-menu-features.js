// console.log("admin-menu-features loaded")
// console.log("menuItemsAppereance: " + JSON.stringify(menuItemsAppereance))
// console.log("menuItemsAppereance.id: ", menuItemsAppereance)

jQuery(document).ready(function ($) {
	console.log("menuItemsAppereance: ", menuItemsAppereance)

	$(".menu-item-settings").each(function () {
		console.log(".menu-item-settings: " + this)
		var id = $(this)
			.attr("id")
			.replace(/menu-item-settings-/g, "")

		console.log("id: ", id)
		var appereance = menuItemsAppereance.hasOwnProperty(id) ? menuItemsAppereance[id].appereance : ""
		var useModal = menuItemsAppereance.hasOwnProperty(id) ? menuItemsAppereance[id].use_modal : ""

		console.log("appereance: ", appereance)
		console.log("useModal: ", useModal)

		var appereanceField = $(
			'<p class="field-custom description description-wide"><label for="edit-menu-item-appereance-' +
				id +
				'">Link Style:<br><select id="edit-menu-item-appereance-' +
				id +
				'" class="widefat edit-menu-item-appereance" name="menu-item-appereance[' +
				id +
				']"><option value="default">Default Link</option><option value="cta">CTA Button</option></select></label></p>'
		)

		var useModalField = $(
			'<p class="field-custom description description-wide"><label for="edit-menu-item-use-modal-' +
				id +
				'">Use Modal:<br><input type="checkbox" id="edit-menu-item-use-modal-' +
				id +
				'" class="widefat edit-menu-item-use-modal" name="menu-item-use-modal[' +
				id +
				']" ' +
				(useModal ? "checked" : "") +
				" /></label></p>"
		)

		// Finde das Eingabefeld für den "Angezeigten Namen" und füge die neuen Felder danach ein
		$(this).find(".edit-menu-item-title").closest("p").after(useModalField).after(appereanceField)

		// Setze den gespeicherten Wert als ausgewählt
		appereanceField.find("select").val(appereance).change()
	})

	// Observer if user adds new Buttons
	var menuEditor = document.querySelector("#menu-to-edit") // Ändern Sie dies entsprechend Ihrer Struktur
	if (!menuEditor) return

	var observer = new MutationObserver(function (mutations) {
		mutations.forEach(function (mutation) {
			if (mutation.type === "childList") {
				mutation.addedNodes.forEach(function (addedNode) {
					if (addedNode.classList && addedNode.classList.contains("menu-item")) {
						var id = $(addedNode)
							.attr("id")
							.replace(/menu-item-/g, "")
						var appereanceField = $(
							'<p class="field-custom description description-wide"><label for="edit-menu-item-appereance-' +
								id +
								'">Link Style!:<br><select id="edit-menu-item-appereance-' +
								id +
								'" class="widefat edit-menu-item-appereance" name="menu-item-appereance[' +
								id +
								']"><option value="default">Default Link</option><option value="cta">CTA Button</option></select></label></p>'
						)

						var useModalField = $(
							'<p class="field-custom description description-wide"><label for="edit-menu-item-use-modal-' +
								id +
								'">Use Modal:<br><input type="checkbox" id="edit-menu-item-use-modal-' +
								id +
								'" class="widefat edit-menu-item-use-modal" name="menu-item-use-modal[' +
								id +
								']" /></label></p>'
						)

						$(addedNode).find(".edit-menu-item-title").closest("p").after(useModalField).after(appereanceField)
					}
				})
			}
		})
	})

	// Konfiguration des Observers:
	var config = {childList: true, subtree: true}

	// Übergabe des Zielknotens und der Konfiguration an den Observer
	observer.observe(menuEditor, config)
})

/* Old working für cta apperance */
/*
jQuery(document).ready(function ($) {
	console.log("menuItemsAppereance: ", menuItemsAppereance)

	$(".menu-item-settings").each(function () {
		console.log(".menu-item-settings: " + this)
		var id = $(this)
			.attr("id")
			.replace(/menu-item-settings-/g, "")

		console.log("id: ", id)
		var appereance = menuItemsAppereance.hasOwnProperty(id) ? menuItemsAppereance[id].appereance : ""
		console.log("appereance: ", appereance)

		var appereanceField = $(
			'<p class="field-custom description description-wide"><label for="edit-menu-item-appereance-' +
				id +
				'">Link Style:<br><select id="edit-menu-item-appereance-' +
				id +
				'" class="widefat edit-menu-item-appereance" name="menu-item-appereance[' +
				id +
				']"><option value="default">Default Link</option><option value="cta">CTA Button</option></select></label></p>'
		)

		// Finde das Eingabefeld für den "Angezeigten Namen" und füge das neue Feld danach ein
		$(this).find(".edit-menu-item-title").closest("p").after(appereanceField)

		// Setze den gespeicherten Wert als ausgewählt
		appereanceField.find("select").val(appereance).change()
	})

	// Observer if user adds new Buttons
	var menuEditor = document.querySelector("#menu-to-edit") // Ändern Sie dies entsprechend Ihrer Struktur
	if (!menuEditor) return

	var observer = new MutationObserver(function (mutations) {
		mutations.forEach(function (mutation) {
			if (mutation.type === "childList") {
				mutation.addedNodes.forEach(function (addedNode) {
					if (addedNode.classList && addedNode.classList.contains("menu-item")) {
						var id = $(addedNode)
							.attr("id")
							.replace(/menu-item-/g, "")
						var appereanceField = $(
							'<p class="field-custom description description-wide">' +
								'<label for="edit-menu-item-appereance-' +
								id +
								'">Link Style:<br>' +
								'<select id="edit-menu-item-appereance-' +
								id +
								'" class="widefat edit-menu-item-appereance" name="menu-item-appereance[' +
								id +
								']">' +
								'<option value="default">Default Link</option>' +
								'<option value="cta">CTA Button</option>' +
								"</select></label></p>"
						)
						$(addedNode).find(".edit-menu-item-title").closest("p").after(appereanceField)
					}
				})
			}
		})
	})

	// Konfiguration des Observers:
	var config = {childList: true, subtree: true}

	// Übergabe des Zielknotens und der Konfiguration an den Observer
	observer.observe(menuEditor, config)
})
*/

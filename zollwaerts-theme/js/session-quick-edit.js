;(function ($) {
	$(document).on("click", ".editinline", function () {
		var post_id = $(this).closest("tr").attr("id").replace("post-", "")

		// Global Event
		var global_event = $("#edit-" + post_id).find('input[name="global_event"]')
		if (typeof global_event !== "undefined") {
			var global_event_value = $("#global_event-" + post_id)
				.text()
				.trim()
			if (global_event_value == "Yes") {
				global_event.prop("checked", true)
			} else {
				global_event.prop("checked", false)
			}
		}

		// Kein Link zur Detailseite
		var no_link_to_detail = $("#edit-" + post_id).find('input[name="no_link_to_detail"]')
		if (typeof no_link_to_detail !== "undefined") {
			var no_link_to_detail_value = $("#no_link_to_detail-" + post_id)
				.text()
				.trim()
			if (no_link_to_detail_value == "Yes") {
				no_link_to_detail.prop("checked", true)
			} else {
				no_link_to_detail.prop("checked", false)
			}
		}

		// Partner
		var is_partner = $("#edit-" + post_id).find('input[name="is_partner"]')
		if (typeof is_partner !== "undefined") {
			var is_partner_value = $("#is_partner-" + post_id)
				.text()
				.trim()
			if (is_partner_value == "Yes") {
				is_partner.prop("checked", true)
			} else {
				is_partner.prop("checked", false)
			}
		}
	})
})(jQuery)

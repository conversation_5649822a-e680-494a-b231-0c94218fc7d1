jQuery(document).ready(function ($) {
	$("#publish").click(function (e) {
		e.preventDefault()

		// Post-ID und Metadaten abrufen
		const postId = $("#post_ID").val()
		const locationId = $('input[name="tax_input[session_location][]"]:checked').val()
		const startTime = $('input[name="session_start_time"]').val()
		const endTime = $('input[name="session_end_time"]').val()

		// AJAX-Request zur Validierung senden
		$.ajax({
			url: ajaxurl,
			type: "POST",
			data: {
				action: "validate_session_time_overlap",
				security: ajax_object.security,
				post_id: postId,
				location_id: locationId,
				start_time: startTime,
				end_time: endTime,
			},
			success: function (response) {
				if (response.success) {
					// Wenn keine Überschneidungen gefunden wurden, Beitrag speichern
					$("#publish").off("click").click()
				} else {
					// Fehlermeldung anzeigen
					alert(response.data)
				}
			},
		})
	})
})

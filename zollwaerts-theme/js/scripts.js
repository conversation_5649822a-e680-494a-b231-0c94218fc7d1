// Open modal
// function openModal(modalClass) {
// 	const body = document.body
// 	const modal = document.querySelector("." + modalClass)
// 	if (modal) {
// 		body.classList.add("modal-open")
// 		modal.classList.add("active")
// 	} else {
// 		console.error("Modal mit der Klasse " + modalClass + " wurde nicht gefunden.")
// 	}
// }

// document.querySelectorAll(".open-modal-button").forEach((button) => {
// 	button.addEventListener("click", function () {
// 		const modalClass = this.getAttribute("data-modal-class") // Ang<PERSON>mmen, dass der Button ein data-Attribut mit der Modal-Klasse hat
// 		openModal(modalClass)
// 	})
// })

const allLinks = document.querySelectorAll("a")

allLinks.forEach((link) => {
	if (link.getAttribute("data-modal-class")) {
		link.addEventListener("click", function (event) {
			event.preventDefault() // Verhindert das Springen zum Anker
			const modalId = this.getAttribute("data-modal-class") // Nimmt an, dass der Link ein data-Attribut mit der Modal-Klasse hat
			const modalClass = modalId.substring(1)

			openModal(modalClass)
		})
	}
})

function openModal(modalClass) {
	console.log("openModal called")
	console.log("modalClass", modalClass)
	if (modalClass) {
		console.log(`modal with class ${modalClass} found`)
		const body = document.body
		const modal = document.querySelector(`.${modalClass}`)
		console.log("modal", modal)

		if (modal) {
			console.log("modal found")
			// Custom logic zum Öffnen des Modals
			body.classList.add("modal-open")
			modal.classList.add("active")
		}
	} else {
		console.error("Modal mit der Klasse " + modalClass + " wurde nicht gefunden.")
	}
}

// Close modal
function closeModal(modalClass) {
	const body = document.body
	const modal = document.querySelector("." + modalClass)
	if (modal) {
		body.classList.remove("modal-open")
		modal.classList.remove("active")
	} else {
		console.error("Modal mit der Klasse " + modalClass + " wurde nicht gefunden.")
	}
}
document.querySelectorAll(".close-modal-button").forEach((button) => {
	button.addEventListener("click", function () {
		const modalClass = this.getAttribute("data-modal-class") // Angenommen, dass der Button ein data-Attribut mit der Modal-Klasse hat
		closeModal(modalClass)
	})
})

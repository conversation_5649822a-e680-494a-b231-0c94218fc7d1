<?php get_header(); ?>


<?php
    if (is_preview()) {
        // Überprüfen, ob es eine Revision gibt
        $revision_id = wp_is_post_revision(get_the_ID());

        if ($revision_id) {
            // Hole die Meta-Daten aus der Revision
            $json_data = get_metadata('post', $revision_id, '_page_builder_blocks', true);
        } else {
            // Falls keine Revision vorhanden ist, hole die regulären Meta-Daten
            $json_data = get_post_meta(get_the_ID(), '_page_builder_blocks', true);
        }
    } else {
        // Keine Vorschau, reguläre Meta-Daten holen
        $json_data = get_post_meta(get_the_ID(), '_page_builder_blocks', true);
    }

    /*
    /** OLD AND WORKING BUT NOT WITH PREVIEW */
    /*
    $json_data = get_post_meta(get_the_ID(), '_page_builder_blocks', true);
    */

    // Ver<PERSON><PERSON> von wp_unslash, um unnötige Escape-Zeichen zu entfernen
    $unslashed_data = wp_unslash($json_data);

    // Dekodieren der JSON-Daten in ein PHP-Array
    $page_builder_blocks = json_decode($unslashed_data, true); 
?>

<?php get_template_part('global-modules/page-builder-modals'); ?>
<?php get_template_part('global-modules/global-modals'); ?>


<?php 
// $has_hero_block = false;

// // TODO: if-check if "$has_hero_block" is true
// function enqueue_threejs_scripts() {
//     $script_path = get_template_directory() . '/js/libraries/three.module.min.js';
//     $script_url = get_template_directory_uri() . '/js/libraries/three.module.min.js';
//     // wp_enqueue_script('custom-threejs', get_template_directory_uri() . '/js/custom-threejs.js', array('three-js'), null, true);

//     if (file_exists($script_path)) {
//         wp_enqueue_script('three-js', $script_url, array(), null, true);
//         echo '<script>console.log("threeJS available");</script>';
//     } else {
//         echo '<script>console.error("ThreeJS NOT FOUND");</script>';
//     }
// }
// add_action('wp_enqueue_scripts', 'enqueue_threejs_scripts');
?>

<main>
    <!-- <p style="width: 300px; height: 300px; background: red;">Direkter HTML-Inhalt zum Testen</p> -->

    <?php
    while (have_posts()) : the_post();
        ?>
        <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
            <!-- <header class="entry-header">
                <h1 class="entry-title"><?php the_title(); ?></h1>
            </header> -->
            <div class="entry-content">
                <?php 
                    // the_content(); 
                ?>

                <div class="page-builder-output">
                    <?php include get_template_directory() . '/template-parts/page-builder.php'; ?>         
                </div>

                <?php
                wp_link_pages(array(
                    'before' => '<div class="page-links">' . esc_html__('Pages:', 'zollwaerts-theme'),
                    'after'  => '</div>',
                ));
                ?>


            </div>
        </article>
        <?php
        // Wenn Kommentare erlaubt sind oder wir mindestens einen Kommentar haben, das Kommentar-Template laden
        if (comments_open() || get_comments_number()) :
            comments_template();
        endif;
    endwhile; // Ende der Loop
    ?>
</main>

<?php 
    // $useSidebar = get_field('use_sidebar');
    $useSidebar = false;
    if($useSidebar) {
        get_sidebar(); 
    }
?>

<?php get_footer(); ?>

</div>




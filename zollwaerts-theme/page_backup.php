<?php get_header(); ?>

<?php

    // useSidebar
    $useSidebar = false;

    // Holen der gespeicherten JSON-Daten aus den Post-Metadaten
    $json_data = get_post_meta(get_the_ID(), '_page_builder_blocks', true);

    // <PERSON><PERSON><PERSON><PERSON> von wp_unslash, um unnötige Escape-Zeichen zu entfernen
    $unslashed_data = wp_unslash($json_data);

    // Dekodieren der JSON-Daten in ein PHP-Array
    $page_builder_blocks = json_decode($unslashed_data, true); 

    if ($page_builder_blocks && is_array($page_builder_blocks)) {
        foreach ($page_builder_blocks as $block) {
            if( $block['type'] === 'modal') {
                // echo '<div style="border: solid red 2px;">';
                // echo htmlspecialchars(json_encode($block));
                // echo '</div>';

                echo '<div class="modal ' . $block['id'] . '" style="opacity: 0;">';
                    // Modal Header
                    echo '<div class="modal-header">';
                    echo '<div class="close-modal-button" data-modal-class="' . $block['id'] . '">
                                <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18 17.94 6M18 18 6.06 6"/>
                                </svg>
                            </div>';
                    echo '</div>';
                    // echo '<h1> es gibt ein Modal mit der ID: ' . $block['id'] . '</h1>';

                    // Modal COntent
                    echo '<div class="modal-wrapper">' . $block['content'] . '</div>';
                    // set_query_var( 'block', $block );
                    // get_template_part( 'template-parts/block', $block_type );
                echo '</div>';
            }
        
        } 
    } else {
        echo '<div> NO BLOCKS </div>';
    }


    // Helper functions
    function find_form_by_id($theme_settings_array, $id) {
    if (isset($theme_settings_array['htmlForms'])) {
        foreach ($theme_settings_array['htmlForms'] as $form) {
            if ($form['id'] == $id) {
                return $form;
            }
        }
    }
    return null; // ID nicht gefunden
}
?>



<?php 
// $has_hero_block = false;

// // TODO: if-check if "$has_hero_block" is true
// function enqueue_threejs_scripts() {
//     $script_path = get_template_directory() . '/js/libraries/three.module.min.js';
//     $script_url = get_template_directory_uri() . '/js/libraries/three.module.min.js';
//     // wp_enqueue_script('custom-threejs', get_template_directory_uri() . '/js/custom-threejs.js', array('three-js'), null, true);

//     if (file_exists($script_path)) {
//         wp_enqueue_script('three-js', $script_url, array(), null, true);
//         echo '<script>console.log("threeJS available");</script>';
//     } else {
//         echo '<script>console.error("ThreeJS NOT FOUND");</script>';
//     }
// }
// add_action('wp_enqueue_scripts', 'enqueue_threejs_scripts');
?>

<main>
    <!-- <p style="width: 300px; height: 300px; background: red;">Direkter HTML-Inhalt zum Testen</p> -->
    <?php
    while (have_posts()) : the_post();
        ?>
        <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
            <!-- <header class="entry-header">
                <h1 class="entry-title"><?php the_title(); ?></h1>
            </header> -->
            <div class="entry-content">
                <?php the_content(); ?>

                <div class="page-builder-output">
                    <!-- Meta Boxes Output -->
                    <?php
                        // TODO: Das gibt es aktuell noch in der DB, wird aber im Page Builder nicht verwendet 
                        // if ($mein_text_input) {
                        //     echo '<div class="mein-text-input">' . wp_kses_post($mein_text_input) . '</div>';
                        // }

                        // Page Builder Blocks Output
                        /* Original Code der, abgesehen von den "n" bei Umbrüchen, funktionierte
                        $page_builder_blocks = get_post_meta( get_the_ID(), '_page_builder_blocks', true );
                        */

                        if ($page_builder_blocks && is_array($page_builder_blocks)) {
                            foreach ($page_builder_blocks as $block) {
                                // echo '<div style="border: solid red 2px;">';
                                // echo htmlspecialchars(json_encode($block));
                                // echo '</div>';

                                switch ( $block['type'] ) {
                                    case 'hero':
                                        $block_type = 'hero';
                                        $has_hero_block = true;
                                        break;
                                    case 'text-image':
                                        $block_type = 'text-image';
                                        break;
                                    case 'featureGrid':  // Dein neuer Blocktyp für featureGrid
                                        $block_type = 'featureGrid';
                                        break;
                                    case 'imageGrid':  // Dein neuer Blocktyp für imageGrid
                                        $block_type = 'imageGrid';
                                        break;
                                    case 'cta-section':  // Dein neuer Blocktyp für cta-section
                                        $block_type = 'cta-section';
                                        break;
                                    case 'slider-section':  // Dein neuer Blocktyp für slider-section
                                        $block_type = 'slider-section';
                                        break;
                                    case 'sticky-text-image':  // Dein neuer Blocktyp für sticky-text-image
                                        $block_type = 'sticky-text-image';
                                        break;
                                    case 'pricing-table':  // Dein neuer Blocktyp für pricing-table
                                        $block_type = 'pricing-table';
                                        break;
                                    case 'modal':  // Dein neuer Blocktyp für modal
                                        $block_type = 'modal';
                                        break;
                                    case 'raw-html':  // Dein neuer Blocktyp für HTML-Inhalte
                                        $block_type = 'raw-html';
                                        break;
                                    case 'headline':
                                        $block_type = 'headline';
                                        break;
                                    case 'paragraph':
                                        $block_type = 'paragraph';
                                        break;
                                    default:
                                        $block_type = '';
                                        break;
                                }
                                if ( $block_type ) {
                                    set_query_var( 'block', $block );

                                    // TODO: make actual fullwidth settings 
                                    // $is_fullwidth = $block['advancedSettings']['fullwidth'];
                                    $fullwidth_types = ['hero', 'slider-section'];
                                    $is_fullwidth = in_array($block_type, $fullwidth_types) ? 'fullwidth' : '';

                                    // echo '<p>' . $block_type . ' ' . $is_fullwidth . '</p>';


                                    $spacing_top = $block['advancedSettings']['spacing']['top'];
                                    $spacing_right = $block['advancedSettings']['spacing']['right'];
                                    $spacing_bottom = $block['advancedSettings']['spacing']['bottom'];
                                    $spacing_left = $block['advancedSettings']['spacing']['left'];
                                    
                                    $custom_spacing_top = $block['advancedSettings']['spacing']['topCustom'];
                                    $custom_spacing_right = $block['advancedSettings']['spacing']['rightCustom'];
                                    $custom_spacing_bottom = $block['advancedSettings']['spacing']['bottomCustom'];
                                    $custom_spacing_left = $block['advancedSettings']['spacing']['leftCustom'];

                                    $isSpacingTopCustom = $spacing_top === 'custom';
                                    $isSpacingRightCustom = $spacing_right === 'custom';
                                    $isSpacingBottomCustom = $spacing_bottom === 'custom';
                                    $isSpacingLeftCustom = $spacing_bottom === 'custom';

                                    $spacing_styles = '';
                                    if ($isSpacingTopCustom) {
                                        $spacing_styles .= "padding-top: {$custom_spacing_top}px; ";
                                    }
                                    if ($isSpacingRightCustom) {
                                        $spacing_styles .= "padding-right: {$custom_spacing_right}px; ";
                                    }
                                    if ($isSpacingBottomCustom) {
                                        $spacing_styles .= "padding-bottom: {$custom_spacing_bottom}px; ";
                                    }
                                    if ($isSpacingLeftCustom) {
                                        $spacing_styles .= "padding-left: {$custom_spacing_left}px; ";
                                    }
                                    ?>

                                    <div 
                                        class="block <?php echo $is_fullwidth . ' ' . 'top-spacing-' . $spacing_top ?> <?php echo 'right-spacing-' . $spacing_bottom ?> <?php echo 'bottom-spacing-' . $spacing_bottom ?> <?php echo 'left-spacing-' . $spacing_left ?>" 
                                    >
                                        <div class="block-wrapper" style="<?php echo esc_attr($spacing_styles); ?>">
                                            <?php get_template_part( 'template-parts/block', $block_type ); ?>
                                        </div>
                                    </div>
                                    <?php
                                }
                            
                            } 
                        } else {
                            echo '<div> NO BLOCKS </div>';
                        }
                    ?>



                    <!-- New building blocks output -->
                    
                    <?php
                        if ($page_builder_blocks && is_array($page_builder_blocks)) {
                            foreach ($page_builder_blocks as $block) {
                                switch ( $block['type'] ) {
                                    case 'row':
                                        $block_type = 'row';
                                        // $has_hero_block = true;
                                        break;
                                }
                                if ( $block_type === 'row' ) {
                                    // set_query_var( 'block', $block );
                                    // echo '<div style="border: solid red 2px;">';
                                    // echo htmlspecialchars(json_encode($block));
                                    // echo '</div>';

                                    if ($block['cols'] && is_array($block['cols'])) {
                                        foreach ($block['cols'] as $col) {

                                            // echo '<div style="border: solid red 2px;">';
                                            // echo htmlspecialchars(json_encode($col));
                                            // echo '</div>';

                                            foreach ($col['content'] as $content) {
                                                echo '<div style="border: solid red 2px;">';
                                                echo htmlspecialchars(json_encode($content));
                                                echo '</div>';

                                                switch ( $content['type'] ) {
                                                    case 'hero':
                                                        $content_type = 'hero';
                                                        $has_hero_content = true;
                                                        break;
                                                    case 'text-image':
                                                        $content_type = 'text-image';
                                                        break;
                                                    case 'featureGrid':  // Dein neuer contenttyp für featureGrid
                                                        $content_type = 'featureGrid';
                                                        break;
                                                    case 'imageGrid':  // Dein neuer contenttyp für imageGrid
                                                        $content_type = 'imageGrid';
                                                        break;
                                                    case 'cta-section':  // Dein neuer contenttyp für cta-section
                                                        $content_type = 'cta-section';
                                                        break;
                                                    case 'slider-section':  // Dein neuer contenttyp für slider-section
                                                        $content_type = 'slider-section';
                                                        break;
                                                    case 'sticky-text-image':  // Dein neuer contenttyp für sticky-text-image
                                                        $content_type = 'sticky-text-image';
                                                        break;
                                                    case 'pricing-table':  // Dein neuer contenttyp für pricing-table
                                                        $content_type = 'pricing-table';
                                                        break;
                                                    case 'modal':  // Dein neuer contenttyp für modal
                                                        $content_type = 'modal';
                                                        break;
                                                    case 'raw-html':  // Dein neuer contenttyp für HTML-Inhalte
                                                        $content_type = 'raw-html';
                                                        break;
                                                    case 'headline':
                                                        $content_type = 'headline';
                                                        break;
                                                    case 'paragraph':
                                                        $content_type = 'paragraph';
                                                        break;
                                                    default:
                                                        $content_type = '';
                                                        break;
                                                }
                                                if ( $content_type ) {
                                                    set_query_var( 'block', $content );

                                                    // TODO: make actual fullwidth settings 
                                                    // $is_fullwidth = $block['advancedSettings']['fullwidth'];
                                                    $fullwidth_types = ['hero', 'slider-section'];
                                                    $is_fullwidth = in_array($content_type, $fullwidth_types) ? 'fullwidth' : '';

                                                    // echo '<p>' . $content_type . ' ' . $is_fullwidth . '</p>';


                                                    $spacing_top = $block['advancedSettings']['spacing']['top'];
                                                    $spacing_right = $block['advancedSettings']['spacing']['right'];
                                                    $spacing_bottom = $block['advancedSettings']['spacing']['bottom'];
                                                    $spacing_left = $block['advancedSettings']['spacing']['left'];
                                                    
                                                    $custom_spacing_top = $block['advancedSettings']['spacing']['topCustom'];
                                                    $custom_spacing_right = $block['advancedSettings']['spacing']['rightCustom'];
                                                    $custom_spacing_bottom = $block['advancedSettings']['spacing']['bottomCustom'];
                                                    $custom_spacing_left = $block['advancedSettings']['spacing']['leftCustom'];

                                                    $isSpacingTopCustom = $spacing_top === 'custom';
                                                    $isSpacingRightCustom = $spacing_right === 'custom';
                                                    $isSpacingBottomCustom = $spacing_bottom === 'custom';
                                                    $isSpacingLeftCustom = $spacing_bottom === 'custom';

                                                    $spacing_styles = '';
                                                    if ($isSpacingTopCustom) {
                                                        $spacing_styles .= "padding-top: {$custom_spacing_top}px; ";
                                                    }
                                                    if ($isSpacingRightCustom) {
                                                        $spacing_styles .= "padding-right: {$custom_spacing_right}px; ";
                                                    }
                                                    if ($isSpacingBottomCustom) {
                                                        $spacing_styles .= "padding-bottom: {$custom_spacing_bottom}px; ";
                                                    }
                                                    if ($isSpacingLeftCustom) {
                                                        $spacing_styles .= "padding-left: {$custom_spacing_left}px; ";
                                                    }
                                                    ?>

                                                    <div 
                                                        class="block <?php echo $is_fullwidth . ' ' . 'top-spacing-' . $spacing_top ?> <?php echo 'right-spacing-' . $spacing_bottom ?> <?php echo 'bottom-spacing-' . $spacing_bottom ?> <?php echo 'left-spacing-' . $spacing_left ?>" 
                                                    >
                                                        <div class="block-wrapper" style="<?php echo esc_attr($spacing_styles); ?>">
                                                            <?php get_template_part( 'template-parts/block', $content_type ); ?>
                                                        </div>
                                                    </div>
                                                    <?php
                                                }


                                            }

                                            /*
                                                
                                            */
                                    
                                        } 
                                    }
                                }
                            
                            } 
                        } 
                    ?>
                
                </div>

                
                <!-- <section>
                    <div class="block fullwidth"> 
                        <div class="block-wrapper">
                            <div class="content-wrapper">
                                <div class="nw-slide" data-slide-max-slides="3" data-slide-gap="36px">
                                    <div class="nw-slide__wrapper">
                                        <ul class="nw-slide__list">
                                            <li class="nw-slide__item">
                                                <div class="background"></div>
                                                <div class="item-content">
                                                    <img src="/wp-content/uploads/2024/06/Vortrag-1-scaled.jpeg">
                                                    <h6>Specialist Export Control</h6>
                                                    <h4>whatever 1</h4>
                                                </div>
                                            </li>
                                            <li class="nw-slide__item">
                                                <div class="background"></div>
                                                <div class="item-content">
                                                    <img src="/wp-content/uploads/2024/06/Vortrag-1-scaled.jpeg">
                                                    <h6>Specialist Export Control</h6>
                                                    <h4>whatever 1</h4>
                                                </div>
                                            </li>
                                            <li class="nw-slide__item">
                                                <div class="background"></div>
                                                <div class="item-content">
                                                    <img src="/wp-content/uploads/2024/06/Vortrag-1-scaled.jpeg">
                                                    <h6>Specialist Export Control</h6>
                                                    <h4>whatever 1</h4>
                                                </div>
                                            </li>
                                            <li class="nw-slide__item">
                                                <div class="background"></div>
                                                <div class="item-content">
                                                    <img src="/wp-content/uploads/2024/06/Vortrag-1-scaled.jpeg">
                                                    <h6>Specialist Export Control</h6>
                                                    <h4>whatever 1</h4>
                                                </div>
                                            </li>
                                            <li class="nw-slide__item">
                                                <div class="background"></div>
                                                <div class="item-content">
                                                    <img src="/wp-content/uploads/2024/06/Vortrag-1-scaled.jpeg">
                                                    <h6>Specialist Export Control</h6>
                                                    <h4>whatever 1</h4>
                                                </div>
                                            </li>
                                            <li class="nw-slide__item">
                                                <div class="background"></div>
                                                <div class="item-content">
                                                    <img src="/wp-content/uploads/2024/06/Vortrag-1-scaled.jpeg">
                                                    <h6>Specialist Export Control</h6>
                                                    <h4>whatever 1</h4>
                                                </div>
                                            </li>
                                            <li class="nw-slide__item">
                                                <div class="background"></div>
                                                <div class="item-content">
                                                    <img src="/wp-content/uploads/2024/06/Vortrag-1-scaled.jpeg">
                                                    <h6>Specialist Export Control</h6>
                                                    <h4>whatever 1</h4>
                                                </div>
                                            </li>
                                        </ul>
                                </div>
                            </div>
                        </div>
                </section> -->
                

                <!-- <script>
                function splitValueUnit(value) {
                    const regex = /^(\d+)(\D+)$/;
                    const match = value.match(regex);
                    
                    if (match) {
                        return {
                            number: parseFloat(match[1]),
                            unit: match[2]
                        };
                    }
                    
                    throw new Error("Invalid format");
                }

                document.addEventListener('DOMContentLoaded', function() {
                    const nwSlide = document.querySelectorAll('.nw-slide');

                    nwSlide.forEach(slide => {
                        const nwSlideWrapper = slide.querySelector('.nw-slide__wrapper');
                        const nwSlideList = slide.querySelector('.nw-slide__list');
                        const nwSlideItem = slide.querySelectorAll('.nw-slide__item');

                        let currentSlide = 0;
                        let maxSlides = nwSlideItem.length;
                        
                        // Function to get the current max slides in view based on screen width
                        function getMaxSlidesInView() {
                            const mobileMaxSlidesInView = 1;
                            const desktopMaxSlidesInView = parseInt(slide.getAttribute('data-slide-max-slides') || '1');
                            return window.innerWidth <= 699 ? mobileMaxSlidesInView : desktopMaxSlidesInView;
                        }

                        let maxSlidesInView = getMaxSlidesInView();
                        let gapCount = maxSlidesInView - 1;
                        let slideGapAttribute = slide.getAttribute('data-slide-gap');

                        // split number and unit
                        let slideGap = splitValueUnit(slideGapAttribute);

                        // Calc slider width based on maxSlidesInView and gap
                        let slideFraction = (100 / maxSlidesInView) 
                        let sliderWrapperWidth = nwSlideWrapper.offsetWidth;
                        console.log('%c sliderWrapperWidth: ', 'background: #222; color: #bada55;', sliderWrapperWidth)
                        let slideFractionInPx = sliderWrapperWidth / maxSlidesInView
                        let minusSliderWidth = ((slideGap.number * gapCount) / maxSlidesInView); 
                        let sliderWidth = `calc(${slideFraction}% - ${minusSliderWidth}px)`
                        console.log('`${slideGap.number}${slideGap.unit}`: ', `${slideGap.number}${slideGap.unit}`)

                        let currentTransformX = 0

                        nwSlideItem.forEach(slideItem => {
                            slideItem.style.minWidth = sliderWidth;
                            slideItem.style.marginRight = `${slideGap.number}${slideGap.unit}`;
                        })

                        const sliderIndicator = document.createElement('div');
                        sliderIndicator.classList.add('slider-indicator');

                        // Helper funktion to set the active indicator
                        function setIndicatorActive() {
                            sliderIndicatorItems.forEach((item, i) => {
                                if(i === currentSlide) {
                                    item.classList.add('active'); 
                                } else {
                                    item.classList.remove('active');
                                }
                            });
                        }

                        let arrayOfSlideValues = [];
                        // Create indicators with eventlisteners 
                        for (let i = 0; i <= maxSlides - maxSlidesInView; i++) {
                            const indicatorItem = document.createElement('div');
                            indicatorItem.classList.add('slider-indicator-item');
                            indicatorItem.setAttribute('data-slide-index', i);

                            let minusSliderWidth = ((slideGap.number * gapCount) / maxSlidesInView); 
                            // check if mobile or desktop
                            let minusTransformWidth = window.innerWidth <= 699 ? 36 : (((slideGap.number * gapCount) / maxSlidesInView) / 2); 
                            let tempCurrentTransformX = (-slideFractionInPx * i) - (minusTransformWidth * i);
                            arrayOfSlideValues.push(tempCurrentTransformX)

                            indicatorItem.addEventListener('click', function() {
                                currentSlide = i;
                                console.log('current slide = ', currentSlide);

                                setIndicatorActive();

                                nwSlideList.style.transform = `translate(${arrayOfSlideValues[i]}px, 0px)`;
                                currentTransformX = arrayOfSlideValues[i]
                                console.log('currentTransformX: ', currentTransformX)
                            })
                            sliderIndicator.appendChild(indicatorItem);
                        }
                        slide.appendChild(sliderIndicator);

                        let sliderIndicatorItems = sliderIndicator.querySelectorAll('.slider-indicator-item');
                        setIndicatorActive();

                        console.log('arrayOfSlideValues: ', arrayOfSlideValues);

                        // Dragging
                        let isDragging = false
                        let difference = 0 
                        let newTransform = 0

                        let startX = 0
                        let whileX = 0
                        let endX = 0
                        // function for dragging with snap to position 
                        function dragStart(e) {
                            isDragging = true

                            // get mouse x and y position
                            // startX = e.clientX;
                            startX = e.type === 'touchstart' ? e.touches[0].clientX : e.clientX;
                            console.log('startX = ', startX);
                            
                        }

                        function dragging(e) {
                            if(!isDragging) return;


                            console.log('currentTransformX: ', currentTransformX);

                            // get mouse x and y position
                            // whileX = e.clientX;
                            whileX = e.type === 'touchmove' ? e.touches[0].clientX : e.clientX;

                            difference = startX - whileX;
                            console.log('difference from dragging = ', difference);
                            newTransform = currentTransformX - difference
                            console.log('newTransform from dragging: ', newTransform);

                            nwSlideList.style.transition = 'none'
                            
                            nwSlideList.style.transform = `translate(${newTransform}px, 0px)`

                            // TODO: while dragging nach links und rechts auf die hälfte eines slider items begrenzen 

                        }

                        function dragEnd(e) {
                            if(!isDragging) return;
                            isDragging = false

                            // get mouse x and y position
                            // endX = e.clientX;
                            endX = e.type === 'touchend' ? e.changedTouches[0].clientX : e.clientX;

                            // deactivate transition inline style to enable default transition 
                            nwSlideList.style.transition = ''

                            let maxSliderPositionFromArray = arrayOfSlideValues.length
                            // console.log('maxSliderPositionFromArray: ', maxSliderPositionFromArray);

                            // Get the with of one slider item
                            let snapFraction = arrayOfSlideValues[1]


                            // TODO: Wenn gedraggt wird und man in eine richtug zieht sollte die Slide tendenziell eher in die richtung wechseln in die man zieht 
                            // Sprich Math round sollte nicht bei 0.5 sondern bei 0.25 oder 0.75 reagieren 

                            // Get the current slider snapped position
                            currentSlide = Math.round(newTransform / snapFraction)
                            // console.log('currentSlide from dragEnd: ', currentSlide);

                            // Begrenze currentSlide auf den Bereich von 0 bis maxSliderPositionFromArray
                            currentSlide = Math.max(0, Math.min(currentSlide, maxSliderPositionFromArray - 1));

                            // calculate the final transform value
                            finalTransformValue = snapFraction * currentSlide 

                            // set the final transform value
                            nwSlideList.style.transform = `translate(${finalTransformValue}px, 0px)`

                            // reset values for next dragging event
                            currentTransformX = finalTransformValue
                            newTransform = 0
                            
                            // set indicator 
                            setIndicatorActive();
                        }

                        
                        // from rr
                        nwSlideWrapper.addEventListener('mousedown', dragStart);
                        nwSlideWrapper.addEventListener('touchstart', dragStart);

                        document.body.addEventListener('mousemove', dragging);
                        document.body.addEventListener('touchmove', dragging);

                        // nwSlideWrapper.addEventListener('mouseup', dragEnd);
                        document.body.addEventListener('mouseup', dragEnd);
                        nwSlideWrapper.addEventListener('touchend', dragEnd);
                        // nwSlideList.addEventListener('mouseleave', dragEnd); // Optional: Maus verlässt den slider
                    })   
                })
                </script>

                <style>
                .nw-slide {
                    /* overflow: hidden; */
                    width: 100%;
                }

                ul.nw-slide__list {
                    display: flex;
                    position: relative;
                    margin-left: 0;

                    transition: all 0.66s cubic-bezier(0.77,0.2,0.05,1.0);
                }
                /* TODO: list transformation 
                ul.nw-slide__list {
                    display: flex;
                    position: relative;
                    transform: translate(calc(-33.333% - 36px), 0px);
                }
                */

                li.nw-slide__item {
                    /* min-width: calc(33.3333% - ((36px * 2) / 3));
                    margin-right: 36px; */
                    border-radius: 16px;
                    list-style: none;
                    position: relative;
                    cursor: grab;
                }
                
                li.nw-slide__item .item-content {
                    pointer-events: none;
                    z-index: 1;
                    position: relative;
                    padding: 2rem;
                }

                li.nw-slide__item .item-content h6 {
                    padding-top: 1rem;
                }

                li.nw-slide__item .background {
                    background-color: var(--accent-color-secondary);
                    position: relative;
                    width: 100%;
                    height: 60%;
                    border-radius: 16px;

                    overflow: hidden;
                    position: absolute;
                    z-index: 1;
                }

                /* odd */
                li.nw-slide__item:nth-child(odd) .background:before {
                    content: '';
                    background: radial-gradient(circle at 100% 0, rgb(191 22 146 / 66%) 0%, #eee0 66%);
                    position: absolute;
                    width: 100%;
                    height: 100%;
                }
                /* even */ 
                li.nw-slide__item:nth-child(even) .background:before {
                    content: '';
                    background: radial-gradient(circle at 100% 0, rgb(39 126 185 / 66%) 0%, #eee0 66%);
                    position: absolute;
                    width: 100%;
                    height: 100%;
                }


                /* Slider indicator */ 
                .slider-indicator {
                    margin-top: 30px;
                    display: flex;
                    justify-content: center;
                }
                .slider-indicator-item {
                    width: 30px;
                    height: 6px;
                    display: block;
                    background: #AFAFC7;
                    margin: 0px 1%;
                    cursor: pointer;

                    transition: all 0.66s cubic-bezier(0.77,0.2,0.05,1.0);
                }

                .slider-indicator-item.active {
                    width: 70px;
                    background: var(--accent-color);

                }

                .slider-indicator-item * { 
                    pointer-events: none;
                }
                </style> -->

                <!-- ??? -->
                <?php
                wp_link_pages(array(
                    'before' => '<div class="page-links">' . esc_html__('Pages:', 'mein-leeres-theme'),
                    'after'  => '</div>',
                ));
                ?>
            </div>
        </article>
        <?php
        // Wenn Kommentare erlaubt sind oder wir mindestens einen Kommentar haben, das Kommentar-Template laden
        if (comments_open() || get_comments_number()) :
            comments_template();
        endif;
    endwhile; // Ende der Loop
    ?>
</main>

<?php 
    if($useSidebar) {
        get_sidebar(); 
    }
?>

<?php get_footer(); ?>

</div>




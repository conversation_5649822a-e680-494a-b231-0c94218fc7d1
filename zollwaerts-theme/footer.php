<footer>
    <div class="footer-outer">
        <div class="footer-inner">
            <div class="footer-header">
                <h4>Zollwärts</h4>
                <p>Ein Event der Grenzlotsen GmbH</p>
            </div>

            <div class="footer-body">
                <div class="footer-column">
                    <p><b>Zollwärts</b></p>
                    <p>17. - 19. September 2025</p>

                    <a href="#nw-5m0j8do0r" data-modal-class="#nw-5m0j8do0r" class="cta-button">
                        <div>
                            Ticket sichern
                            <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 12H5m14 0-4 4m4-4-4-4"></path>
                            </svg>
                        </div>
                    </a>
                </div>

                <div class="footer-column">
                    <p><b>Rechtliches</b></p>
                    <a href="https://www.grenzlotsen.de/impressum/" target="_blank">
                        <p>Impressum</p>
                    </a>
                    <a href="https://www.grenzlotsen.de/datenschutz/" target="_blank">
                        <p>Datenschutz</p>
                    </a>
                    <a href="https://www.grenzlotsen.de/agb/" target="_blank">
                        <p>AGB</p>
                    </a>
                </div>

                <div class="footer-column">
                    <p><b>Social Media</b></p>

                    <a href="https://www.facebook.com/grenzlotsen.de?locale=de_DE" target="_blank">
                        <p>Facebook</p>
                    </a>
                    <a href="" target="_blank">
                        <p>LinkedIn</p>
                    </a>
                    
                    <a href="" target="_blank">
                        <p>XING</p>
                    </a>

                    <a href="" target="_blank">
                        <p>Instagram</p>
                    </a>
                    </div>
                
                <div class="footer-column">
                    <p><b>Verpasse keine Event-Updates</b></p>
                    <p>Melde dich für unseren Event Newsletter an, um als Erster alle Infos zum Zollwärts Event 2025 zu erhalten</p>

                    <a href="nw-gnc2ntrfn" data-modal-class="#nw-gnc2ntrfn" class="cta-button">
                        <div>
                            Jetzt abbonieren
                            <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 12H5m14 0-4 4m4-4-4-4"></path>
                            </svg>
                        </div>
                    </a>
                </div>
            </div>

            <div class="footer-footer">
                <p>Grenzlotsen</p>
                <p>© <?php echo date('Y'); ?> <?php bloginfo('name'); ?> ALLE RECHTE VORBEHALTEN</p>
            </div>
        </div>
    </div>
</footer>

<style>

    footer {
        /* background-color: var(--color-black);
        color: var(--color-white);
        padding: 2rem 0; */
    }

    footer h1,
    footer h2,
    footer h3,
    footer h4,
    footer h5,
    footer h6 {
        color: #fff;
        font-size: 2rem;
    }

    .footer-outer {
        background-color: var(--accent-color-secondary);
        padding: 2rem 0;
        color: #fff;
        font-size: 14px;
    }

    .footer-inner {
        max-width: var(--content-width);
        padding-left: var(--content-padding);
        padding-right: var(--content-padding);
        margin: 0 auto;
    }

    footer .footer-header {
        padding-bottom: 3rem;
    }

    footer .footer-body {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        /* gap: 2rem; */
        gap: 4rem;
        padding-bottom: 3rem;
    }

    footer .footer-footer {
        border-top: solid 1px var(--light-gray);
        padding-top: 1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    @media screen and (max-width: 1199px) {
        footer .footer-body {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            /* gap: 2rem; */
            gap: 4rem;
            padding-bottom: 3rem;

            .footer-column:nth-child(4) {
                order: 1;
            }

            .footer-column:nth-child(2) {
                order: 4;
            }

            .footer-column:nth-child(3) {
                order: 3;
            }
        }
    }
    @media screen and (max-width: 999px) {
        footer .footer-footer {
            justify-content: space-between;
            align-items: start;
            flex-direction: column;
        }
    }


    /* Elements insier Footer */
    footer .footer-outer p {
        font-size: 1rem;
        line-height: 1.5;
    }

    footer a {
        color: #fff;
        cursor: pointer;
    }
    footer a:hover {
        color: var(--accent-color);
    }
    footer a.cta-button div {
        margin-top: 1rem;
    }
</style>

<?php wp_footer(); ?>

<!-- TODO: USE LOCAL FILES -->
<!-- lenis -->
<script src="https://unpkg.com/lenis@1.1.1/dist/lenis.min.js"></script> 
<!-- GSAP Core -->
<script src="https://cdn.jsdelivr.net/npm/gsap@3.12.5/dist/gsap.min.js"></script>
<!-- ScrollTrigger -->
<script src="https://cdn.jsdelivr.net/npm/gsap@3.12.5/dist/ScrollTrigger.min.js"></script>

<style>
    html.lenis, html.lenis body {
        height: auto;
    }

    .lenis.lenis-smooth {
        scroll-behavior: auto !important;
    }

    .lenis.lenis-smooth [data-lenis-prevent] {
        overscroll-behavior: contain;
    }

    .lenis.lenis-stopped {
        overflow: hidden;
    }

    .lenis.lenis-smooth iframe {
        pointer-events: none;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {

        let alreadyInitializedOtherScripts = false;

        // Auswahl aller Multi-Image-Listen mit der Klasse ".multi-image-list"
        const multiImageLists = document.querySelectorAll('.multi-image-list');

        // Funktion zur Rotation eines Punktes
        function rotatePoint(x, y, cx, cy, radians) {
            const newX = cx + (x - cx) * Math.cos(radians) - (y - cy) * Math.sin(radians);
            const newY = cy + (x - cx) * Math.sin(radians) + (y - cy) * Math.cos(radians);
            return { x: newX, y: newY };
        }

        // Funktion zur Berechnung der Extrempunkte eines Pfades nach der Rotation
        function calculateExtremePoints(points, rotationDegrees, cx, cy, scaleX, scaleY) {
            const radians = rotationDegrees * (Math.PI / 180);

            // Berechnung der Extrempunkte nach Rotation
            let minX = Infinity;
            let maxX = -Infinity;
            let minY = Infinity;
            let maxY = -Infinity;

            points.forEach(point => {
                const rotatedPoint = rotatePoint(point.x * scaleX, point.y * scaleY, cx, cy, radians);
                if (rotatedPoint.x < minX) {
                    minX = rotatedPoint.x;
                }
                if (rotatedPoint.x > maxX) {
                    maxX = rotatedPoint.x;
                }
                if (rotatedPoint.y < minY) {
                    minY = rotatedPoint.y;
                }
                if (rotatedPoint.y > maxY) {
                    maxY = rotatedPoint.y;
                }
            });

            return { minY, maxY, height: maxY - minY, width: maxX - minX };
        }

        // Funktion zur Berechnung der Höhe aller Multi-Image-Listen
        function calculateMultiImageListHeights() {
            multiImageLists.forEach(multiImageList => {
                let highestValue = 0;

                // Auswahl aller SVG-Elemente innerhalb der aktuellen Multi-Image-Liste
                const svgElements = multiImageList.querySelectorAll('.image-mask-svg');

                svgElements.forEach(svgElement => {
                    const pathElement = svgElement.querySelector('mask path');
                    if (!pathElement) return;

                    // Extrahieren der Pfaddaten
                    const pathLength = pathElement.getTotalLength();
                    const points = [];

                    // Aufteilen des Pfades in Punkte
                    for (let i = 0; i <= pathLength; i += pathLength / 100) { // 100 Punkte für Genauigkeit
                        const point = pathElement.getPointAtLength(i);
                        points.push({ x: point.x, y: point.y });
                    }

                    // Winkel der Rotation aus data Attribut
                    const rotationDegrees = parseInt(svgElement.getAttribute('data-rotation-degrees')) || 0;

                    // Berechnung des Skalierungsfaktors basierend auf der tatsächlichen Größe des SVG-Elements
                    const svgWidth = svgElement.clientWidth;
                    const svgHeight = svgElement.clientHeight;
                    const viewBox = svgElement.viewBox.baseVal;
                    const scaleX = svgWidth / viewBox.width;
                    const scaleY = svgHeight / viewBox.height;

                    // Berechnung des Mittelpunkts der Bounding Box
                    const boundingBox = svgElement.getBBox();
                    const cx = boundingBox.x + boundingBox.width / 2;
                    const cy = boundingBox.y + boundingBox.height / 2;

                    // Berechnung der Extrempunkte
                    const extremes = calculateExtremePoints(points, rotationDegrees, cx, cy, scaleX, scaleY);
                    console.log("Tatsächliche Höhe nach Rotation für SVG:", extremes.height);
                    // give parent element height
                    // parent of svgElement
                    const parent = svgElement.parentElement;
                    parent.style.height = `${extremes.height}px`;

                    // get margin top from svg in px (value is in percentage)
                    const marginTop = parseFloat(window.getComputedStyle(svgElement).marginTop)
                    const topDistance = marginTop ? marginTop : 0 
                    console.log('topDistance', topDistance);
                    // const realTopDistance = topDistance / 100 * svgElement.clientHeight;
                    // console.log('topDistance', topDistance, 'realTopDistance', realTopDistance);
                    // Höchste Höhe in der aktuellen Multi-Image-Liste ermitteln
                    if (extremes.height + topDistance > highestValue) {
                        highestValue = extremes.height + topDistance;
                    }
                });

                // Setze die Höhe der Multi-Image-Liste basierend auf der höchsten Höhe
                multiImageList.style.height = `${highestValue}px`;
                multiImageList.classList.add('calculated')
            });

            if(!alreadyInitializedOtherScripts) {
                initAllSliders();
                initAllVideoLightboxes();
                initAllAccordions();
                initLenis();
                initGSAP();

                alreadyInitializedOtherScripts = true
            }
            console.log('%c Multi-Image-Liste wurde berechnet', 'color: #00ff00; font-size: 32px; font-weight: bold;');
        }

        // Initiale Berechnung der Höhen
        calculateMultiImageListHeights();

        // Debounced Resize Event Listener
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                calculateMultiImageListHeights();
            }, 25); // 25ms debounce Zeit
        });





        /**
            * Multi-Image-List
            */
        /*
        // Initialisiere das Array für die gespeicherten Referenzen
        let imageLists = [];

        // Funktion zum Setzen der maximalen Höhe des Wrappers
        const setMultiImageListHeight = (parent, children) => {
            let maxHeight = 0;
            children.forEach(child => {
                // Extrahiere die Höhe des Elements
                const rect = child.getBoundingClientRect();
                // console.log('rect: ', rect);
                const absoluteHeight = rect.height;
                const absoluteTop = parseFloat(child.style.top) ? parseFloat(child.style.top) : 0;

                // Die Gesamtposition ergibt sich aus der Höhe plus der top-Position
                const totalHeight = absoluteHeight + absoluteTop;

                if (totalHeight > maxHeight) {
                    maxHeight = totalHeight;
                }
            });

            // console.log('maxHeight: ', maxHeight);

            // Setze die Höhe des Wrappers auf die maximal berechnete Höhe
            parent.style.height = `${maxHeight}px`;
        };

        // Initiale Funktion zum Speichern der Referenzen und Berechnen der Höhe
        const initializeImageLists = () => {
            // Hol dir alle Multi-Image-Listen und speichere sie in einem Array
            imageLists = Array.from(document.querySelectorAll('.multi-image-list')).map(singleImageList => {
                const singleImageListItems = singleImageList.querySelectorAll('.image-item');
                // console.log('singleImageList', singleImageList);
                // console.log('singleImageListItems', singleImageListItems);

                // Setze die initiale Höhe
                setMultiImageListHeight(singleImageList, singleImageListItems);

                // Speichere die Referenz für später
                return {
                    wrapper: singleImageList,
                    items: singleImageListItems
                };
            });
        };

        // Debounced Resize-Funktion
        let resizeTimeout;
        const debounceResize = () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                // Verwende die gespeicherten Referenzen, um die Höhe neu zu berechnen
                imageLists.forEach(({ wrapper, items }) => {
                    setMultiImageListHeight(wrapper, items);
                });
            }, 25); // Wartezeit, um die Anzahl der Funktionsaufrufe zu verringern
        };

        // Initialisiere die Referenzen, sobald das DOM geladen ist
        initializeImageLists();

        // Event Listener für Fenster-Resize hinzufügen
        window.addEventListener('resize', debounceResize);
        */
    });

    function isMobile() {
        const userAgent = navigator.userAgent || navigator.vendor || window.opera;
        return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent.toLowerCase());
    }


    /*****
     * GSAP Script 
     */
    function initGSAP() {
        
        // GSAP Scroll trigger plugin
        gsap.registerPlugin(ScrollTrigger)

        ScrollTrigger.config({ 
            refreshPriority: 1 ,
            autoRefreshEvents: ''
        });

        function isMobile() {
            const userAgent = navigator.userAgent || navigator.vendor || window.opera;
            return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent.toLowerCase());
        }



        const scrub = !isMobile();


        /**
         * Hero animation (only basic hero)
         */

        const basicHeros = [...document.querySelectorAll('.block-inner.hero .content-wrapper.basic-hero')]; // .basic-hero
        console.log('basicHeros: ', basicHeros);

        basicHeros.forEach(hero => {
            
            const leftBubblePaths = [...hero.querySelectorAll('.left-side .left-bubble svg path.path-anim')];
            console.log('leftBubblePaths: ', leftBubblePaths);

            leftBubblePaths.forEach((path, index) => {
                
                const pathTo = path.dataset.pathTo;
                console.log('pathTo: ', pathTo);

                gsap.fromTo(path, {
                    // rotation: -30, // Rotation startet bei 180 Grad
                    transformOrigin: "50% 50%", // Drehpunkt auf 50% 50%
                    // y: 32,
                }, {
                    ease: 'power1.out',
                    delay: 0.2 * index,
                    duration: 1 - (0.2 * index), // Dauer der Animation
                    // actual animation
                    // rotation: 0, // Rotation endet bei 0 Grad
                    // y: 0,
                    attr: { d: pathTo },
                })
            });

            /*
            const rightBubbleSVGs =[...hero.querySelectorAll('.right-side .stretch-right .right-bubble svg')];
            console.log('rightBubbleSVGs: ', rightBubbleSVGs);

            rightBubbleSVGs.forEach((path, index) => {
                gsap.fromTo(path, {
                    opacity: 0,
                    y: 18,
                }, {
                    ease: 'power1.out',
                    delay: 0.2 * index,
                    duration: 1 - (0.2 * index), // Dauer der Animation
                    // actual animation
                    opacity: 1,
                    y: 0,
                })
            });
            */


            const rightBubblePaths = [...hero.querySelectorAll('.right-side .stretch-right .right-bubble svg path.path-anim')];
            // console.log('rightBubblePaths: ', rightBubblePaths);

            
            rightBubblePaths.forEach((path, index) => {
                
                const pathTo = path.dataset.pathTo;
                // console.log('pathTo: ', pathTo);

                gsap.fromTo(path, {
                    // rotation: -30, // Rotation startet bei 180 Grad
                    transformOrigin: "50% 50%", // Drehpunkt auf 50% 50%
                    // y: 32,
                }, {
                    ease: 'power1.out',
                    delay: 0.2 + 0.2 * index,
                    duration: 1 - (0.2 * index), // Dauer der Animation
                    // actual animation
                    // rotation: 0, // Rotation endet bei 0 Grad
                    // y: 0,
                    attr: { d: pathTo },
                })
            });
        })


        /**
         * Flexible Content Module
         */
        const flexibleContentModules = [...document.querySelectorAll('.flexible-content')];

        flexibleContentModules.forEach((module, moduleIndex) => {
            // console.log(`Module ${moduleIndex} initialisiert`);

            const imageMasks = [...module.querySelectorAll('.image-mask-svg.animation')];
            // console.log('imageMasks gefunden: ', imageMasks.length);
            
            imageMasks.forEach((imageMask, index) => {
                // console.log('`#${imageMask.id}`: ', `#${imageMask.id}`);
                // console.log(`imageMask ${index} in Module ${moduleIndex}:`, imageMask);

                const path = imageMask.querySelector('path');
                // console.log('path: ', path);
                const pathTo = path.dataset.pathTo;

                gsap.timeline({
                    scrollTrigger: {
                        // trigger: `#${imageMask.id}`, // Element, das den ScrollTrigger auslöst
                        trigger: module, // Element, das den ScrollTrigger auslöst
                        start: "top 100%",
                        end: "bottom 100%",
                        toggleActions: "play none none none",
                        scrub: 1,
                        // markers: true,
                    }
                })
                .fromTo(path, {
                    // rotation: -60,
                    transformOrigin: "50% 50%",
                }, {
                    ease: 'power1.out',
                    // rotation: 0,
                    attr: { d: pathTo }
                });
            });
        });

        /** 
         * Feature Grid animation 
         */ 
        
            // Get all feature grid blocks
        const featureGrids = [...document.querySelectorAll('.block-inner.feature-grid')];
        
        // TODO: das hier müsste noch auf ein resize reagieren 
        if (!isMobile() && window.innerWidth > 999) {
            featureGrids.forEach(grid => {

                const scrubValue = true; 
                const duration = 1;

                // Var for pseudo-bg position animation (before)
                gsap.timeline({
                    scrollTrigger: {
                        trigger: grid,
                        start: "top bottom",
                        // end: `bottom bottom`,
                        end: `bottom 90%`,
                        scrub: scrubValue,
                        // snap: true,
                    }
                })
                .fromTo(grid, {
                    css: {
                        '--bg-one-opacity': 0,
                        '--bg-one-transformation-size': '16%',
                        '--bg-one-transformation-x': '50%'
                    }
                }, {
                    ease: 'power1.out',
                    css: {
                        '--bg-one-opacity': 0.66,
                        '--bg-one-transformation-size': '30%',
                        '--bg-one-transformation-x': '34%'
                    }
                });
                
                const heading = grid.querySelector('.block-inner.feature-grid h2');
                const introParagraph = grid.querySelector('.block-inner.feature-grid .module-intro');
                const featureGridItems = [...grid.querySelectorAll('.block-inner.feature-grid .grid-item')];
                const paths = [...grid.querySelectorAll('.grid-item path.path-anim')];
                const iconWrappers = [...grid.querySelectorAll('.icon-wrapper')];

                // h2 animation
                gsap.timeline({
                    scrollTrigger: {
                        trigger: grid,
                        start: "top bottom",
                        // end: `bottom bottom`,
                        end: `bottom 90%`,
                        scrub: false,
                    }
                })
                .fromTo(heading, {
                    opacity: 0,
                    y: 50,
                }, {
                    duration: duration,
                    ease: 'power1.out',
                    opacity: 1,
                    y: 0,
                });

                // Intro Paragraph animation
                if(introParagraph) {
                    gsap.timeline({
                        scrollTrigger: {
                            trigger: grid,
                            start: "top bottom",
                            // end: `bottom bottom`,
                            end: `bottom 90%`,
                            scrub: false,
                        }
                    })
                    .fromTo(introParagraph, {
                        opacity: 0,
                        y: 50,
                    }, {
                        duration: duration,
                        ease: 'power1.out',
                        opacity: 1,
                        y: 0,
                    });
                }

                
                featureGridItems.forEach((item, index) => {
                    gsap.timeline({
                        scrollTrigger: {
                            trigger: grid, // featureGridItems[0],
                            start: "top bottom",
                            // end: `bottom bottom`,
                            end: `bottom 90%`,
                            scrub: scrubValue,
                        }
                    })
                    .fromTo(item, {
                        opacity: 1,
                        y: 50 +  (index * 50),
                    }, {
                        duration: duration,
                        ease: 'power1.out',
                        opacity: 1,
                        y: 0,
                    });
                })

                // desktop paths
                paths.forEach(el => {
                    const gridItem = el.closest('.grid-item');
                    const pathTo = el.dataset.pathTo;

                    gsap.timeline({
                        scrollTrigger: {
                            trigger: grid, // gridItem,
                            start: "top bottom",
                            // end: `bottom bottom`,
                            end: `bottom 90%`,
                            scrub: scrubValue,
                        }
                    })
                    .fromTo(el, {
                    }, {
                        duration: duration,
                        ease: 'power1.out',
                        attr: { d: pathTo },
                        opacity: .2 
                    });
                });
                
                
                iconWrappers.forEach(iconWrapper => {
                    const gridItem = iconWrapper.closest('.grid-item');
                    const pathTo = iconWrapper.dataset.pathTo;

                    gsap.timeline({
                        scrollTrigger: {
                            trigger: grid, // gridItem,
                            start: "top bottom",
                            // end: `bottom bottom`,
                            end: `bottom 90%`,
                            scrub: scrubValue,
                        }
                    })
                    .fromTo(iconWrapper, {
                        // opacity: 0, 
                        css: {
                            '--number-opacity': 0,
                            '--number-transformation-x': '30px'
                        }
                    }, {
                        duration: duration,
                        ease: 'power1.out',
                        css: {
                            '--number-opacity': 1,
                            '--number-transformation-x': '-10px'
                        },
                        // opacity: 1,
                    });
                });
            });
        } else {
            featureGrids.forEach(grid => {
                const featureGridItems = [...grid.querySelectorAll('.block-inner.feature-grid .grid-item')];
                const paths = [...grid.querySelectorAll('.grid-item path.path-anim')];
                const iconWrappers = [...grid.querySelectorAll('.icon-wrapper')];

                paths.forEach(el => {
                    const gridItem = el.closest('.grid-item');
                    const pathTo = el.dataset.pathTo;
                    el.setAttribute('d', pathTo);
                });

                iconWrappers.forEach(iconWrapper => {
                    iconWrapper.style.setProperty('--number-opacity', '1');
                    iconWrapper.style.setProperty('--number-transformation-x', '-10px');
                });

            });
        }


            /**
             * Sticky Text Images animation
             */
            console.log('%c stickyTextImages', 'color: #f00; font-size: 2em; font-weight: bold;');
            const stickyTextsWrapper = document.querySelectorAll('.sticky-text-image .sticky-text .single-text-wrapper');
            console.log('stickyTextsWrapper: ', stickyTextsWrapper)
            const stickyTexts = document.querySelectorAll('.sticky-text-image .sticky-text .single-text-wrapper .single-text');
            console.log('stickyTexts: ', stickyTexts)

            const imageWrappers = document.querySelectorAll('.sticky-image .single-image-wrapper');

            const popupPaths = [...document.querySelectorAll('.sticky-text-image .sticky-image .single-image path.popup-anim')];
            // console.log('popupPaths: ', popupPaths)
            // mobile Paths 
            const mobilePopupPaths = [...document.querySelectorAll('.sticky-text .single-image path.popup-anim-mobile')]
            // console.log('mobilePopupPaths: ', mobilePopupPaths)

            const popupMoodPaths = [...document.querySelectorAll('.sticky-text-image .sticky-image .single-image path.popup-anim-mood')];
            // mobile paths
            const mobilePopupMoodPaths = [...document.querySelectorAll('.sticky-text .single-image path.popup-anim-mood-mobile')];
            // console.log('mobilePopupMoodPaths: ', mobilePopupMoodPaths)
            
            const stickyTextblockBgWrapper = document.querySelector('.sticky-text-image .block-bg-wrapper');
            const stickyTextBackgroundElements = [...document.querySelectorAll('.block-bg-wrapper .bg-elem ')];
            const stickyTextBackgroundPaths = [...document.querySelectorAll('.block-bg-wrapper .bg-elem .background-path')];



            // GSAP Scroll trigger for stickyTextBackgroundElements
            stickyTextBackgroundElements.forEach((el, index) => {
                const pathTo = stickyTextBackgroundPaths[index]?.dataset?.pathTo;
                const defaultPath = stickyTextBackgroundPaths[index]?.dataset?.defaultPath; 

                const stickyTextBackgroundParralax = gsap.timeline({
                    scrollTrigger: {
                        trigger: stickyTextsWrapper[index],
                        start: "top 100%",
                        end: "bottom 0%",
                        scrub: true, 
                        toggleActions: "play none none none", //
                        markers: false, 
                    }
                })
                .fromTo(el, {
                    y: -250,
                }, {
                    ease: 'power1.out',
                    y: 125,
                })
            })

            stickyTextBackgroundPaths.forEach((el, index) => {
                console.log()
                
                const pathTo = el.dataset.pathTo;
                const defaultPath = el.dataset.defaultPath;

                const stickyTextBackgroundParralax = gsap.timeline({
                    scrollTrigger: {
                        trigger: stickyTextsWrapper[index],
                        start: "top 50%", 
                        end: "top 0%",   
                        scrub: false, 
                        toggleActions: "play reverse play reverse",
                        markers: false, 
                    }
                })
                .fromTo(el, {
                    attr: { d: defaultPath },
                }, {
                    ease: 'power1.out',
                    attr: { d: pathTo },
                })  
            })


            stickyTexts.forEach((el, index) => {
                console.log('%c timeline fade in', 'color: green; font-weight: bold;');
                const singleImageWrapper = imageWrappers[index];
                console.log('singleImageWrapper: ', singleImageWrapper);
                const pathParentSvg = popupPaths[index].closest('svg');
                console.log('pathParentSvg: ', pathParentSvg);
                console.log(`stickyTexts: ${index} -> ${el}`);

                
                const timelineFadeIn = gsap.timeline({
                    scrollTrigger: {
                        trigger: stickyTextsWrapper[index], // stickyTexts[index],
                        start: "top 50%",  // Wenn der obere Rand des Elements 75% vom des Viewports ist
                        // end: "center 25%",       // Ende, wenn der obere Rand des Elements am 25% des Viewports ist
                        toggleClass: {targets: [el, singleImageWrapper, pathParentSvg], className: "active"}, // Fügt die Klasse 'active' hinzu oder entfernt sie
                        scrub: false, // sync animation with scroll position 
                        // scrub: true, // sync animation with scroll position 
                        // snap: 1 / (stickyTexts.length - 1),
                        // snap: snapValue, 
                        // snap: 0.5,
                        toggleActions: "play none none none", //  // Aktionen beim Vorwärts- und Rückwärts-Scrollen
                        markers: false, // Zeigt Scroll-Marker zur Visualisierung (optional)
                    },
                })
            });


            stickyTexts.forEach((el, index) => {
                console.log('%c timeline fade out', 'color: #a00; font-weight: bold;');
                const singleImageWrapper = imageWrappers[index];
                console.log('singleImageWrapper: ', singleImageWrapper);
                const pathParentSvg = popupPaths[index].closest('svg');
                console.log('pathParentSvg: ', pathParentSvg);
                console.log(`stickyTexts: ${index} -> ${el}`);

                const timelineFadeOut = gsap.timeline({
                    scrollTrigger: {
                        trigger: stickyTextsWrapper[index], // stickyTexts[index],
                        start: "top 0% ", 
                        // end: "center -100%",       // Ende, wenn der obere Rand des Elements am 25% des Viewports ist
                        toggleClass: {targets: [el, singleImageWrapper, pathParentSvg], className: "fade-out"}, // Fügt die Klasse 'active' hinzu oder entfernt sie
                        scrub: false, // sync animation with scroll position 
                        // scrub: 1, // sync animation with scroll position 
                        // snap: (1 / (stickyTexts.length)) - 0.125,
                        // toggleActions: onEnter, onLeave, onEnterBack, onLeaveBack
                        toggleActions: "play none none none", //  // Aktionen beim Vorwärts- und Rückwärts-Scrollen
                        markers: false // Zeigt Scroll-Marker zur Visualisierung (optional)
                    },
                });
            })


            // popupMoodPaths (desktop)
            popupMoodPaths.forEach((el, index) => {
                const pathTo = el.dataset.pathTo;
                const defaultPath = el.dataset.defaultPath;

                gsap.timeline({
                    scrollTrigger: {
                        trigger: stickyTextsWrapper[index], 
                        start: "top 50% ", 
                        end: "top 0% ", 
                        toggleActions: "play reverse play reverse",
                        scrub: false,
                        markers: false,
                    }
                })
                .fromTo(el, {
                    // attr: { d: index === 0 ? pathTo : defaultPath },
                    attr: { d: defaultPath },
                }, {
                    ease: 'power1.out',
                    attr: { d: pathTo },
                });
            });

            // mobile
            mobilePopupMoodPaths.forEach((el, index) => {
                // console.log('el mobile mood: ', el);
                const pathTo = el.dataset.pathTo;
                // console.log('pathTo: ', pathTo);
                const defaultPath = el.dataset.defaultPath;
                // console.log('defaultPath: ', defaultPath)

                const imageWrapper = el.closest('.single-image ');

                gsap.timeline({
                    scrollTrigger: {
                        trigger: imageWrapper, // stickyTextsWrapper[index], 
                        start: "center bottom", 
                        end: "center top", 
                        toggleActions: "play reverse play reverse",
                        scrub: false,
                        markers: false,
                    }
                })
                .fromTo(el, {
                    // attr: { d: index === 0 ? pathTo : defaultPath },
                    attr: { d: defaultPath },
                }, {
                    ease: 'power1.out',
                    attr: { d: pathTo },
                });
            });




            // FadeIn popupPaths
            // TODO: Code vereinfachen indem ich nur eine Timeline nutze und die "start" und "bottom" Werte in der forEach Schleife mit if abfragen setze
            popupPaths.forEach((el, index) => {
                // console.log('popup Paths -> el: ', el)
                const pathTo = el.dataset.pathTo;
                const defaultPath = el.dataset.defaultPath;

                if(index === 0) {
                    // console.log('index: ', index)
                    // console.log('popupPaths[0]: ', popupPaths[0])
                    // console.log('stickyTextsWrapper[index]: ', stickyTextsWrapper[index])
                    gsap.fromTo(popupPaths[0],
                        { attr: { d: defaultPath } },
                        {
                            attr: { d: pathTo },
                            scrollTrigger: {
                            trigger: stickyTextsWrapper[index],
                            start: "top 150%",
                            end: "top 0%",
                            scrub: false,
                            // toggleActions: onEnter, onLeave, onEnterBack, onLeaveBack
                            toggleActions: "play reverse play reverse"
                            }
                        }
                    );
                }
                
                if(index !== 0 && index !== popupPaths.length - 1) {
                    // console.log('index: ', index)
                    gsap.fromTo(popupPaths[index],
                        { attr: { d: defaultPath } },
                        {
                            attr: { d: pathTo },
                            scrollTrigger: {
                            trigger: stickyTextsWrapper[index],
                            start: "top 50%",
                            end: "top 0%",
                            scrub: false,
                            // toggleActions: onEnter, onLeave, onEnterBack, onLeaveBack
                            toggleActions: "play reverse play reverse"
                            }
                        }
                    );
                }
                
                if(index === popupPaths.length - 1) {
                    // console.log('index from last child: ', index)
                    // console.log('popupPaths.length: ', popupPaths.length)
                    gsap.fromTo(popupPaths[index],
                        { attr: { d: defaultPath } },
                        {
                            attr: { d: pathTo },
                            scrollTrigger: {
                            trigger: stickyTextsWrapper[index],
                            start: "top 50%",
                            end: "top -150%",
                            scrub: false,
                            // toggleActions: onEnter, onLeave, onEnterBack, onLeaveBack
                            toggleActions: "play reverse play reverse"
                            }
                        }
                    );
                }
            });

            
            // FadeIn mobilePopupPaths
            mobilePopupPaths.forEach((el, index) => {
                // console.log('mobile popup Paths -> el: ', el)
                const pathTo = el.dataset.pathTo;
                // console.log('pathTo: ', pathTo);
                const defaultPath = el.dataset.defaultPath;
                // console.log('defaultPath: ', defaultPath);

                const imageWrapper = el.closest('.single-image ');

                gsap.fromTo(el,
                    { attr: { d: defaultPath } },
                    {
                        attr: { d: pathTo },
                        scrollTrigger: {
                            trigger: imageWrapper,
                            start: "center 100%",
                            end: "center 0%",
                            scrub: false,
                            // toggleActions: onEnter, onLeave, onEnterBack, onLeaveBack
                            toggleActions: "play reverse play reverse",
                            // toggleActions: "play pause pause pause",
                            markers: false
                        }
                    }
                );
            });
            
            
            // function handleScroll() {
            //     console.log('handle scroll');
            //     mobilePopupPaths.forEach((el, index) => {
            //         const pathTo = el.dataset.pathTo;
            //         const defaultPath = el.dataset.defaultPath;
            //         const imageWrapper = el.closest('.single-image');

            //         const imageWrapperTop = imageWrapper.getBoundingClientRect().top;
            //         const imageWrapperHeight = imageWrapper.offsetHeight;
            //         const windowHeight = window.innerHeight;

            //         const scrollProgress = (windowHeight - imageWrapperTop) / (windowHeight + imageWrapperHeight);

            //         if (scrollProgress >= 0 && scrollProgress <= 1) {
            //         const progress = scrollProgress;
            //         const path = pathTo.replace(/\s/g, '').split(',');
            //         const defaultPathArray = defaultPath.replace(/\s/g, '').split(',');

            //         const interpolatedPath = path.map((segment, i) => {
            //             const defaultSegment = defaultPathArray[i];
            //             const toValue = parseFloat(segment);
            //             const fromValue = parseFloat(defaultSegment);
            //             return fromValue + (toValue - fromValue) * progress;
            //         });

            //         const interpolatedPathString = `M${interpolatedPath.join(',')}`;
            //         el.setAttribute('d', interpolatedPathString);
            //         } else if (scrollProgress < 0) {
            //         el.setAttribute('d', defaultPath);
            //         } else {
            //         el.setAttribute('d', pathTo);
            //         }
            //     });
            // }

            // console.log('init handle Scroll')
            // window.addEventListener('scroll', handleScroll);
            


                /*
                

                const popupPathAnimation = gsap.timeline({
                    scrollTrigger: {
                        trigger: stickyTextsWrapper[index], 
                        start: "top 50% ",
                        end: "top 0%", 
                        toggleActions: "play reverse play reverse",
                        scrub: false,
                        // markers: {
                        //     indent: 150,
                        //     startColor: "#73b740",
                        //     endColor: "#73b740",
                        // },
                        // onLeave: () => {
                        //     if (index !== 0) return;
                        //     console.log('first Element has left: ', index);
                        //     hasPassed = true;
                        //     popupPathAnimation.restart();
                        // },
                        // onLeaveBack: () => {
                        //     if (index !== 1) return;
                        //     console.log('Element entered again', index);
                        //     hasPassed = false;
                        //     popupPathAnimation.restart();
                        //     // el.setAttribute('d', defaultPath);
                        // },
                    },
                    onUpdate: () => {
                        if (index !== 0) return;

                        const rect = stickyTextsWrapper[index].getBoundingClientRect();
                        const top = rect.top;

                        if (top <= 0 && !hasPassed.value) {
                            hasPassed.value = true;
                            console.log('element has passed');
                            popupPathAnimation.invalidate().restart();
                        } else if (top > 0 && hasPassed.value) {
                            hasPassed.value = false;
                            console.log('element has not passed');
                            popupPathAnimation.invalidate().restart();
                        }
                    },
                })
                popupPathAnimation.fromTo(el, {
                    // attr: { d: index !== 0 ? defaultPath : pathTo },
                    // attr: { d: index !== 0 ? el.dataset.defaultPath : hasPassed ? el.dataset.defaultPath : el.dataset.pathTo },
                    attr: { d: () => {
                            console.log('hasPassed: ', hasPassed);
                            return index !== 0 ? defaultPath : hasPassed.value ? defaultPath : pathTo 
                        }
                    },
                    // attr: { d: defaultPath },
                }, {
                    ease: 'power1.out',
                    attr: { d: pathTo },
                });
                */
        // }

    }
        

        
    /**
     * Basic Scroll events 
     */
    // console.log('Lenis: ', lenis);

    /** 
     * LENIS
     */

    function initLenis() {
        
        const lenis = new Lenis({
            wrapper: document.querySelector('[data-scroll-container]'),
            // content: document.querySelector('main'),
        })

        lenis.on('scroll', (e) => {
            // console.log(e.animate.to)

            if (e.animate.to > 0 && !header.classList.contains('fixed')) {
                header.classList.add('fixed');
                // console.log('fix added')
            } 
            
            if(e.animate.to <= 0 && header.classList.contains('fixed')) {
                header.classList.remove('fixed');
                // console.log('fix removed')
            }
        })

        lenis.on('scroll', ScrollTrigger.update)

        gsap.ticker.add((time)=>{
            lenis.raf(time * 1000)
        })

        gsap.ticker.lagSmoothing(0)

        /** Handle ANkerlink behaviour */
        // Handle anchor link clicks
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();

                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);

                if (targetElement) {
                    const headerOffset = 120; // Höhe des Headers
                    const targetPosition = targetElement.getBoundingClientRect().top + window.scrollY - headerOffset;

                    // Use Lenis to scroll to the target position with the offset
                    lenis.scrollTo(targetPosition);
                }
            });
        });


        const header = document.querySelector('.header-outer');


        // Scrollposition wiederherstellen
        window.addEventListener('load', () => {
            const scrollPosition = localStorage.getItem('scrollPosition');
            console.log('scrollPosition: ', scrollPosition);
            if (window.scrollY > 1 && !header.classList.contains('fixed')) {
                header.classList.add('fixed');
                console.log('fix added')
            } else {
                console.log('nicht mehr als 1px scrolled')
                console.log('window.scrollY: ', window.scrollY)
            }
            
            if(window.scrollY <= 1 && header.classList.contains('fixed')) {
                header.classList.remove('fixed');
                console.log('fix removed')
            }
        });


        // Fallback if lenis dont work 
        function handleScrollForHeader() {
            if (window.scrollY > 1 && !header?.classList.contains('fixed')) {
                header?.classList.add('fixed');
                console.log('window.scrollY: ', window.scrollY);
            } 
            
            if (window.scrollY <= 1 && header?.classList.contains('fixed')) {
                header?.classList.remove('fixed');
                console.log('window.scrollY: ', window.scrollY);
            }
        }

        // Prüfen, ob das Gerät mobil ist
        if (isMobile()) {
            window.addEventListener('scroll', handleScrollForHeader);
        } else {
            console.log('lenis handel header scroll');
        }


        // Listener for smoothScrollToTarget event
        window.addEventListener('smoothScrollToTarget', (event) => {
            console.log('smoothScrollToTarget');

            const targetPosition = event.detail.targetPosition;

            // // wait 0.5 seconds
            // setTimeout(() => {
            //     // Scrollen zum angegebenen Target
            //     lenis.scrollTo(targetPosition, { immediate: true, duration: 1.5 });
            // }, 500);

            // Scrollen zum angegebenen Target
            lenis.scrollTo(targetPosition, { immediate: false, duration: 1 });
        });



        // Listener hinzufügen, um Lenis zu stoppen, wenn das Modal geöffnet wird
        window.addEventListener('modalOpen', () => {
            console.log('should stop lenis');

            // Alle ScrollTrigger pausieren
            ScrollTrigger.getAll().forEach(trigger => {
                console.log('trigger: ', trigger); 
                trigger.disable()
            });

            // Scrollen stoppen, wenn Modal geöffnet ist
            lenis.stop(); 
            
        });

        // Listener hinzufügen, um Lenis wieder zu starten, wenn das Modal geschlossen wird
        window.addEventListener('modalClose', () => {
            console.log('should start lenis');
            
            // Später wieder aktivieren
            ScrollTrigger.getAll().forEach(trigger => {
                console.log('trigger: ', trigger);
                trigger.enable()
            });
            // Scrollen wieder aktivieren, wenn Modal geschlossen ist
            lenis.start(); 
        });
        
        // Globale Variable für Lenis-Instanz
        // let lenis;

        // // Funktion zur Initialisierung von Lenis
        // function initLenis() {
        //     lenis = new Lenis({
        //         wrapper: document.querySelector('[data-scroll-container]'),
        //     });

        //     lenis.on('scroll', (e) => {
        //         // Existierende Scroll-Logik hier...
        //     });

        //     // Lenis mit GSAP verbinden
        //     lenis.on('scroll', ScrollTrigger.update);
        //     gsap.ticker.add((time) => {
        //         lenis.raf(time * 1000);
        //     });
        //     gsap.ticker.lagSmoothing(0);
        // }

        // // Funktion zum Umschalten des Scrollings für das Modal
        // function toggleModalScroll(isOpen) {
        //     const modalContent = document.querySelector('.modal.active'); // Passen Sie den Selektor an

        //     if (isOpen) {
        //         lenis.stop();
        //         modalContent.style.overflow = 'auto';
        //     } else {
        //         lenis.start();
        //         modalContent.style.overflow = '';
        //     }
        // }

        // // Event-Listener für Modal-Öffnen und -Schließen
        // window.addEventListener('modalOpen', () => {
        //     toggleModalScroll(true);
        // });

        // window.addEventListener('modalClose', () => {
        //     toggleModalScroll(false);
        // });

        // // Lenis initialisieren
        // initLenis();
    }
    



    /**
     * // TODO: 
     * Script for slider 
     */

    // Definiere Funktionen außerhalb der Schleife für eine bessere Wiederverwendbarkeit und Testbarkeit
    function splitValueUnit(value) {
        const regex = /^(\d+)(\D+)$/;
        const match = value.match(regex);
        
        if (match) {
            return {
                number: parseFloat(match[1]),
                unit: match[2]
            };
        }
        
        throw new Error("Invalid format");
    }

    function initAllSliders() {
        const sliderLinks = document.querySelectorAll('[data-link-url]');

        const timeTrashhold = 200;
        const mouseMoveTrashhold = 8;

        sliderLinks.forEach(link => {
            // console.log('link: ', link);
            // console.log(link.getAttribute('data-link-url'));

            let isDragging = false;
            let startTime;
            let startX;
            let startY;

            link.addEventListener('mousedown', (e) => {
                isDragging = false;
                startTime = new Date().getTime();
                startX = e.clientX;
                startY = e.clientY;
            });

            link.addEventListener('mousemove', (e) => {
                if (Math.abs(e.clientX - startX) > mouseMoveTrashhold || Math.abs(e.clientY - startY) > mouseMoveTrashhold) {
                    isDragging = true;
                }
            });

            link.addEventListener('mouseup', (e) => {
                const endTime = new Date().getTime();
                const timeDiff = endTime - startTime;

                if (!isDragging && timeDiff < timeTrashhold) {
                    const linkUrl = link.getAttribute('data-link-url');
                    console.log('linkUrl: ', linkUrl);
                    
                    const linkTarget = link.getAttribute('data-link-target');
                    if (linkTarget === '_blank') {
                        window.open(linkUrl, '_blank');
                    } else {
                        window.location.href = linkUrl;
                    }
                }
            });

            link.addEventListener('touchstart', (e) => {
                isDragging = false;
                startTime = new Date().getTime();
                startX = e.touches[0].clientX;
                startY = e.touches[0].clientY;
            });

            link.addEventListener('touchmove', (e) => {
                if (Math.abs(e.touches[0].clientX - startX) > mouseMoveTrashhold || Math.abs(e.touches[0].clientY - startY) > mouseMoveTrashhold) {
                    isDragging = true;
                }
            });

            link.addEventListener('touchend', (e) => {
                const endTime = new Date().getTime();
                const timeDiff = endTime - startTime;

                if (!isDragging && timeDiff < timeTrashhold) {
                    const linkUrl = link.getAttribute('data-link-url');
                    const linkTarget = link.getAttribute('data-link-target');
                    if (linkTarget === '_blank') {
                        window.open(linkUrl, '_blank');
                    } else {
                        window.location.href = linkUrl;
                    }
                }
            });
        });



        const nwSlide = document.querySelectorAll('.nw-slide');
        console.log('nwSlide: ', nwSlide);

        nwSlide.forEach(slide => {

            console.log('slide: ', slide);

            const nwSlideWrapper = slide.querySelector('.nw-slide__wrapper');
            const nwSlideList = slide.querySelector('.nw-slide__list');
            const nwSlideItem = slide.querySelectorAll('.nw-slide__item');

            let currentSlide = 0;
            let maxSlides = nwSlideItem.length;
            console.log('maxSlides: ', maxSlides);
            let arrayOfSlideValues = [];
            let currentTransformX = 0;
            let maxSlidesInView = null;
            let sliderIndicatorItems;
            let sliderAggregator = 1; // is for snapping on dnd (1 snaps on each slide)
            let dragThreshold = 0.25;

            function getMaxSlidesInView() {
                const mobileMaxSlidesInView = 1;
                const tabletMaxSlidesInView = 2;
                const desktopMaxSlidesInView = parseInt(slide.getAttribute('data-slide-max-slides') || 1);
                return window.innerWidth <= 699 ? mobileMaxSlidesInView : window.innerWidth <= 999 ? tabletMaxSlidesInView : desktopMaxSlidesInView;
            }

            function getSliderAggregator() {
                let dataSlideAggregatorAttribute = parseInt(slide.getAttribute('data-side-aggregation')) || 1;
                if(maxSlidesInView > 2) {
                    sliderAggregator = dataSlideAggregatorAttribute
                }
                if(maxSlidesInView === 2 && dataSlideAggregatorAttribute > 1) {
                    sliderAggregator = 2
                }
                if(maxSlidesInView === 1 && dataSlideAggregatorAttribute > 1) {
                    sliderAggregator = 1
                }

                return sliderAggregator
            }

            function getdDragThreshold() {
                // dragThreshold = parseFloat(slide.getAttribute('data-drag-threshold')) || 0.25;
                if(maxSlidesInView === 1) {
                    dragThreshold = 0.33
                }
                if(maxSlidesInView === 2 || maxSlidesInView === 3) {
                    dragThreshold = 0.25
                }
                if(maxSlidesInView > 3) {
                    dragThreshold = 0.1
                }
                
                return dragThreshold
            }

            // Exit function if there are not more slides than initialy showed
            if(maxSlides <= getMaxSlidesInView()) {
                nwSlideWrapper.style = 'cursor: pointer';
                return 
            }


            function calculateSliderValues() {
                maxSlidesInView = getMaxSlidesInView();
                sliderAggregator = getSliderAggregator();
                dragThreshold = getdDragThreshold();

                if(currentSlide > maxSlides - maxSlidesInView) currentSlide = maxSlides - maxSlidesInView

                let gapCount = maxSlidesInView - 1;
                let slideGapAttribute = slide.getAttribute('data-slide-gap');
                let slideGap = splitValueUnit(slideGapAttribute);
                let slideFraction = (100 / maxSlidesInView);
                let sliderWrapperWidth = nwSlideWrapper.offsetWidth;
                let slideFractionInPx = (sliderWrapperWidth - (gapCount * slideGap.number)) / maxSlidesInView;
                let minusSliderWidth = ((slideGap.number * gapCount) / maxSlidesInView);
                let sliderWidth = `calc(${slideFraction}% - ${minusSliderWidth}px)`;

                nwSlideItem.forEach(slideItem => {
                    slideItem.style.minWidth = sliderWidth;
                    slideItem.style.marginRight = `${slideGap.number}${slideGap.unit}`;
                });

                arrayOfSlideValues = [];
                for (let i = 0; i <= maxSlides - maxSlidesInView; i++) {
                    let tempCurrentTransformX = (-slideFractionInPx * i) - (slideGap.number * i);
                    arrayOfSlideValues.push(tempCurrentTransformX);
                }
            }

            function createIndicators() {
                const existingIndicators = slide.querySelector('.slider-indicator');
                if (existingIndicators) {
                    existingIndicators.remove();
                }

                const sliderIndicator = document.createElement('div');
                sliderIndicator.classList.add('slider-indicator');

                for (let i = 0; i <= maxSlides - getMaxSlidesInView(); i++) {
                    const indicatorItem = document.createElement('div');
                    indicatorItem.classList.add('slider-indicator-item');
                    indicatorItem.setAttribute('data-slide-index', i);

                    indicatorItem.addEventListener('click', function() {
                        nwSlideList.style.transition = '';
                        currentSlide = i;
                        setIndicatorActive();
                        nwSlideList.style.transform = `translate(${arrayOfSlideValues[i]}px, 0px)`;
                        currentTransformX = arrayOfSlideValues[i];
                    });

                    console.log('append Indicator');
                    sliderIndicator.appendChild(indicatorItem);
                }

                console.log('apppend indicator');
                slide.appendChild(sliderIndicator);
                sliderIndicatorItems = sliderIndicator.querySelectorAll('.slider-indicator-item');
                setIndicatorActive();
            }

            function setIndicatorActive() {
                sliderIndicatorItems.forEach((item, i) => {
                    if (i === currentSlide) {
                        item.classList.add('active');
                    } else {
                        item.classList.remove('active');
                    }
                });
            }

            calculateSliderValues();
            createIndicators();
            
            setIndicatorActive();

            let isDragging = false;
            let differenceX = 0;
            let differenceY = 0;
            let newTransform = 0;
            let startX = 0;
            let startY = 0;
            let whileX = 0;
            let whileY = 0;
            let endX = 0;

            function dragStart(e) {
                if (e.type === 'touchstart' || e.button === 0) {
                    isDragging = true;
                    startX = e.type === 'touchstart' ? e.touches[0].clientX : e.clientX;
                    startY = e.type === 'touchstart' ? e.touches[0].clientY : e.clientY;

                    nwSlideWrapper.classList.add('is-dragging');
                }
            }

            // transition: all 0.33s cubic-bezier(.44,1.7,.54,.76);
            function dragging(e) {
                if (!isDragging) return;
                whileX = e.type === 'touchmove' ? e.touches[0].clientX : e.clientX;
                whileY = e.type === 'touchmove' ? e.touches[0].clientY : e.clientY;
                differenceX = startX - whileX;
                differenceY = startY - whileY;
                newTransform = currentTransformX - differenceX;

                if (Math.abs(differenceX) > Math.abs(differenceY)) {
                    e.preventDefault();
                }

                // Verhindern, dass der Slider über den letzten oder ersten Slide hinausgezogen wird
                if (newTransform <= arrayOfSlideValues[arrayOfSlideValues.length - 1] + (arrayOfSlideValues[1] / 2)) {
                    dragEnd(e);
                    nwSlideList.style.transition = 'all 0.5s cubic-bezier(.44,1.7,.48,.83)';
                    return;
                }
                if (newTransform >= arrayOfSlideValues[0] - (arrayOfSlideValues[1] / 2)) {
                    dragEnd(e);
                    nwSlideList.style.transition = 'all 0.5s cubic-bezier(.44,1.7,.48,.83)';
                    return;
                }
                nwSlideList.style.transition = 'none';
                nwSlideList.style.transform = `translate(${newTransform}px, 0px)`;
            }

            function dragEnd(e) {
                if (!isDragging) return;
                isDragging = false;
                if (whileX === 0) return;
                endX = e.type === 'touchend' ? e.changedTouches[0].clientX : e.clientX;
                nwSlideList.style.transition = '';
                /* For default and team */ 
                // let snapFraction = arrayOfSlideValues[1];
                // currentSlide = Math.round(newTransform / snapFraction);
                // currentSlide = Math.max(0, Math.min(currentSlide, arrayOfSlideValues.length - 1));
                // let finalTransformValue = snapFraction * currentSlide;

                /* With slideAggregator */
                let slidesPerGroup = sliderAggregator; // Anzahl der Slides pro Gruppe
                let snapFraction = arrayOfSlideValues[1]; // Wert für das Einrasten, entspricht einem Slide

                // Berechne die verschobene Strecke als Prozentwert der Breite eines Slide-Bereichs
                let draggedPercentage = Math.abs(newTransform - currentTransformX) / (snapFraction * slidesPerGroup) * -1;

                // Wenn die Schwelle überschritten wird, gehe zum nächsten Abschnitt, ansonsten bleibe beim aktuellen
                if (draggedPercentage > dragThreshold) {
                    // Berechne die neue Slide-Position basierend auf der Drag-Richtung
                    if (newTransform < currentTransformX) {
                        // Nach rechts gezogen
                        currentSlide = Math.ceil(newTransform / (snapFraction * slidesPerGroup)) * slidesPerGroup;
                    } else {
                        // Nach links gezogen
                        currentSlide = Math.floor(newTransform / (snapFraction * slidesPerGroup)) * slidesPerGroup;
                    }
                }

                // Stelle sicher, dass currentSlide innerhalb der Grenzen bleibt
                currentSlide = Math.max(0, Math.min(currentSlide, arrayOfSlideValues.length - 1));
                
                let finalTransformValue = arrayOfSlideValues[currentSlide];


                // set the final transform value
                nwSlideList.style.transform = `translate(${finalTransformValue}px, 0px)`;
                whileX = 0;
                currentTransformX = finalTransformValue;
                newTransform = 0;

                nwSlideWrapper.classList.remove('is-dragging');
                setIndicatorActive();

            }

            nwSlideWrapper.addEventListener('mousedown', dragStart);
            nwSlideWrapper.addEventListener('touchstart', dragStart);
            document.body.addEventListener('mousemove', dragging);
            document.body.addEventListener('touchmove', dragging, { passive: false });
            document.body.addEventListener('mouseup', dragEnd);
            nwSlideWrapper.addEventListener('touchend', dragEnd);

            window.addEventListener('resize', () => {
                nwSlideList.style.transition = 'none';
                calculateSliderValues();
                createIndicators();
                setIndicatorActive();
                nwSlideList.style.transform = `translate(${arrayOfSlideValues[currentSlide]}px, 0px)`;
                nwSlideList.style.transition = '';
            });
        });
    }



    function initAllVideoLightboxes() {
        
        /**
         * Video Lighgbox Script 
         */
        const returnScale = (transformAttr) => {
            if (transformAttr) {
                const scaleMatch = transformAttr.match(/scale\(([^)]+)\)/);
                if (scaleMatch) {
                    const scaleValue = scaleMatch[1]; // Extrahiere den scale-Wert
                    console.log('SVG scale-Wert:', scaleValue); // Ausgabe z.B.: 1.2
                    return scaleValue
                }
            } else {
                return 1
            }
        }
            
        // Funktion zum Erstellen und Einfügen der Lightbox
        function createLightbox() {
            const lightboxModal = document.createElement('div');
            lightboxModal.id = 'hero-video-lightbox-modal';
            lightboxModal.className = 'lightbox-modal hero-video-lightbox-modal';
            lightboxModal.style.display = 'none';

            const lightboxContent = document.createElement('div');
            lightboxContent.className = 'lightbox-content';
            lightboxModal.appendChild(lightboxContent);

            const closeBtn = document.createElement('span');
            closeBtn.className = 'lightbox-close';
            closeBtn.innerHTML = `<svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18 17.94 6M18 18 6.06 6"></path>
                                </svg>`;
            lightboxContent.appendChild(closeBtn);

            const innerContent = document.createElement('div');
            innerContent.className = 'lightbox-inner-content';
            lightboxContent.appendChild(innerContent);

            const buttonContainer = document.createElement('div');
            buttonContainer.className = 'lightbox-button-container';
            lightboxContent.appendChild(buttonContainer);

            document.body.appendChild(lightboxModal);

            // Eventlistener zum Schließen der Lightbox
            closeBtn.addEventListener('click', function () {
                lightboxModal.style.display = 'none';
                innerContent.innerHTML = ''; // Nur den inneren Inhalt löschen
            });

            // Eventlistener zum Schließen der Lightbox, wenn außerhalb des Inhalts geklickt wird
            lightboxModal.addEventListener('click', function (e) {
                if (e.target === lightboxModal) {
                    lightboxModal.style.display = 'none';
                    innerContent.innerHTML = ''; // Nur den inneren Inhalt löschen
                }
            });

            return { lightboxModal, innerContent };
        }

        // Lightbox erstellen und in den DOM einfügen
        const { lightboxModal, innerContent } = createLightbox();

        // Durch alle Grids auf der Seite loopen
        document.querySelectorAll('.contains-video-lightbox').forEach(videoLightboxContainer => {
            console.log('videoLightboxContainer: ', videoLightboxContainer);

            const openVideoLightboxListener = videoLightboxContainer.querySelector('.open-video-lightbox-listener');
            console.log('openVideoLightboxListener: ', openVideoLightboxListener);

            const playButtonWrapper = videoLightboxContainer.querySelector('.play-btn-outer-wrapper');
            
            /**
             * Hover event 
             */
            const isHero =videoLightboxContainer.classList.contains('hero-wrapper')

            let videoBubbleWrapper
            let svgImage
            let transformAttr 
            let svgImageOriginalScale
            let imageWidth 
            let imageHeight
            let centerX 
            let centerY
        

            if(isHero) {
                videoBubbleWrapper = videoLightboxContainer.querySelector('.right-bubble.first');
        
                svgImage = videoLightboxContainer.querySelector('.right-bubble.first svg svg image');
                transformAttr = svgImage.getAttribute('transform');
                svgImageOriginalScale = returnScale(transformAttr);
        
                // Hole die Breite und Höhe des SVG-Images
                imageWidth = svgImage.getBBox().width;
                imageHeight = svgImage.getBBox().height;
        
                // Berechne die Mitte des Bildes
                centerX = imageWidth / 2;
                centerY = imageHeight / 2;
            }
            
            
            
            openVideoLightboxListener.addEventListener('mouseover', function () {
                if(isHero) {
                    videoBubbleWrapper.classList.add('hover');
                    svgImage.setAttribute('transform', `scale(${svgImageOriginalScale * 1.05})`);
                }

                playButtonWrapper.classList.add('hover');
            })

            openVideoLightboxListener.addEventListener('mouseout', function () {
                if(isHero) {
                    videoBubbleWrapper.classList.remove('hover');
                    svgImage.setAttribute('transform', `scale(${svgImageOriginalScale})`);
                }

                playButtonWrapper.classList.remove('hover');
            })
            

            /**
             * Click Event
             */
            // get data-video-url
            const videoUrl = openVideoLightboxListener.getAttribute('data-video-url');

            openVideoLightboxListener.addEventListener('click', function () {
                console.log('openVideoLightboxListener clicked');
                
                innerContent.innerHTML = ''; // Inneren Inhalt löschen
                lightboxModal.style.display = 'flex';

                const video = document.createElement('video');
                video.src = videoUrl;
                video.controls = true;
                video.autoplay = true;
                innerContent.appendChild(video);
            })
        });
    }



    // Init all Accordions 
    function initAllAccordions() {
        const accordionItems = document.querySelectorAll('.single-q-and-a');
        accordionItems.forEach((item, index) => {
            const toggleButton = item.querySelector('.toggle-button');
            const question = item.querySelector('.content-wrapper h4');
            const answer = item.querySelector('.content-wrapper p');

            if(index === 0) {
                answer.style.height = `${answer.scrollHeight}px`;
            }

            toggleButton.addEventListener('click', () => {
                toggleAccordionItems(index);
            })

            question.addEventListener('click', () => {
                toggleAccordionItems(index);
            })
        })

        function toggleAccordionItems(index, answer) {
            accordionItems.forEach((item, i) => {
                const answer = item.querySelector('.content-wrapper p');
                const scrollHeight = answer.scrollHeight;

                const isItemCollapsed = item.classList.contains('collapsed');

                if(i === index) {
                    if (!isItemCollapsed) {
                        item.classList.add('collapsed');
                        answer.style.height = 0;
                    } else {
                        item.classList.remove('collapsed');
                        answer.style.height = `${scrollHeight}px`;
                    }
                } else {
                    item.classList.add('collapsed');
                    answer.style.height = 0;
                }
            })
        }
    }

</script>

</body>
</html>

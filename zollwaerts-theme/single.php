<!-- Blog Post Template -->
<?php get_header(); ?>


<?php
    $json_data = get_post_meta(get_the_ID(), '_page_builder_blocks', true);

    // <PERSON><PERSON><PERSON><PERSON> von wp_unslash, um unnötige Escape-Zeichen zu entfernen
    $unslashed_data = wp_unslash($json_data);

    // Dekodieren der JSON-Daten in ein PHP-Array
    $page_builder_blocks = json_decode($unslashed_data, true); 
?>


<style>
    .single-post .block-wrapper {
        padding-top: 50px;
        padding-bottom: 50px;
    }
</style>

<main>
    <?php
    while (have_posts()) : the_post();
        ?>
        <article id="post-<?php the_ID(); ?>" <?php post_class(); ?>>
            

            <div class="entry-content block">
                <!-- Ausgabe von Autor und Kategorien -->
                <div class="post-header">
                    <div class="categories">
                        <p>
                            <?php the_category(', '); ?>
                        </p>
                    </div>

                    <!-- <h1 class="entry-title"><?php the_title(); ?></h1> -->

                    <?php
                        $title_big = get_post_meta(get_the_ID(), '_grenzlotsen_title_big', true);
                        $title_small = get_post_meta(get_the_ID(), '_grenzlotsen_title_small', true);

                        if ($title_big) {
                            echo '<h1>' . esc_html($title_big) . '</h1>';
                        } else {
                            echo '<h1>' . the_title() . '</h1>';
                        }

                        if ($title_small) {
                            echo '<h3>' . esc_html($title_small) . '</h3>';
                        }

                    ?>

                    <style>
                        .post-header h1 {
                            font-size: 54px;
                            line-height: 64px;
                            font-weight: 600;
                            /* margin-bottom: 34px; */
                            padding: 16px 0;
                        }
                        .post-header h3 {
                            font-size: 24px;
                            line-height: 36px;
                            font-weight: 600;
                            /* margin-bottom: 34px; */
                            padding: 16px 0;
                        }
                        .post-header .intro-text {
                            padding: 16px 0
                        }
                    </style>


                    <!-- Post Meta -->
                    <div class="post-meta">
                        <div class="author-info">
                            <?php echo get_avatar( get_the_author_meta( 'ID' ), 64 ); // 64 ist die Größe des Gravatars ?>
                            <p>
                                <?php the_author(); ?>
                            </p>
                        </div>

                        <span class="vertical-divider"></span>

                        <div class="last-modified">
                            <?php 
                            // Zuletzt geändert Datum ausgeben
                            echo '<p>' . esc_html__('Zuletzt aktualisiert:', 'zollwaerts-theme') . ' ' . esc_html( get_the_modified_date() ) . '</p>';
                            ?>
                        </div>

                        <span class="vertical-divider"></span>

                        <div class="published">
                            <?php
                            // Publiziert Datum ausgeben
                            echo '<p>' . esc_html__('Publiziert:', 'zollwaerts-theme') . ' ' . esc_html( get_the_date() ) . '</p>';
                            ?>
                        </div>
                    </div>
                    <style>
                        .post-meta {
                            display: flex;
                            jusity: start;
                            align-items: center;
                            gap: 1rem;
                            padding: 24px 0;
                        }

                        .post-meta p {
                            font-family: "Poppins-Regular";
                            font-size: 16px;
                            font-weight: 400;
                            font-style: Regular;
                            letter-spacing: 0px;
                            line-height: 30px;
                        }

                        .post-meta .author-info {
                            display: flex;
                            jusity: start;
                            align-items: center;
                            gap: 1rem;
                        }
                        .post-meta .author-info img {
                            border-radius: 999px;
                        }

                        .post-meta .vertical-divider {
                            height: 30px;
                            width: 2px;
                            background: #AFAFC7;
                        }
                    </style>


                    <?php
                        $intro_text = get_post_meta(get_the_ID(), '_grenzlotsen_intro', true);

                        if ($intro_text) {
                            echo '<div class="intro-text"><p>' . wp_kses_post($intro_text) . '</p></div>';
                        }

                        $image_id = get_post_meta(get_the_ID(), '_grenzlotsen_image', true);
                        $image_url = wp_get_attachment_url($image_id);

                        if ($image_url) {
                            echo '<div class="hero-img-wrapper"><img src="' . esc_url($image_url) . '" alt="' . esc_attr($title_big) . '"></div>';
                        }
                    ?>

                    <style>
                        .hero-img-wrapper {
                            padding: 76px 0 50px 0;
                        }
                    </style>

                </div>
            </div>

            <!-- REAL CODE -->
            <section class="main-content">

                <?php the_content(); ?>

                <?php 
                    // // Holen der gespeicherten JSON-Daten aus den Post-Metadaten
                    // $json_data = get_post_meta(get_the_ID(), '_page_builder_blocks', true);

                    // // Verwenden von wp_unslash, um unnötige Escape-Zeichen zu entfernen
                    // $unslashed_data = wp_unslash($json_data);
                    
                    // // Dekodieren der JSON-Daten in ein PHP-Array
                    // $page_builder_blocks = json_decode($unslashed_data, true); 


                    if ($page_builder_blocks && is_array($page_builder_blocks)) {
                        foreach ($page_builder_blocks as $block) {
                            echo '<div style="border: solid red 2px; margin-bottom: 10px;">';
                            echo 'Block-Typ: ' . htmlspecialchars($block['type']);
                            echo '</div>';
                            // Rest des Codes...
                        }

                        foreach ($page_builder_blocks as $block) {
                            // echo '<div style="border: solid red 2px;">';
                            // echo htmlspecialchars(json_encode($block));
                            // echo '</div>';

                            switch ( $block['type'] ) {
                                case 'hero':
                                    $block_type = 'hero';
                                    $has_hero_block = true;
                                    break;
                                case 'text-image':
                                    $block_type = 'text-image';
                                    break;
                                case 'featureGrid':  // Dein neuer Blocktyp für featureGrid
                                    $block_type = 'featureGrid';
                                    break;
                                case 'imageGrid':  // Dein neuer Blocktyp für imageGrid
                                    $block_type = 'imageGrid';
                                    break;
                                case 'cta-section':  // Dein neuer Blocktyp für cta-section
                                    $block_type = 'cta-section';
                                    break;
                                case 'slider-section':  // Dein neuer Blocktyp für slider-section
                                    $block_type = 'slider-section';
                                    break;
                                case 'sticky-text-image':  // Dein neuer Blocktyp für sticky-text-image
                                    $block_type = 'sticky-text-image';
                                    break;
                                case 'post-list':  // Dein neuer Blocktyp für post-list
                                    $block_type = 'post-list';
                                    break;
                                case 'pricing-table':  // Dein neuer Blocktyp für pricing-table
                                    $block_type = 'pricing-table';
                                    break;
                                case 'modal':  // Dein neuer Blocktyp für modal
                                    $block_type = 'modal';
                                    break;
                                case 'raw-html':  // Dein neuer Blocktyp für HTML-Inhalte
                                    $block_type = 'raw-html';
                                    break;
                                case 'headline':
                                    $block_type = 'headline';
                                    break;
                                case 'paragraph':
                                    $block_type = 'paragraph';
                                    break;
                                default:
                                    $block_type = '';
                                    break;
                            }
                            if ( $block_type ) {
                                set_query_var( 'block', $block );

                                // TODO: make actual fullwidth settings 
                                // $is_fullwidth = $block['advancedSettings']['fullwidth'];
                                $fullwidth_types = ['hero', 'slider-section'];
                                $is_fullwidth = in_array($block_type, $fullwidth_types) ? 'fullwidth' : '';

                                // echo '<p>' . $block_type . ' ' . $is_fullwidth . '</p>';


                                $spacing_top = isset($block['advancedSettings']['spacing']['top']) ? $block['advancedSettings']['spacing']['top'] : 'default_value';
                                $spacing_right = isset($block['advancedSettings']['spacing']['right']) ? $block['advancedSettings']['spacing']['right'] : 'default_value';
                                $spacing_bottom = isset($block['advancedSettings']['spacing']['bottom']) ? $block['advancedSettings']['spacing']['bottom'] : 'default_value';
                                $spacing_left = isset($block['advancedSettings']['spacing']['left']) ? $block['advancedSettings']['spacing']['left'] : 'default_value';

                                $custom_spacing_top = isset($block['advancedSettings']['spacing']['topCustom']) ? $block['advancedSettings']['spacing']['topCustom'] : 'default_value';
                                $custom_spacing_right = isset($block['advancedSettings']['spacing']['rightCustom']) ? $block['advancedSettings']['spacing']['rightCustom'] : 'default_value';
                                $custom_spacing_bottom = isset($block['advancedSettings']['spacing']['bottomCustom']) ? $block['advancedSettings']['spacing']['bottomCustom'] : 'default_value';
                                $custom_spacing_left = isset($block['advancedSettings']['spacing']['leftCustom']) ? $block['advancedSettings']['spacing']['leftCustom'] : 'default_value';


                                $isSpacingTopCustom = $spacing_top === 'custom';
                                $isSpacingRightCustom = $spacing_right === 'custom';
                                $isSpacingBottomCustom = $spacing_bottom === 'custom';
                                $isSpacingLeftCustom = $spacing_bottom === 'custom';

                                $spacing_styles = '';
                                if ($isSpacingTopCustom) {
                                    $spacing_styles .= "padding-top: {$custom_spacing_top}px; ";
                                }
                                if ($isSpacingRightCustom) {
                                    $spacing_styles .= "padding-right: {$custom_spacing_right}px; ";
                                }
                                if ($isSpacingBottomCustom) {
                                    $spacing_styles .= "padding-bottom: {$custom_spacing_bottom}px; ";
                                }
                                if ($isSpacingLeftCustom) {
                                    $spacing_styles .= "padding-left: {$custom_spacing_left}px; ";
                                }
                                ?>

                                <div 
                                    class="block <?php echo $is_fullwidth . ' ' . 'top-spacing-' . $spacing_top ?> <?php echo 'right-spacing-' . $spacing_bottom ?> <?php echo 'bottom-spacing-' . $spacing_bottom ?> <?php echo 'left-spacing-' . $spacing_left ?>" 
                                >
                                    <div class="block-wrapper" style="<?php echo esc_attr($spacing_styles); ?>">
                                        <?php get_template_part( 'template-parts/block', $block_type ); ?>
                                    </div>
                                </div>
                                <?php
                            }
                        
                        } 
                    } else {
                        echo '<div> NO BLOCKS </div>';
                    }
                ?>
            
                

                <!-- Meta Boxes Output -->
                <?php
                    $mein_text_input = get_post_meta(get_the_ID(), '_mein_text_input', true);
                    if ($mein_text_input) {
                        echo '<div class="mein-text-input">' . wp_kses_post($mein_text_input) . '</div>';
                    }
                ?>

                <?php
                wp_link_pages(array(
                    'before' => '<div class="page-links">' . esc_html__('Pages:', 'zollwaerts-theme'),
                    'after'  => '</div>',
                ));
                ?>
            </section> <!-- End "block" class -->


        </article>

        <?php
            // Wenn Kommentare erlaubt sind oder wir mindestens einen Kommentar haben, das Kommentar-Template laden
            if (comments_open() || get_comments_number()) :
                echo '<section class="comment-section block" style="margin-top: 300px;">';
                comments_template();
                echo '</section>';
            endif;
        ?>
        <?php
    endwhile; // Ende der Loop
    ?>
</main>

<?php get_template_part('global-modules/global-modals'); ?>

<?php 
    // get_sidebar(); 
?>
<?php get_footer(); ?>

<!-- Global Modals -->

<?php
    // Helper functions
    function find_form_by_id($theme_settings_array, $id) {
        if (isset($theme_settings_array['htmlForms'])) {
            foreach ($theme_settings_array['htmlForms'] as $form) {
                if ($form['id'] == $id) {
                    return $form;
                }
            }
        }
        return null; // ID nicht gefunden
    }

    /**
     * global modals – on whole site available
     */
    // Holen der gespeicherten JSON-Daten aus den Post-Metadaten
    $theme_settings_json = get_option('grenzlotsen_page_builder_content');
    // echo '<p>theme_settings_json:' . $theme_settings_json . '</p>';

    // Ver<PERSON><PERSON> von wp_unslash, um unnötige Escape-Zeichen zu entfernen
    $unslashed_theme_settings = wp_unslash($theme_settings_json);
    // echo '<p>unslashed_data:' . $unslashed_data . '</p>';

    // Dekodieren der JSON-Daten in ein PHP-Array
    $theme_settings_array = json_decode($unslashed_theme_settings, true); 

    if ($theme_settings_array === null && json_last_error() !== JSON_ERROR_NONE) {
        echo '<p>JSON-Fehler: ' . json_last_error_msg() . '</p>';
    }
    
    // echo '<pre>';
    // echo '<p>theme_settings_array:</p>';
    // print_r($theme_settings_array['modals']);
    // echo '</pre>';

    echo '<!-- Start global-modals -->';

    if (isset($theme_settings_array['modals']) && is_array($theme_settings_array['modals'])) {
        foreach ($theme_settings_array['modals'] as $modal) {

            echo '<div class="modal ' . $modal['id'] . ' global-modal" style="opacity: 0;">';
                // Modal Header
                echo '<div class="modal-header">';
                echo '<div class="close-modal-button" data-modal-class="' . $modal['id'] . '">
                            <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18 17.94 6M18 18 6.06 6"/>
                            </svg>
                        </div>';
                echo '</div>';

                // Modal COntent
                echo '<div class="modal-wrapper">';
                    if (isset($modal['content']) && is_array($modal['content'])) {
                        foreach ($modal['content'] as $content_element) {
                            if( $content_element['type'] === 'form') {
                                // echo '<h3> es gibt ein formular mit der ID: ' . $content_element['referenceId'] . '</h3>'; 
                                // echo '<p>Lorem Ipsum</p>';  
                                $id_to_find =  $content_element['referenceId'];
                                $form = find_form_by_id($theme_settings_array, $id_to_find);
                                if (!empty($form)) {
                                    echo '<div>' . $form['html'] . '</div>';
                                } 
                            }

                            /* echo display element in forech */
                            // echo '<hr>';
                            // echo '<pre>';
                            //     echo print_r($content_element);
                            // echo '</pre>';
                        } 
                    } else {
                        echo '<!-- No content found in modal -->';
                    }

                    /* echo display all */ 
                    // echo '<pre>';
                    // echo '<p>theme_settings_array:</p>';
                    // print_r($theme_settings_array['modals']);
                    // echo '</pre>';

                echo '</div>';
            echo '</div>';

        }
    } else {
        echo '<!-- No modals found or incorrect data structure -->';
    }

    echo '<!-- End global-modals -->';

?>
<?php

    // useSidebar
    $useSidebar = false;

    /**
     * Modal only on this specific page
     */
    // Holen der gespeicherten JSON-Daten aus den Post-Metadaten
    $json_data = get_post_meta(get_the_ID(), '_page_builder_blocks', true);

    // <PERSON><PERSON><PERSON><PERSON> von wp_unslash, um unnötige Escape-Zeichen zu entfernen
    $unslashed_data = wp_unslash($json_data);

    // Dekodieren der JSON-Daten in ein PHP-Array
    $page_builder_blocks = json_decode($unslashed_data, true); 

    if ($page_builder_blocks && is_array($page_builder_blocks)) {
        foreach ($page_builder_blocks as $block) {
            if( $block['type'] === 'modal') {
                // echo '<div style="border: solid red 2px;">';
                // echo htmlspecialchars(json_encode($block));
                // echo '</div>';

                echo '<div class="modal ' . $block['id'] . '" style="opacity: 0;">';
                    // Modal Header
                    echo '<div class="modal-header">';
                    echo '<div class="close-modal-button" data-modal-class="' . $block['id'] . '">
                                <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18 17.94 6M18 18 6.06 6"/>
                                </svg>
                            </div>';
                    echo '</div>';
                    // echo '<h1> es gibt ein Modal mit der ID: ' . $block['id'] . '</h1>';

                    // Modal COntent
                    echo '<div class="modal-wrapper">' . $block['content'] . '</div>';
                    // set_query_var( 'block', $block );
                    // get_template_part( 'template-parts/block', $block_type );
                echo '</div>';
            }
        
        } 
    } else {
        echo '';
    }



?>
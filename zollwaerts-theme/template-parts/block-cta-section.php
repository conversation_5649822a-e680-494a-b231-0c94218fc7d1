<?php
if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

// Angenommen $block enthält deine Datenstruktur
$ctas = $block['ctas'] ?? []; // Sicherstellen, dass ctas existiert

// Daten als String ausgeben
// echo '<pre>';
// echo 'Block-Daten: ';
// echo htmlspecialchars(var_export($block, true));
// echo '</pre>';


$spacing_top = $block['advancedSettings']['spacing']['top'];
$spacing_right = $block['advancedSettings']['spacing']['right'];
$spacing_bottom = $block['advancedSettings']['spacing']['bottom'];
$spacing_left = $block['advancedSettings']['spacing']['left'];

$isSpacingTopCustom = $spacing_top === 'custom';
$isSpacingRightCustom = $spacing_top === 'custom';
$isSpacingBottomCustom = $spacing_bottom === 'custom';
$isSpacingLeftCustom = $spacing_bottom === 'custom';
?>

<div 
    id="<?php echo esc_attr( $block['id'] ); ?>" 
    class="block-inner cta-section <?php echo $block['useImage'] ? 'with-image' : 'no-image' ?> <?php echo isset($block['useQuote']) && $block['useQuote'] ? 'with-quote' : 'no-quote' ?>">
    <div class="content-wrapper <?php $block['useImage'] ? 'with-image' : '' ?>">
        <div class="background"></div>

        <div class="content">

            <?php if ( isset($block['useQuote']) && $block['useQuote'] && ! empty( $block['quote'] ) ) : ?>
                <div class="quote">
                    <span class="quote-icon">
                        <svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                            <g id="startseite" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <g id="Startseite-video/img" transform="translate(-408, -11023)" fill="#FFFFFF" fill-rule="nonzero">
                                    <g id="section" transform="translate(295, 10917)">
                                        <g id="icons/quote" transform="translate(113, 109.5)">
                                            <path d="M7.46666667,25.5 C11.1515152,25.5 14.0606061,22.4712644 14.0606061,18.8563218 C14.0606061,15.2413793 11.5393939,12.7011494 8.33939394,12.7011494 C7.66060606,12.7011494 6.88484848,12.7988506 6.69090909,12.8965517 C7.27272727,9.57471264 10.569697,5.56896552 13.6727273,3.71264368 L9.01818182,0 C3.68484848,3.81034483 0,9.86781609 0,16.6091954 C0,22.2758621 3.49090909,25.5 7.46666667,25.5 Z M25.4060606,25.5 C29.0909091,25.5 32,22.4712644 32,18.8563218 C32,15.2413793 29.4787879,12.7011494 26.2787879,12.7011494 C25.6,12.7011494 24.8242424,12.7988506 24.630303,12.8965517 C25.2121212,9.57471264 28.5090909,5.56896552 31.6121212,3.71264368 L26.9575758,0 C21.6242424,3.81034483 17.9393939,9.86781609 17.9393939,16.6091954 C17.9393939,22.2758621 21.430303,25.5 25.4060606,25.5 Z"></path>
                                        </g>
                                    </g>
                                </g>
                            </g>
                        </svg>
                    </span>
                    <h3><?php echo  $block['quote']; ?></h3>
                </div>
            <?php endif; ?>


            <?php if ( isset($block['useQuote']) && $block['useQuote']) : ?>
                <div class="quote-right-side" style="display: flex; flex-direction: column; gap: 2rem;">
            <?php endif; ?>

                <?php if ( ! empty( $block['tagline'] ) ) : ?>
                    <p class="tagline"><?php echo wp_kses_post( $block['tagline'] ); ?></p>
                <?php endif; ?>

                <?php if ( ! empty( $block['heading'] ) ) : ?>
                    <h2><?php echo wp_kses_post( $block['heading'] ); ?></h2>
                <?php endif; ?>

                <?php if ( ! empty( $block['content'] ) ) : ?>
                    <?php $content_with_br = nl2br($block['content']); ?>
                    <p><?php echo $content_with_br; ?></p>
                    <!-- <?php 
                        $content = wp_kses_post($block['content']); // Den Inhalt filtern, um XSS-Angriffe zu vermeiden
                        // $paragraphs = explode("\n\n", $content); // Den Inhalt in Absätze aufteilen
                        $paragraphs = preg_split('/\R{2,}/u', $content);

                        echo '<p>' . json_encode($block['content']) . '</p>';
                        echo '<p>' . json_encode($block['content']) . '</p>';

                        foreach ($paragraphs as $paragraph) {
                            echo '<p>' . $paragraph . '</p>';
                        }
                    ?> -->

                <?php endif; ?>


                <?php if ( ! empty( $ctas ) ) : ?>
                    <div class="cta-buttons">
                        <?php foreach ( $ctas as $cta ) : ?>
                            <a 
                                href="<?php echo esc_url( $cta['link'] ); ?>" 
                                style="display: inline-block;"
                                <?php if(isset($cta['useModal']) && $cta['useModal']) : ?>
                                    <?php echo 'data-modal-class="' . esc_attr( $cta['link'] ) . '"' ?>
                                <?php endif; ?>

                                <?php echo isset($cta['id']) ? 'id="cta-' . esc_attr( $cta['id'] ) . '">' : '' ?>
                                class="cta-button">
                                <div>
                                    <?php echo wp_kses_post( $cta['text'] ); ?>
                                    <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 12H5m14 0-4 4m4-4-4-4"></path>
                                    </svg>
                                </div>
                            </a>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>

            <?php if ( isset($block['useQuote']) && $block['useQuote']) : ?>
            </div>
            <?php endif; ?>
        </div>

        <?php if ( $block['useImage'] && ! empty( $block['image'] ) ) : ?>
            <div class="image">
                <img src="<?php echo esc_url( $block['image'] ); ?>" alt="Image">
            </div>
        <?php endif; ?>
    </div>
</div>   




<style>
    .block-inner.cta-section .content {
        /* overflow: hidden;
        position: relative;
        border-radius: 1rem;
        padding: 3rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        grid-template-columns: 1fr 1fr; */

        overflow: hidden;
        position: relative;
        border-radius: 0;
        padding: 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        color: #fff;
    }

    /* .block-inner.cta-section .content-wrapper {
        position: absolute;
        width: 100%;
        height: 100%;
        background: #11114A;
        border-radius: 1rem;
        overflow: hidden;
        position: relative;
    } */

    .block-inner.cta-section .content-wrapper {
        position: absolute;
        width: 100%;
        height: 100%;
        background: #11114A;
        border-radius: 1rem;
        padding: 2.5rem 3rem;
        overflow: hidden;
        position: relative;
        display: grid;
        gap: 36px;
    }

    /* without image bg */
    .block-inner.cta-section .content-wrapper .background {
        position: absolute;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle at 0% 0%, #bf1692 0%, #eee0 50%);
    }
    .block-inner.cta-section .content-wrapper .background:before {
        content: '';
        background: radial-gradient(circle at 100% 0, rgb(191 22 146 / 66%) 0%, #eee0 50%);
        position: absolute;
        width: 100%;
        height: 100%;
    }

    /* width image bg */
    .block-inner.cta-section .content-wrapper.with-image .background {
        position: absolute;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle at 0% 0%, #bf1692 0%, #eee0 33%);
    }
    .block-inner.cta-section .content-wrapper.with-image .background:before {
        content: '';
        background: radial-gradient(circle at 100% 0, rgb(191 22 146 / 66%) 0%, #eee0 33%);
        position: absolute;
        width: 100%;
        height: 100%;
    }


    

    /* .block-inner.cta-section .content:before {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle at 0%, #bf1692 0%, #eee0 50%);
        margin-left: -3rem;
        z-index: 0;
    } */

    .block-inner.cta-section h2,
    .block-inner.cta-section h3,
    .block-inner.cta-section h4, 
    .block-inner.cta-section h5,
    .block-inner.cta-section h6,
    .block-inner.cta-section p {
        color: #fff;
        margin-bottom: 20px;
        text-wrap: pretty;
    }
    .block-inner.cta-section.no-image h2,
    .block-inner.cta-section.no-image h3,
    .block-inner.cta-section.no-image h4, 
    .block-inner.cta-section.no-image h5,
    .block-inner.cta-section.no-image h6,
    .block-inner.cta-section.no-image p {
        margin: 0;
    }

    .block-inner.cta-section.with-image .cta-buttons {
        margin-top: 20px;
    }

    .block-inner.cta-section .cta-buttons a.cta-button {
        margin: 0;
    }


    /* Use of Image */ 
    .block-inner.cta-section.with-image .content-wrapper {
        position: absolute;
        width: 100%;
        height: 100%;
        /* background: #11114A; */
        border-radius: 1rem;
        overflow: hidden;
        position: relative;
        display: grid;
        grid-template-columns: 1fr 1fr;
        align-items: center;
    }

    .block-inner.cta-section.with-image .content {
        display: flex;
        flex-direction: column;
        align-items: baseline;
    }

    .block-inner.cta-section.with-image .content-wrapper .image {
        z-index: 1;
    }



    /**
     * With Quote 
     */ 
    .block-inner.cta-section.with-quote .content {
        grid-template-columns: 1fr 1fr;
        display: grid;
        gap: 2rem;
    }
    .block-inner.cta-section.with-quote .content-wrapper {
        background-image: url(<?php echo isset($block['useBackgroundImage']) && $block['useBackgroundImage'] && isset($block['backgroundImage']) ? $block['backgroundImage'] : ''; ?>);
        /* background-image: url('<?php echo esc_url($block['backgroundImage']) ?>'); */
        background-size: cover;
        background-position: center;
        border-radius: 1rem;
        padding: 10rem 3rem;
    }
    .block-inner.cta-section.with-quote .content-wrapper .background {
        position: absolute;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle at 0% 0%, #bf1692ba 0%, #000000ab 50%);
    }

    
</style>
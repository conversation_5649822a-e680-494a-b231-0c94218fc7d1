<?php
if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

// Überprüfung, ob es sich um den richtigen Blocktyp handelt
if ($block['type'] === 'imageGrid') :
?>

<div id="<?php echo esc_attr($block['id']); ?>" class="block-inner image-grid">
    <?php if (!empty($block['heading'])) : ?>
        <h2><?php echo wp_kses_post($block['heading']); ?></h2>
    <?php endif; ?>
    
    <?php if (!empty($block['paragraph'])) : ?>
        <p class="module-intro"><?php echo wp_kses_post($block['paragraph']); ?></p>
    <?php endif; ?>

    <div class="grid" style="grid-template-columns: repeat(<?php echo intval($block['gridTemplate']); ?>, 1fr);">
        <?php foreach ($block['gridItems'] as $item) : ?>
            <div class="grid-item" 
                data-url="<?php echo isset($item['useVideo']) && $item['useVideo'] ? esc_url($item['videoUrl']) : esc_url($item['iconUrl']); ?>" 
                data-type="<?php echo isset($item['useVideo']) ? ($item['useVideo'] ? 'video' : 'image') : 'image'; ?>"
            >
                <?php if (isset($item['useVideo']) && $item['useVideo']) : ?>
                    <span class="video-overlay">
                        <span class="play-button">
                            <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="128" height="128" fill="currentColor" viewBox="0 0 24 24">
                                <path fill-rule="evenodd" d="M8.6 5.2A1 1 0 0 0 7 6v12a1 1 0 0 0 1.6.8l8-6a1 1 0 0 0 0-1.6l-8-6Z" clip-rule="evenodd"/>
                            </svg>
                        </span>
                    </span>
                    <?php echo '<img src="' . esc_url($item['thumbnailUrl']) . '" alt="' . esc_attr($block['heading']) . '" />'; ?>
                <?php else : ?>
                    <?php echo '<img src="' . esc_url($item['iconUrl']) . '" alt="' . esc_attr($block['heading']) . '" />'; ?>
                <?php endif; ?>
            </div>
        <?php endforeach; ?>
    </div>
</div>

<?php
    // Variable zum Überprüfen, ob das Script bereits eingebunden wurde
    global $image_grid_script_loaded;
    if ( ! isset( $image_grid_script_loaded ) ) {
        $image_grid_script_loaded = false;
    }
?>

<?php if ( ! $image_grid_script_loaded ) : ?>
<script>
document.addEventListener("DOMContentLoaded", function () {
    // Funktion zum Erstellen und Einfügen der Lightbox
    function createLightbox() {
        const lightboxModal = document.createElement('div');
        lightboxModal.id = 'lightbox-modal';
        lightboxModal.className = 'lightbox-modal';
        lightboxModal.style.display = 'none';

        const lightboxContent = document.createElement('div');
        lightboxContent.className = 'lightbox-content';
        lightboxModal.appendChild(lightboxContent);

        const closeBtn = document.createElement('span');
        closeBtn.className = 'lightbox-close';
        closeBtn.innerHTML = `<svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18 17.94 6M18 18 6.06 6"></path>
                              </svg>`;
        lightboxContent.appendChild(closeBtn);

        const innerContent = document.createElement('div');
        innerContent.className = 'lightbox-inner-content';
        lightboxContent.appendChild(innerContent);

        const buttonContainer = document.createElement('div');
        buttonContainer.className = 'lightbox-button-container';
        lightboxContent.appendChild(buttonContainer);

        const prevBtn = document.createElement('button');
        prevBtn.className = 'navigate lightbox-prev';
        prevBtn.innerHTML = `<svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m15 19-7-7 7-7"/>
                            </svg>`;
        buttonContainer.appendChild(prevBtn);

        const nextBtn = document.createElement('button');
        nextBtn.className = 'navigate lightbox-next';
        nextBtn.innerHTML = `<svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m9 5 7 7-7 7"/>
                            </svg>`;
        buttonContainer.appendChild(nextBtn);

        document.body.appendChild(lightboxModal);

        // Eventlistener zum Schließen der Lightbox
        closeBtn.addEventListener('click', function () {
            lightboxModal.style.display = 'none';
            innerContent.innerHTML = ''; // Nur den inneren Inhalt löschen
        });

        // Eventlistener zum Schließen der Lightbox, wenn außerhalb des Inhalts geklickt wird
        lightboxModal.addEventListener('click', function (e) {
            if (e.target === lightboxModal) {
                lightboxModal.style.display = 'none';
                innerContent.innerHTML = ''; // Nur den inneren Inhalt löschen
            }
        });

        return { lightboxModal, innerContent, prevBtn, nextBtn };
    }

    // Lightbox erstellen und in den DOM einfügen
    const { lightboxModal, innerContent, prevBtn, nextBtn } = createLightbox();

    // Durch alle Grids auf der Seite loopen
    document.querySelectorAll('.block-inner.image-grid').forEach(grid => {
        const gridItems = grid.querySelectorAll('.grid-item');
        let currentIndex = -1;

        function showItem(index) {
            if (index < 0) {
                index = gridItems.length - 1; // Loop zum letzten Element
            } else if (index >= gridItems.length) {
                index = 0; // Loop zum ersten Element
            }

            const item = gridItems[index];
            const url = item.getAttribute('data-url');
            const type = item.getAttribute('data-type');

            innerContent.innerHTML = ''; // Inneren Inhalt löschen

            if (type === 'video') {
                const video = document.createElement('video');
                video.src = url;
                video.controls = true;
                video.autoplay = true;
                innerContent.appendChild(video);
            } else {
                const img = document.createElement('img');
                img.src = url;
                innerContent.appendChild(img);
            }

            lightboxModal.style.display = 'flex';
            currentIndex = index;
        }

        // Eventlistener für alle Grid-Items im aktuellen Grid
        gridItems.forEach((item, index) => {
            item.addEventListener('click', function () {
                showItem(index);
            });
        });

        // Eventlistener für die Navigationsbuttons im aktuellen Grid
        prevBtn.addEventListener('click', function () {
            showItem(currentIndex - 1);
        });

        nextBtn.addEventListener('click', function () {
            showItem(currentIndex + 1);
        });
    });
});

</script>
<?php 
$image_grid_script_loaded = true; 
endif; 
?>

<style>
    /** Lightbox CSS */
    .lightbox-modal {
        position: fixed;
        z-index: 9999;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.9);
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 1rem;
    }

    .lightbox-close {
        position: absolute;
        /* top: 2rem; */
        right: 0px;
        color: white;
        font-size: 2rem;
        cursor: pointer;
    }

    .lightbox-content {
        /* max-width: 90%; */
        /* max-height: 90%; */
        max-width: var(--content-width);
        padding: var(--content-padding);
        /* padding-top: calc(4rem + 70px); */
        position: relative;
    }

    .lightbox-content img,
    .lightbox-content video {
        width: 100%;
        height: auto;
        display: block;
        max-height: 80vh;
        object-fit: contain;
    }

    .lightbox-button-container {
        display: flex;
        justify-content: center;
    }

    .lightbox-button-container button.navigate {
        width: 3rem;
        border-radius: 4px;
        aspect-ratio: 1 / 1;
        margin-top: 1rem;
    }

    /* Image grid CSS */
    .block-inner.image-grid .grid-item img {
        width: 100%;
        height: 100%;
    } 
    .block-inner.image-grid .grid-item {
        position: relative;
        cursor: pointer;
    }
    .block-inner.image-grid .grid-item img {
        display: block;
        object-fit: cover;
    }

    .block-inner.image-grid .grid-item .video-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.3);
        display: flex;
        justify-content: center;
        align-items: center;
        transition: background-color 0.3s ease;
    }
    .block-inner.image-grid .grid-item:hover .video-overlay {
        background-color: rgba(0, 0, 0, 0.2);
    }

    
    .block-inner.image-grid .grid-item .video-overlay .play-button {
        background-color: rgba(255, 255, 255, 0.9);
        /* padding: 1rem; */
        width: 25%;
        aspect-ratio: 1 / 1;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: space-around;
        transition: background-color 0.3s ease;
    }
    .block-inner.image-grid .grid-item:hover .video-overlay .play-button {
        background-color: rgba(255, 255, 255, 1);
        /* background-color: var(--accent-color-secondary); */
    }

    .block-inner.image-grid .grid-item .video-overlay .play-button svg {
        width: 50%;
        height: 50%;
        fill: var(--accent-color-secondary);
        transition: fill 0.3s ease;
    }
    .block-inner.image-grid .grid-item:hover .video-overlay .play-button svg {
        fill: var(--accent-color);
    }
</style>

<?php endif; ?>

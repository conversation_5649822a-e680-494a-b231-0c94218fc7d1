<?php
if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

// Angenommen $block enthält deine Datenstruktur
$flexibleContent = $block['flexibleContent'] ?? []; // Sicherstellen, dass flexibleContent existiert

// get length of flexibleContent
$flexibleContentLength = count($flexibleContent);

// save vertical alignment in var 
// align-items: ' . $verticalAlignment'
$verticalAlignment = $block['verticalAlignment'] ?? 'normal';
?>

<div id="<?php echo esc_attr( $block['id'] ); ?>" class="block-inner flexible-content">
    <!-- Text Content -->
    <div class="text-content">
        <?php if ( ! empty( $block['tagline'] ) ) : ?>
            <p class="tagline"><?php echo wp_kses_post( $block['tagline'] ); ?></p>
        <?php endif; ?>

        <?php if ( ! empty( $block['headline'] ) ) : ?>
            <h2><?php echo wp_kses_post( $block['headline'] ); ?></h2>
        <?php endif; ?>
        
        <?php if ( ! empty( $block['paragraph'] ) ) : ?>
            <p class="paragraph"><?php echo wp_kses_post( $block['paragraph'] ); ?></p> 
        <?php endif; ?>

    </div>


    <?php if ( ! empty( $flexibleContent ) ) : ?> 
        <div class="flexible-content-wrapper" style="<?php echo 'grid-template-columns: repeat(' . $flexibleContentLength . ', 1fr); align-items: ' . $verticalAlignment . ';' ?>">
            <?php foreach ($flexibleContent as $contentRow) : ?>
                <div class="col">
                    <?php foreach ($contentRow as $content) : ?>
                        <?php switch ($content['type']) :
                            case 'textEditor';
                                echo '<div class="content-item tinymce-output">' . wp_kses_post($content['content']) . '</div>';
                                break;
                            case 'image';
                                if(isset($content['styles']) && $content['styles']['useImageMask']) {
                                    $unique_id = uniqid('uid-');
                                    echo '<div class="content-item image image-mask">';
                                        echo '<svg 
                                                class="image-mask-svg animation image-mask-svg-' . $block['id'] . '-' . $unique_id . '" 
                                                id="image-mask-svg-' . $block['id'] . '-' . $unique_id . '" 
                                                xmlns="http://www.w3.org/2000/svg" 
                                                xmlns:xlink="http://www.w3.org/1999/xlink" 
                                                viewBox="0 0 116 100" 
                                                style="max-width: 100%; 
                                                max-height: unset;
                                                position: relative;
                                                z-index: 1;">
                                                    <defs>
                                                        <mask id="mask-1-' . $block['id'] . '-' . $unique_id . '">
                                                            <path 
                                                                class="popup-anim path-anim" 
                                                                d="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                                                                data-path-to="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" 
                                                                data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z"
                                                                fill="#fff" 
                                                                data-svg-origin="51 45.5" 
                                                                transform="matrix(1,0,0,1,0,0)" 
                                                                style="translate: none; rotate: none; scale: none; transform-origin: 0px 0px;"
                                                            >
                                                            </path>
                                                        </mask>
                                                    </defs>
                                                    <!-- Das Bild in ein eigenes SVG-Element wrappen -->
                                                    <svg 
                                                        xmlns="http://www.w3.org/2000/svg" 
                                                        viewBox="0 0 116 100" 
                                                        mask="url(#mask-1-' . $block['id'] . '-' . $unique_id . ')" 
                                                        style=position: relative; width: 100%; height: auto; top: 0px; left: 0px;"
                                                    >
                                                        <image 
                                                            xlink:href="' . $content['mediaUrl'] . '" 
                                                            style="y: ' . $content['styles']['imageMaskTranslateY'] . '; x: ' . $content['styles']['imageMaskTranslateX'] . '; scale: ' . $content['styles']['imageMaskScale'] . '; width: 100%;"
                                                        >
                                                        </image>
                                                    </svg>
                                                </svg>';
                                    echo '</div>';
                                } else {
                                    echo '<div class="content-item image"><img src="' . $content['mediaUrl'] . '" alt="Image"></div>';
                                }
                                break;
                            case 'headline':
                                echo '<div class="content-item headline">';
                                    echo '<h2 style="margin-bottom: 0">' . wp_kses_post($content['content']) . '</h2>';
                                echo '</div>';
                                break;
                            case 'paragraph':
                                echo '<div class="content-item paragraph">';
                                    echo '<p class="intro">' . wp_kses_post($content['content']) . '</p>';
                                echo '</div>';
                                break;
                            case 'icon-with-text':
                                echo '<div class="content-item icon-with-text">';
                                    if($content['useSvgCode']) {
                                        echo '<div class="icon">' . $content['svgCode'] . '</div>';
                                    } else {
                                        echo '<div class="icon"><img src="' . $content['mediaUrl'] . '" alt="' . $content['alt'] . '"></div>';
                                    }
                                    echo '<div class="text">' . $content['content'] . '</div>';
                                echo '</div>';
                                break;
                            case 'list':
                                echo '<div class="content-item list">';
                                    echo '<ul>';
                                    foreach ($content['listItems'] as $listItem) {
                                        echo '<li>' . wp_kses_post($listItem['content']) . '</li>';
                                    }
                                    echo '</ul>';
                                echo '</div>';
                                break;
                            case 'accordion':
                                ?>
                                <div class="content-item q-and-a">
                                    <div class="grid grid-start">
                                        <div class="sticky-text">
                                            <?php 
                                                $i = 1;
                                                foreach ($content['accordionItems'] as $item) : 
                                            ?>
                                                <!-- <p><?php echo $i ?></p> -->
                                                <div class="single-q-and-a <?php echo $i !== 1 ? 'collapsed' : ''; ?>">
                                                    <div class="content-wrapper">
                                                        <?php if (!empty($item['question'])) : ?>
                                                            <h4><?php echo wp_kses_post($item['question']); ?></h4>
                                                        <?php endif; ?>
                                                        <?php if (!empty($item['answer'])) : ?>
                                                            <p style="<?php echo $i !== 1 ? 'height: 0;' : ''; ?>">
                                                                <?php echo wp_kses_post($item['answer']); ?>
                                                            </p>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="button-wrapper">
                                                        <button class="toggle-button">
                                                            <!-- <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14m-7 7V5"/>
                                                            </svg> -->
                                                            <span class="expand-icon-elem"></span>
                                                            <span class="expand-icon-elem"></span>
                                                        </button>
                                                    </div>
                                                </div>
                                            <?php $i++; endforeach; ?>
                                        </div>
                                    </div>
                                </div>
                                <?php
                                // set var to make sure to include style and js only ones
                                $hasAccordion = false;
                                if($hasAccordion === false) {
                                    ?>
                                    <style>
                                        .single-q-and-a {
                                            padding: 1rem 0 0 0;
                                            border-bottom: solid 1px #EBEEF1;
                                            display: flex;
                                            gap: 2rem;
                                        }
                                        .single-q-and-a:first-child {
                                            border-top: solid 1px #EBEEF1;
                                        }

                                        .single-q-and-a .content-wrapper {
                                            flex-grow: 1;
                                        }

                                        .single-q-and-a h4,
                                        .single-q-and-a .button-wrapper {
                                            cursor: pointer;
                                        }


                                        .single-q-and-a h4,
                                        .single-q-and-a p {
                                            margin: 0 0 1rem 0;
                                        }
                                        .single-q-and-a p {
                                            overflow: hidden;
                                            /* height: 300px; */
                                            transition: all 0.66s cubic-bezier(0.77,0.2,0.05,1.0);
                                        }

                                        .single-q-and-a.collapsed p {
                                            height: 0;
                                            margin: 0;
                                        }


                                        .single-q-and-a .button-wrapper {
                                            display: flex;
                                            justify-content: flex-end;
                                        }

                                        .single-q-and-a .button-wrapper .toggle-button {
                                            outline: none;
                                            border: none;
                                            background: transparent;
                                            display: flex;
                                            align-items: center;
                                            gap: 1rem;
                                            width: 2rem;
                                            height: 2rem;
                                            text-align: center;

                                            cursor: pointer;
                                        }

                                        .single-q-and-a .button-wrapper .expand-icon-elem {
                                            width: 28px;
                                            height: 4px;
                                            display: absolute;
                                            position: absolute;
                                            background: var(--accent-color-secondary);
                                            border-radius: 999px;
                                            transform: rotate(0deg);

                                            transition: all 0.66s cubic-bezier(0.77,0.2,0.05,1.0);
                                        }
                                        .single-q-and-a.collapsed .button-wrapper .expand-icon-elem {
                                            transform: rotate(180deg);
                                        }

                                        .single-q-and-a .button-wrapper .expand-icon-elem:first-child {
                                            transform: rotate(0deg);
                                        }
                                        .single-q-and-a.collapsed .button-wrapper .expand-icon-elem:first-child {
                                            transform: rotate(90deg);
                                        }

                                    </style>

                                    <script>
                                        // debugger;

                                        const accordionItems = document.querySelectorAll('.single-q-and-a');
                                        accordionItems.forEach((item, index) => {
                                            const toggleButton = item.querySelector('.toggle-button');
                                            const question = item.querySelector('.content-wrapper h4');
                                            const answer = item.querySelector('.content-wrapper p');

                                            if(index === 0) {
                                                answer.style.height = `${answer.scrollHeight}px`;
                                            }

                                            toggleButton.addEventListener('click', () => {
                                                toggleAccordionItems(index);
                                            })

                                            question.addEventListener('click', () => {
                                                toggleAccordionItems(index);
                                            })
                                        })

                                        function toggleAccordionItems(index, answer) {
                                            accordionItems.forEach((item, i) => {
                                                const answer = item.querySelector('.content-wrapper p');
                                                const scrollHeight = answer.scrollHeight;

                                                const isItemCollapsed = item.classList.contains('collapsed');

                                                if(i === index) {
                                                    if (!isItemCollapsed) {
                                                        item.classList.add('collapsed');
                                                        answer.style.height = 0;
                                                    } else {
                                                        item.classList.remove('collapsed');
                                                        answer.style.height = `${scrollHeight}px`;
                                                    }
                                                } else {
                                                    item.classList.add('collapsed');
                                                    answer.style.height = 0;
                                                }
                                            })
                                        }
                                    </script>
                                    <?php
                                    // Setze die Variable auf true, damit das Akkordeon-Stylesheet und Script nur einmal eingefügt werden
                                    $hasAccordion = true;
                                }

                                break;
                            case 'button':
                                $data_modal = isset($content['useModal']) && $content['useModal'] ? 'data-modal-class="' . $content['link'] . '"' : '';

                                echo '<div class="content-item button">';
                                    echo '<a href="'. wp_kses_post($content['link']) . '" ' . $data_modal . ' class="cta-button" style="padding-bottom: 40px;">';
                                        echo '<div>';
                                            echo wp_kses_post($content['content']);
                                        echo '</div>';
                                    echo '</a>';
                                echo '</div>';
                                break;
                        endswitch; ?>
                    <?php endforeach; ?>
                </div>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>


<style>
    .content-item {
        padding-bottom: 2rem;
    }
    .content-item:last-child {
        padding-bottom: 0;
    }

    .flexible-content .text-content h2 {
        margin-bottom: 1rem;
    }

    .flexible-content .text-content p.paragraph {
        margin-bottom: 40px;
        /* new style from screendesign */
        color: var(--gray-40);
        font-size: 20px;
        font-weight: 400;
        font-style: Regular;
        letter-spacing: 0px;
        text-align: left;
        line-height: 38px;
    }
    .flexible-content .flexible-content-wrapper {
        display: grid;
        gap: 36px;
    }

    @media screen and (max-width: 999px) {
        .flexible-content .flexible-content-wrapper {
            grid-template-columns: repeat(1, 1fr) !important;
        }
    }


    /** ICON WITH TEXT */
    .flexible-content .icon-with-text {
        display: flex;
        align-items: normal;
        gap: 1rem;
        align-items: center;
    }

    .content-item.icon-with-text > div.icon {
        max-width: 50%;
        max-width: 48px;
    }


</style>
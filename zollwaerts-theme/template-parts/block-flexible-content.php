<?php
if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

/** 
 * $theme_settings_json *
 */
// TODO: unbedingt in page.php auslagern und variable übergeben 
// Holen der gespeicherten JSON-Daten aus den Post-Metadaten
$theme_settings_json = get_option('grenzlotsen_page_builder_content');

// echo '<p>theme_settings_json:' . $theme_settings_json . '</p>';

// Verwenden von wp_unslash, um unnötige Escape-Zeichen zu entfernen
$unslashed_theme_settings = wp_unslash($theme_settings_json);

// echo '<p>unslashed_data:' . $unslashed_data . '</p>';

// Dekodieren der JSON-Daten in ein PHP-Array
$theme_settings_array = json_decode($unslashed_theme_settings, true); 
/**
 * End $theme_settings_array
 */


// Angenommen $block enthält deine Datenstruktur
$flexibleContent = $block['flexibleContent'] ?? []; // Sichers<PERSON>len, dass flexibleContent existiert

// get length of flexibleContent
$flexibleContentLength = count($flexibleContent);

// save appearance in var
$appearance = 'default';
if (isset($block['advancedSettings']) && isset($block['advancedSettings']['appearance'])) {
    $appearance = $block['advancedSettings']['appearance'];
}

// Save vertical alignment in var 
$verticalAlignment = $block['verticalAlignment'] ?? 'normal';
?>

<div id="<?php echo esc_attr( $block['id'] ); ?>" class="block-inner flexible-content <?php echo $appearance; ?>">
    <!-- Text Content -->
    <?php if ( ! empty( $block['tagline'] || ! empty( $block['headline'] ) || ! empty( $block['paragraph'] )) ) : ?>
        <div class="text-content">
            <?php if ( ! empty( $block['tagline'] ) ) : ?>
                <p class="tagline"><?php echo wp_kses_post( $block['tagline'] ); ?></p>
            <?php endif; ?>

            <?php if ( ! empty( $block['headline'] ) ) : ?>
                <h2><?php echo wp_kses_post( $block['headline'] ); ?></h2>
            <?php endif; ?>
            
            <?php if ( ! empty( $block['paragraph'] ) ) : ?>
                <p class="paragraph"><?php echo wp_kses_post( $block['paragraph'] ); ?></p> 
            <?php endif; ?>

        </div>
    <?php endif; ?>


    <?php if ( ! empty( $flexibleContent ) ) : ?> 
        <?php
        $reverseColumnsOnMobile = '';
        if(isset($block['reverseColumnsOnMobile']) && $block['reverseColumnsOnMobile']) {
            $reverseColumnsOnMobile = 'reverse-columns-on-mobile';
        }

        $equalHeightColumns = 'true';
        // set equalHeightColumns to false if nessary
        if(isset($block['equalHeightColumns']) && $block['equalHeightColumns']) {
            $equalHeightColumns = 'false';
        }

        if(isset($block['colSettings'])) {
            // print the colSettings
            // echo '<pre>' . print_r($block['colSettings'], true) . '</pre>';


            // Verwandle colSettings in einen String für die CSS grid-template-columns Eigenschaft
            $gridTemplateColumns = implode(' ', array_map(function($col) {
                return isset($col['width']) && $col['width'] ? "{$col['width']}fr" : '1fr';
            }, $block['colSettings']));

            // echo '<style>.flexible-content-wrapper {grid-template-columns: ' . $gridTemplateColumns . ';}</style>';
        }

        
        ?>
        <div class="flexible-content-wrapper <?php echo $reverseColumnsOnMobile ?>" style="<?php echo 'grid-template-columns: repeat(' . $flexibleContentLength . ', 1fr); grid-template-columns: ' . $gridTemplateColumns . '; align-items: ' . $verticalAlignment . ';' ?>">
            <?php foreach ($flexibleContent as $index => $contentCol) : ?>
                <?php
                    $random_id = generate_random_id('nw-', 16);
                    
                    // Zugriff auf colSettings über den gleichen Index wie flexibleContent
                    if (isset($block['colSettings'][$index])) {
                        
                        $colSetting = $block['colSettings'][$index]; // Zugriff auf colSettings mit dem gleichen Index

                        $colStyle = $colSetting['style'] ?? 'default';
                        $isContentCentered = isset($colSetting['isContentCentered']) && $colSetting['isContentCentered'];

                        if($colStyle === 'default') {
                            $background = 'none';

                            // margin 
                            $marginTop = 0;
                            $marginRight = 0;
                            $marginBottom = 0;
                            $marginLeft = 0;
                            $margin = "{$marginTop} {$marginRight} {$marginBottom} {$marginLeft}";

                            // padding
                            $paddingTop = 0;
                            $paddingRight = 0;
                            $paddingBottom = 0;
                            $paddingLeft = 0;
                            $padding = "{$paddingTop} {$paddingRight} {$paddingBottom} {$paddingLeft}";
                            
                            // translate
                            $translateX = 0;
                            $translateY = 0;
                            $translate = "{$translateX} {$translateY}";
                            
                            // border
                            $borderStyle = 'none';
                            $borderWidth = '';
                            $borderColor = '';
                            $border = "{$borderStyle} {$borderWidth} {$borderColor}";

                            // border radius
                            $borderTopLeftRadius = '0px';
                            $borderTopRightRadius = '0px';
                            $borderBottomLeftRadius = '0px';
                            $borderBottomRightRadius = '0px';
                            $borderRadius = "{$borderTopLeftRadius} {$borderTopRightRadius} {$borderBottomLeftRadius} {$borderBottomRightRadius}";

                            $boxShadow = 'none';
                        }

                        if($colStyle === 'shadow-border') {
                            $background = '#ffffff';

                            // margin 
                            $marginTop = 0;
                            $marginRight = 0;
                            $marginBottom = 0;
                            $marginLeft = 0;
                            $margin = "{$marginTop} {$marginRight} {$marginBottom} {$marginLeft}";

                            $paddingTop = '2rem';
                            $paddingRight = '2rem';
                            $paddingBottom = '2rem';
                            $paddingLeft = '2rem';
                            $padding = "{$paddingTop} {$paddingRight} {$paddingBottom} {$paddingLeft}";

                            // translate
                            $translateX = 0;
                            $translateY = 0;
                            $translate = "{$translateX} {$translateY}";

                            // border
                            $borderStyle = 'solid';
                            $borderWidth = 1;
                            $borderColor = 'var(--gray-20)';
                            $border = "{$borderStyle} {$borderWidth} {$borderColor}";

                            // border radius
                            $borderTopLeftRadius = '1rem';
                            $borderTopRightRadius = '1rem';
                            $borderBottomLeftRadius = '1rem';
                            $borderBottomRightRadius = '1rem';
                            $borderRadius = "{$borderTopLeftRadius} {$borderTopRightRadius} {$borderBottomLeftRadius} {$borderBottomRightRadius}";
                            
                            // boxShadow
                            // 0px 2px 12px 0 rgba(0,0,0,0.1)
                            $boxShadowX = 0;
                            $boxShadowY = 2;
                            $boxShadowBlur = 12;
                            $boxShadowSpread = 0;
                            $boxShadowColor = 'rgba(0,0,0,0.1)';
                            $boxShadow = "{$boxShadowX}px {$boxShadowY}px {$boxShadowBlur}px {$boxShadowSpread}px {$boxShadowColor}";
                        }

                        // Hintergrundfarbe oder Hintergrundverlauf
                        if(isset($colSetting['background']) && isset($colSetting['background']['useBackground']) && $colSetting['background']['useBackground']) {
                            if ($colSetting['background']['useGradient']) {
                                if($colSetting['background']['gradient']['gradientType'] === 'linear') {
                                    if(!empty($colSetting['background']['gradient']['position1']) && !empty($colSetting['background']['gradient']['position2'])) {
                                        $angle = $colSetting['background']['gradient']['angle'];
                                        $bgcolor1 = $colSetting['background']['gradient']['color1'];
                                        $bgcolor2 = $colSetting['background']['gradient']['color2'];
                                        $position1 = $colSetting['background']['gradient']['position1'] . '%';
                                        $position2 = $colSetting['background']['gradient']['position2'] . '%';
                                        $background = "linear-gradient({$angle}deg, {$bgcolor1} {$position1}, {$bgcolor2} {$position2})";
                                    } else {
                                        $angle = $colSetting['background']['gradient']['angle'];
                                        $bgcolor1 = $colSetting['background']['gradient']['color1'];
                                        $bgcolor2 = $colSetting['background']['gradient']['color2'];
                                        $background = "linear-gradient({$angle}deg, {$bgcolor1}, {$bgcolor2})";
                                    }
                                }
                                if ($colSetting['background']['gradient']['gradientType'] === 'radial') {
                                    if(isset($colSetting['background']['gradient']['position1']) && isset($colSetting['background']['gradient']['position2'])) {
                                        $bgcolor1 = $colSetting['background']['gradient']['color1'];
                                        $bgcolor2 = $colSetting['background']['gradient']['color2'];
                                        $circlePositionX = $colSetting['background']['gradient']['circlePositionX'] ?? 'center';
                                        $circlePositionY = $colSetting['background']['gradient']['circlePositionY'] ?? 'center';
                                        $position1 = $colSetting['background']['gradient']['position1'] . '%';
                                        $position2 = $colSetting['background']['gradient']['position2'] . '%';
                                        $background = "radial-gradient(circle at {$circlePositionX}% {$circlePositionY}%, {$bgcolor1} {$position1}, {$bgcolor2} {$position2})";
                                    } else {
                                        $bgcolor1 = $colSetting['background']['gradient']['color1'];
                                        $bgcolor2 = $colSetting['background']['gradient']['color2'];
                                        $background = "radial-gradient(circle at center, {$bgcolor1}, {$bgcolor2})";
                                    }
                                }
                            } else {
                                $background = $colSetting['background']['color'];
                                if (!empty($colSetting['background']['backgroundImage'])) {
                                    $background = "url({$colSetting['background']['backgroundImage']}) {$colSetting['background']['backgroundPosition']}";
                                }
                            }
                        } 

                        // Text Color
                        if (isset($colSetting['background']['forceTextColor']) && $colSetting['background']['forceTextColor']) {
                            $textColor = $colSetting['background']['textColor'];

                            echo '<style>
                                .col.' . $random_id . ' .content-item h1,
                                .col.' . $random_id . ' .content-item h2,
                                .col.' . $random_id . ' .content-item h3,
                                .col.' . $random_id . ' .content-item h4,
                                .col.' . $random_id . ' .content-item h5,
                                .col.' . $random_id . ' .content-item h6,
                                .col.' . $random_id . ' .content-item p,
                                .col.' . $random_id . ' .content-item a,
                                .col.' . $random_id . ' .content-item label,
                                .col.' . $random_id . ' .content-item span,
                                .col.' . $random_id . ' .content-item button {
                                    color: ' . $textColor . ';
                                }
                            </style>';
                        }

                        // Margin und Padding
                        if (isset($colSetting['margin']) && $colSetting['margin']) {
                            $marginTop = $colSetting['margin']['top'] ? $colSetting['margin']['top'] : $marginTop;
                            $marginRight = $colSetting['margin']['right'] ? $colSetting['margin']['right'] : $marginRight;
                            $marginBottom = $colSetting['margin']['bottom'] ? $colSetting['margin']['bottom'] : $marginBottom;
                            $marginLeft = $colSetting['margin']['left'] ? $colSetting['margin']['left'] : $marginLeft;

                            $margin = "{$marginTop} {$marginRight} {$marginBottom} {$marginLeft}";
                        } 

                        if (isset($colSetting['padding']) && $colSetting['padding']) {
                            $paddingTop = $colSetting['padding']['top'] ? $colSetting['padding']['top'] : $paddingTop;
                            $paddingRight = $colSetting['padding']['right'] ? $colSetting['padding']['right'] : $paddingRight;
                            $paddingBottom = $colSetting['padding']['bottom'] ? $colSetting['padding']['bottom'] : $paddingBottom;
                            $paddingLeft = $colSetting['padding']['left'] ? $colSetting['padding']['left'] : $paddingLeft;
                        
                            $padding = "{$paddingTop} {$paddingRight} {$paddingBottom} {$paddingLeft}";
                            // $padding = "{$colSetting['padding']['top']} {$colSetting['padding']['right']} {$colSetting['padding']['bottom']} {$colSetting['padding']['left']}";
                        }

                        if(isset($colSetting['translate']) && $colSetting['translate']) {
                            $translateX = $colSetting['translate']['x'] ? $colSetting['translate']['x'] : $translateX;
                            $translateY = $colSetting['translate']['y'] ? $colSetting['translate']['y'] : $translateY;
                            $translate = "{$translateX} {$translateY}";
                        }

                        // Border
                        if(isset($colSetting['border']) && $colSetting['border']) {
                            // if(isset($colSetting['border']['style']) && $colSetting['border']['style'] && $colSetting['border']['style'] === 'none') {
                            //     $border = 'none';
                            // }
                            if(isset($colSetting['border']['style']) && $colSetting['border']['style'] !== 'none') {
                                $borderStyle = $colSetting['border']['style'] ? $colSetting['border']['style'] : $borderStyle;
                                $borderWidth = $colSetting['border']['width'] ? $colSetting['border']['width'] : $borderWidth;
                                $borderColor = $colSetting['border']['color'] ? $colSetting['border']['color'] : $borderColor;;
                                $border = "{$borderStyle} {$borderWidth}px {$borderColor}";
                            }
                        }
                        if($colSetting['borderRadius']) {
                            $borderTopLeftRadius = $colSetting['borderRadius']['topLeft'] ? $colSetting['borderRadius']['topLeft'] : $borderTopLeftRadius;
                            $borderTopRightRadius = $colSetting['borderRadius']['topRight'] ? $colSetting['borderRadius']['topRight'] : $borderTopRightRadius;
                            $borderBottomLeftRadius = $colSetting['borderRadius']['bottomLeft'] ? $colSetting['borderRadius']['bottomLeft'] : $borderBottomLeftRadius;
                            $borderBottomRightRadius = $colSetting['borderRadius']['bottomRight'] ? $colSetting['borderRadius']['bottomRight'] : $borderBottomRightRadius;

                            $borderRadius = "{$borderTopLeftRadius} {$borderTopRightRadius} {$borderBottomLeftRadius} {$borderBottomRightRadius}";
                        }

                        // Box Shadow
                        if(isset($colSetting['boxShadow']) && $colSetting['useBoxShadow']) {
                            $boxShadowX = isset($colSetting['boxShadow']['x']) && $colSetting['boxShadow']['x'] ? $colSetting['boxShadow']['x'] : $boxShadowX;
                            $boxShadowY = isset($colSetting['boxShadow']['y']) && $colSetting['boxShadow']['y'] ? $colSetting['boxShadow']['y'] : $boxShadowY;
                            $boxShadowBlur = isset($colSetting['boxShadow']['blur']) && $colSetting['boxShadow']['blur'] ? $colSetting['boxShadow']['blur'] : $boxShadowBlur;
                            $boxShadowSpread = isset($colSetting['boxShadow']['spread']) && $colSetting['boxShadow']['spread'] ? $colSetting['boxShadow']['spread'] : $boxShadowSpread;
                            $boxShadowColor = isset($colSetting['boxShadow']['color']) && $colSetting['boxShadow']['color'] ? $colSetting['boxShadow']['color'] : $boxShadowColor;
                            $boxShadow = "0 {$boxShadowX}px {$boxShadowY}px {$boxShadowBlur}px {$boxShadowSpread}px {$boxShadowColor}";
                        } 

                    } else {
                        // Fallback für den Fall, dass kein colSetting für diesen Index existiert
                        $background = '#ffffff';
                        $margin = '0px';
                        $padding = '0px';
                        $translate = '0px 0px';
                        $border = 'none';
                    }
                ?>

                <? if($equalHeightColumns == 'false') : ?>
                <div class="col-wrapper">
                <?php endif; ?>
                    <div 
                        class="col <?php echo $random_id; ?>" 
                        style="background: <?php echo $background; ?>; margin: <?php echo $margin; ?>; padding: <?php echo $padding; ?>; transform: <?php echo $translate; ?>; border: <?php echo $border; ?>; border-radius: <?php echo $borderRadius; ?>; box-shadow: <?php echo $boxShadow; ?>; text-align: <?php echo $isContentCentered ? 'center' : 'unset'; ?>"
                    >
                        
                        <?php
                        // // print $colSettings[$index] as JSON 
                        // echo '<h1>' . print_r($index, true) . '</h1>';
                        // echo '<pre>' . print_r($block['colSettings'], true) . '</pre>';
                        // echo '<pre>' . print_r($block['colSettings'][$index], true) . '</pre>';
                        ?>
                        
                        <?php foreach ($contentCol as $content) : ?>
                            <?php switch ($content['type']) :
                                case 'textEditor';
                                    echo '<div class="content-item tinymce-output">' . wp_kses_post($content['content']) . '</div>';
                                    break;
                                case 'CTAs':
                                    echo '<div class="content-item cta-module" style="flex-direction: ' . $content['direction'] . ';">';
                                    
                                    foreach ($content['ctaArray'] as $cta) {
                                        $target = $cta['openInNewTab'] ? ' target="_blank"' : '';
                                        $arrow = $cta['useArrow'] ? '
                                                                    <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 12H5m14 0-4 4m4-4-4-4"></path>
                                                                    </svg>' : '';
                                        //'<span class="cta-arrow">&#8594;</span>' : '';
                                        $appearanceClass = 'cta-' . $cta['appearance'];
                                        
                                        if ($cta['useModal'] && !empty($cta['modalId'])) {
                                            echo '<button class="cta-button ' . $appearanceClass . '" data-modal-id="' . $cta['modalId'] . '">';
                                                echo '<div>';
                                                    echo '<span class="cta-content">' . $cta['content'] . '</span>';
                                                    echo $arrow;
                                                echo '</div>';
                                            echo '</button>';
                                        } else {
                                            echo '<a href="' . $cta['link'] . '" class="cta-button ' . $appearanceClass . '"' . $target . '>';
                                                echo '<div>';
                                                    echo '<span class="cta-content">' . $cta['content'] . '</span>';
                                                    echo $arrow;
                                                echo '</div>';
                                            echo '</a>';
                                        }
                                    }
                                    
                                    echo '</div>';
                                    break;
                                case 'html-form';
                                    echo '<div class="content-item">';
                                    if(isset($content['htmlFormId']) && $content['htmlFormId']) {
                                        $id_to_find = $content['htmlFormId'];
                                        $form = find_form_by_id($theme_settings_array, $id_to_find);
                                        if (!empty($form)) {
                                            echo '<div>' . $form['html'] . '</div>';
                                        }
                                        // echo '<pre>' . print_r($content['htmlFormId'], true) . '</pre>';
                                    }
                                    echo '</div>';
                                    break;
                                case 'highlighted-text';
                                    //  collect styleSettings
                                    if(isset($content['styleSettings']) && $content['styleSettings']) {
                                        $fontSize = $content['styleSettings']['fontSize'] ? $content['styleSettings']['fontSize'] : '1rem';
                                        $fontWeight = $content['styleSettings']['fontWeight'] ? $content['styleSettings']['fontWeight'] : '400';
                                        $color = $content['styleSettings']['color'] ? $content['styleSettings']['color'] : '#000000';
                                        $textAlign = $content['styleSettings']['textAlign'] ? $content['styleSettings']['textAlign'] : 'left';
                                    } else {
                                        $fontSize = '1rem';
                                        $fontWeight = '400';
                                        $color = '#000000';
                                        $textAlign = 'left';
                                    }
                                    // collect textAnimationSettings
                                    if(isset($content['textAnimationSettings']) && $content['textAnimationSettings'] && $content['textAnimationSettings']['useAnimation']) {
                                        $textAnimationType = $content['textAnimationSettings']['animationType'] ? $content['textAnimationSettings']['animationType'] : 'none';
                                        $textAnimationDuration = $content['textAnimationSettings']['animationDuration'] ? $content['textAnimationSettings']['animationDuration'] : 1000;
                                        $textAnimationStartValue = $content['textAnimationSettings']['animationStartValue'] ? $content['textAnimationSettings']['animationStartValue'] : 0;
                                    } else {
                                        $textAnimationType = false;
                                        $textAnimationDuration = false;
                                    }

                                    // collect spacingSettings
                                    if(isset($content['spacingSettings']) && $content['spacingSettings']) {
                                        $paddingTop = $content['spacingSettings']['padding']['top'] ? $content['spacingSettings']['padding']['top'] : 0;
                                        $paddingRight = $content['spacingSettings']['padding']['right'] ? $content['spacingSettings']['padding']['right'] : 0;
                                        $paddingBottom = $content['spacingSettings']['padding']['bottom'] ? $content['spacingSettings']['padding']['bottom'] : 0;
                                        $paddingLeft = $content['spacingSettings']['padding']['left'] ? $content['spacingSettings']['padding']['left'] : 0;
                                    } else {
                                        $paddingTop = 0;
                                        $paddingRight = 0;
                                        $paddingBottom = 0;
                                        $paddingLeft = 0;
                                    }
                                    $padding = "{$paddingTop} {$paddingRight} {$paddingBottom} {$paddingLeft}";


                                    echo '<div 
                                            class="content-item highlighted-text" 
                                            style="font-size: ' . $fontSize . '; font-weight: ' . $fontWeight . '; color: ' . $color . '; text-align: ' . $textAlign . '; padding: ' . $padding . ';"
                                            data-text-value="' . $content['content'] . '"
                                            data-text-animation-type="' . $textAnimationType . '"
                                            data-text-animation-duration="' . $textAnimationDuration . '"
                                            data-text-animation-start-value="' . $textAnimationStartValue . '"
                                        >';
                                    echo wp_kses_post($content['content']);
                                    echo '</div>';


                                    // Variable zum Überprüfen, ob das Script bereits eingebunden wurde
                                    global $highlighted_text_script_included;
                                    if ( ! isset( $highlighted_text_script_included ) ) {
                                        $highlighted_text_script_included = false;
                                    }

                                    if ( ! $highlighted_text_script_included ) : ?>

                                        <script>
                                            // Helper Function: Intersection Observer 
                                            const createObserver = (element, animationType, animationDuration, animationStartValue) => {
                                                const observer = new IntersectionObserver((entries, observer) => {
                                                    entries.forEach(entry => {
                                                        if (entry.isIntersecting) {
                                                            animateText(element, animationType, animationDuration, animationStartValue);
                                                            observer.unobserve(element); // Stoppe den Observer nach der ersten Animation
                                                        }
                                                    });
                                                });

                                                observer.observe(element); // Startet den Observer
                                            };

                                            // Helper function: easeInOutQuad (non-linear timing function)
                                            function easeInOutQuad(t, b, c, d) {
                                                t /= d / 2;
                                                if (t < 1) return c / 2 * t * t + b;
                                                t--;
                                                return -c / 2 * (t * (t - 2) - 1) + b;
                                            }

                                            // Main counter function
                                            function animateCounter(animationTarget, start, end, duration, prefix = '', suffix = '') {
                                                console.log(`animateCounter -> animationTarget: ${animationTarget}, start : ${start}, end : ${end}, duration : ${duration}`);

                                                let startTime = null;
                                                const isCountingDown = start > end; // Bestimme, ob rückwärts gezählt wird
                                                const totalChange = Math.abs(end - start); // Immer positive Änderung für die easing function

                                                function updateCounter(timestamp) {
                                                    if (!startTime) startTime = timestamp;
                                                    const timeElapsed = timestamp - startTime;

                                                    // Berechne den nächsten Wert mit der easing function
                                                    let progress = easeInOutQuad(timeElapsed, 0, totalChange, duration);

                                                    // Wenn rückwärts gezählt wird, subtrahiere den Fortschritt vom Startwert
                                                    let nextValue = isCountingDown ? start - progress : start + progress;

                                                    // Sicherstellen, dass wir den Endwert nicht überschreiten (nach oben oder unten)
                                                    if (isCountingDown) {
                                                        nextValue = Math.max(nextValue, end); // Beim Runterzählen: Endwert nicht unterschreiten
                                                    } else {
                                                        nextValue = Math.min(nextValue, end); // Beim Hochzählen: Endwert nicht überschreiten
                                                    }

                                                    // Setze den Textinhalt mit Prefix und Suffix
                                                    animationTarget.textContent = `${prefix}${Math.floor(nextValue)}${suffix}`;

                                                    // Füge einen visuellen Effekt hinzu
                                                    animationTarget.classList.add('grow');

                                                    // Entferne den Effekt nach einer kurzen Verzögerung
                                                    setTimeout(() => {
                                                        animationTarget.classList.remove('grow');
                                                    }, 300);

                                                    // Fortsetze die Animation, wenn die Dauer noch nicht erreicht ist
                                                    if (timeElapsed < duration) {
                                                        requestAnimationFrame(updateCounter);
                                                    } else {
                                                        // Finales Ergebnis
                                                        animationTarget.textContent = `${prefix}${end}${suffix}`;
                                                    }
                                                }

                                                // Starte die Animation
                                                requestAnimationFrame(updateCounter);
                                            }

                                            const animateText = (animationTarget, animationType, animationDuration, animationStartValue) => {
                                                if (animationType === 'count') {
                                                    // Extrahiere den Text und finde die Zahl sowie die Zeichen davor und danach
                                                    const fullText = animationTarget.textContent.trim();
                                                    const numberMatch = fullText.match(/(\D*)(\d+)(\D*)/); // Findet Präfix, Zahl und Suffix

                                                    if (numberMatch) {
                                                        const prefix = numberMatch[1] || ''; // Zeichen vor der Zahl (z.B. "$")
                                                        const number = parseInt(numberMatch[2], 10); // Die Zahl selbst (z.B. "48")
                                                        const suffix = numberMatch[3] || ''; // Zeichen nach der Zahl (z.B. "%")

                                                        const startValue = animationStartValue ? parseInt(animationStartValue, 10) : 0;
                                                        const endValue = number;

                                                        // console.log('startValue: ', startValue);
                                                        // console.log('endValue: ', endValue);
                                                        // console.log('prefix: ', prefix, 'suffix: ', suffix);

                                                        animateCounter(animationTarget, startValue, endValue, parseInt(animationDuration, 10), prefix, suffix);
                                                    }
                                                }
                                            }

                                            // await for the DOM to be ready
                                            document.addEventListener('DOMContentLoaded', function() {
                                                const highlightedTexts = document.querySelectorAll('.highlighted-text');
                                                highlightedTexts.forEach(function(highlightedText) {
                                                    console.log('highlightedText: ', highlightedText);
                                                    const animationType = highlightedText.getAttribute('data-text-animation-type');
                                                    console.log('animationType: ', animationType);
                                                    const animationDuration = highlightedText.getAttribute('data-text-animation-duration');
                                                    console.log('animationDuration: ', animationDuration);
                                                    const animationStartValue = highlightedText.getAttribute('data-text-animation-start-value');
                                                    console.log('animationStartValue: ', animationStartValue);

                                                    // Erstelle den Intersection Observer für das jeweilige Element
                                                    createObserver(highlightedText, animationType, animationDuration, animationStartValue);
                                                    // animateText(highlightedText, animationType, animationDuration, animationStartValue);
                                                });
                                            });
                                        </script>
                                    <?php 
                                    $highlighted_text_script_included = true; 
                                    endif; 

                                    break;
                                case 'image';
                                    if(isset($content['useImageComposition']) && $content['useImageComposition'] === true) {
                                        // Multi-Image HTML rendern
                                        echo '<div class="content-item image-composition">';
                                        echo '<ul class="multi-image-list">';
                                        foreach ($content['imageComposition'] as $index => $imageCompositionItem) {
                                            $unique_id = uniqid('uid-');
                                            
                                            $rotation = $imageCompositionItem['styles']['imageMaskRotate'] ? $imageCompositionItem['styles']['imageMaskRotate'] : '0';
                                            
                                            $isVideoTeaser = isset($imageCompositionItem['isVideoTeaser']) && $imageCompositionItem['isVideoTeaser'];
                                            $videoData = isset($imageCompositionItem['videoData']) && $imageCompositionItem['videoData'] ? $imageCompositionItem['videoData'] : null;
                                            
                                            $videoId = null;
                                            if($isVideoTeaser && $videoData ) {
                                                $videoId = $videoData['id'];
                                            }

                                            $video_url = '';
                                            if($videoId) {
                                                $video_url = wp_get_attachment_url($videoId);
                                            }
                                            
                                            echo '<li class="multi-image-list-item ' . (($isVideoTeaser && $videoData && $videoId) ? 'contains-video-lightbox' : '') . '" id="' . $unique_id . '">';
                                            
                                            // echo '<pre>';
                                            // echo print_r($isVideoTeaser, true);
                                            // echo print_r($videoData, true);
                                            // echo print_r($imageCompositionItem, true);
                                            // echo '</pre>';


                                            // Je nach Bildtyp rendern
                                            if ($imageCompositionItem['type'] === 'singleImage') {
                                                echo '<div class="image-item">';

                                                if($isVideoTeaser && $videoData) {
                                                    echo '
                                                        <div 
                                                            class="play-btn-outer-wrapper open-video-lightbox-listener"
                                                            data-video-url="' . $video_url . '"
                                                            style="
                                                                    width: ' . $imageCompositionItem['styles']['width'] . '%; 
                                                                    height: ' . $imageCompositionItem['styles']['height'] . '%;
                                                                    aspect-ratio: ' . $imageCompositionItem['styles']['aspectRatio']. ';
                                                                    margin-top: ' . $imageCompositionItem['styles']['marginTop']. '%;
                                                                    margin-right: ' . $imageCompositionItem['styles']['marginRight'] . '%;
                                                                    margin-bottom: -' . $imageCompositionItem['styles']['marginTop'] . '%;
                                                                    margin-left: ' . $imageCompositionItem['styles']['marginLeft'] . '%;
                                                                "
                                                        >
                                                            <div class="play-btn-inner-wrapper">
                                                                <svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="64" height="64" fill="#fff" viewBox="0 0 24 24">
                                                                    <path fill-rule="evenodd" d="M8.6 5.2A1 1 0 0 0 7 6v12a1 1 0 0 0 1.6.8l8-6a1 1 0 0 0 0-1.6l-8-6Z" clip-rule="evenodd"/>
                                                                </svg>
                                                            </div>
                                                        </div>
                                                    ';
                                                }

                                                if ($imageCompositionItem['styles']['useImageMask']) {
                                                    echo '<svg 
                                                            class="image-mask-svg animation image-mask-svg-' . $block['id'] . '-' . $unique_id . '" 
                                                            id="image-mask-svg-' . $block['id'] . '-' . $unique_id . '" 
                                                            xmlns="http://www.w3.org/2000/svg" 
                                                            xmlns:xlink="http://www.w3.org/1999/xlink" 
                                                            viewBox="0 0 116 100"
                                                            style="
                                                                max-width: 100%; 
                                                                position: relative;
                                                                opacity: ' . $imageCompositionItem['styles']['opacity'] . ';
                                                                z-index: ' . $imageCompositionItem['styles']['zIndex'] . ';
                                                                width: ' . $imageCompositionItem['styles']['width'] . '%; 
                                                                height: ' . $imageCompositionItem['styles']['height'] . '%;
                                                                aspect-ratio: ' . $imageCompositionItem['styles']['aspectRatio']. ';
                                                                margin-top: ' . $imageCompositionItem['styles']['marginTop']. '%;
                                                                margin-right: ' . $imageCompositionItem['styles']['marginRight'] . '%;
                                                                margin-bottom: -' . $imageCompositionItem['styles']['marginTop'] . '%;
                                                                margin-left: ' . $imageCompositionItem['styles']['marginLeft'] . '%;
                                                                transform: rotate(' . $rotation . 'deg);
                                                            "
                                                            data-rotation-degrees="' . $rotation . '"
                                                        >
                                                        <!-- transform: rotate(' . $rotation . 'deg); -->
                                                            <defs>
                                                                <mask id="mask-' . $block['id'] . '-' . $unique_id . '">
                                                                    <!-- class="popup-anim path-anim"  -->
                                                                    <path  
                                                                        class="popup-anim path-anim"
                                                                        d="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" 
                                                                        data-path-to="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" 
                                                                        data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                                                                        fill="#fff" 
                                                                        style="translate: none; rotate: none; scale: none; transform-origin: center center;" 
                                                                        data-svg-origin="51 45.5">
                                                                    </path>
                                                                    <!-- transform="rotate(' . $rotation . ')"> -->
                                                                </mask>
                                                            </defs>

                                                            <!-- Das Bild in ein eigenes SVG-Element wrappen -->
                                                            <svg 
                                                                xmlns="http://www.w3.org/2000/svg" 
                                                                viewBox="0 0 116 100" 
                                                                mask="url(#mask-' . $block['id'] . '-' . $unique_id . ')" 
                                                                class="image-wrapper"
                                                                style=position: relative; width: 100%; height: auto; top: 0px; left: 0px;"
                                                            >
                                                                <rect 
                                                                    width="100%" 
                                                                    height="100%" 
                                                                    fill="#ccc" 
                                                                    style="fill: ' . $imageCompositionItem['styles']['color'] . ' ;"
                                                                ></rect>
                                                                <image 
                                                                    xlink:href="' . $imageCompositionItem['mediaUrl'] . '" 
                                                                    y="' . $imageCompositionItem['styles']['imageMaskTranslateY'] . '%"
                                                                    x="' . $imageCompositionItem['styles']['imageMaskTranslateX'] . '%" 
                                                                    transform="scale(' . $imageCompositionItem['styles']['imageMaskScale'] . ') rotate(-' . $rotation . ')" 
                                                                    width="100%"
                                                                >
                                                                </image>
                                                                <!-- rotate(-' . $rotation . ') -->
                                                            </svg>
                                                        </svg>';
                                                    } else {
                                                        echo '<img 
                                                                src="' . $imageCompositionItem['mediaUrl'] . '" 
                                                                style="
                                                                    max-width: 100%; 
                                                                    position: relative;
                                                                    opacity: ' . $imageCompositionItem['styles']['opacity'] . ';
                                                                    z-index: ' . $imageCompositionItem['styles']['zIndex'] . ';
                                                                    width: ' . $imageCompositionItem['styles']['width'] . '%; 
                                                                    height: ' . $imageCompositionItem['styles']['height'] . '%;
                                                                    aspect-ratio: ' . $imageCompositionItem['styles']['aspectRatio']. ';
                                                                    margin-top: ' . $imageCompositionItem['styles']['marginTop']. '%;
                                                                    margin-right: ' . $imageCompositionItem['styles']['marginRight'] . '%;
                                                                    margin-bottom: -' . $imageCompositionItem['styles']['marginTop'] . '%;
                                                                    margin-left: ' . $imageCompositionItem['styles']['marginLeft'] . '%;
                                                                "
                                                            >';
                                                    }
                                                echo '</div>';
                                                
                                            }
                                            // SVG mit Maske
                                            if ($imageCompositionItem['type'] === 'singleColoredSvg') {
                                                echo '<div class="image-item">
                                                        <svg 
                                                            class="image-mask-svg animation image-mask-svg-' . $block['id'] . '-' . $unique_id . '" 
                                                            id="image-mask-svg-' . $block['id'] . '-' . $unique_id . '" 
                                                            class="active" 
                                                            xmlns="http://www.w3.org/2000/svg" 
                                                            xmlns:xlink="http://www.w3.org/1999/xlink" 
                                                            viewBox="0 0 116 100"
                                                            style="
                                                                max-width: 100%; 
                                                                position: relative;
                                                                opacity: ' . $imageCompositionItem['styles']['opacity'] . ';
                                                                z-index: ' . $imageCompositionItem['styles']['zIndex'] . ';
                                                                width: ' . $imageCompositionItem['styles']['width'] . '%; 
                                                                height: ' . $imageCompositionItem['styles']['height'] . '%;
                                                                aspect-ratio: ' . $imageCompositionItem['styles']['aspectRatio']. ';
                                                                margin-top: ' . $imageCompositionItem['styles']['marginTop']. '%;
                                                                margin-right: ' . $imageCompositionItem['styles']['marginRight'] . '%;
                                                                margin-bottom: -' . $imageCompositionItem['styles']['marginTop'] . '%;
                                                                margin-left: ' . $imageCompositionItem['styles']['marginLeft'] . '%;
                                                                transform: rotate(' . $rotation . 'deg);
                                                            " 
                                                            data-rotation-degrees="' . $rotation . '"
                                                        >
                                                            <defs>
                                                                <mask id="mask-' . $block['id'] . '-' . $unique_id . '">
                                                                    <!-- class="popup-anim path-anim"  -->
                                                                    <!--
                                                                        d="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                                                                        data-path-to="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" 
                                                                        data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z"
                                                                    -->
                                                                    <path  
                                                                        class="popup-anim path-anim"
                                                                        d="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" 
                                                                        data-path-to="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" 
                                                                        data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                                                                        fill="#fff" 
                                                                        style="translate: none; rotate: none; scale: none; transform-origin: center center;" 
                                                                        data-svg-origin="51 45.5">
                                                                    </path>
                                                                    <!-- transform="rotate(' . $rotation . ')"> -->
                                                                </mask>
                                                            </defs>

                                                            <!-- Das Bild in ein eigenes SVG-Element wrappen -->
                                                            <svg 
                                                                xmlns="http://www.w3.org/2000/svg" 
                                                                viewBox="0 0 116 100" 
                                                                mask="url(#mask-' . $block['id'] . '-' . $unique_id . ')" 
                                                                class="image-wrapper"
                                                                style=position: relative; width: 100%; height: auto; top: 0px; left: 0px;"
                                                            >
                                                                <rect 
                                                                    width="100%" 
                                                                    height="100%" 
                                                                    fill="#ccc"
                                                                    style="fill: ' . $imageCompositionItem['styles']['color'] . ' ;"
                                                                ></rect>
                                                            </svg>
                                                        </svg>
                                                    </div>';
                                            }
                                            echo '</li>';
                                        }
                                        echo '</ul>';
                                        echo '</div>';
                                    } else {
                                        $width = isset($content['styles']['width']) && $content['styles']['width'] ? $content['styles']['width'] : '100%';
                                        $height = isset($content['styles']['height']) && $content['styles']['height'] ? $content['styles']['height'] : '100%';
                                        $objectFit = isset($content['styles']['objectFit']) && $content['styles']['objectFit'] ? $content['styles']['objectFit'] : 'cover';
                                        $imageAlignment = isset($content['styles']['imageAlignment']) && $content['styles']['imageAlignment'] ? $content['styles']['imageAlignment'] : 'center';
                                        
                                        $isVideoTeaser = isset($content['isVideoTeaser']) && $content['isVideoTeaser'];
                                        $videoData = isset($content['videoData']) && $content['videoData'] ? $content['videoData'] : null;

                                        $videoId = null;
                                        if($isVideoTeaser && $videoData ) {
                                            $videoId = $videoData['videoId'];
                                        }

                                        if($isVideoTeaser && $videoData) {
                                        ?>
                                            <div class="play-btn-outer-wrapper">
                                                <div class="play-btn-inner-wrapper">
                                                    <svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="64" height="64" fill="#fff" viewBox="0 0 24 24">
                                                        <path fill-rule="evenodd" d="M8.6 5.2A1 1 0 0 0 7 6v12a1 1 0 0 0 1.6.8l8-6a1 1 0 0 0 0-1.6l-8-6Z" clip-rule="evenodd"/>
                                                    </svg>
                                                </div>
                                            </div>
                                        <?php 
                                        } 


                                        if(isset($content['styles']) && $content['styles']['useImageMask']) {
                                            $unique_id = uniqid('uid-');    

                                            echo '<div class="content-item image image-mask">';
                                                echo '<svg 
                                                        class="image-mask-svg animation image-mask-svg-' . $block['id'] . '-' . $unique_id . '" 
                                                        id="image-mask-svg-' . $block['id'] . '-' . $unique_id . '" 
                                                        xmlns="http://www.w3.org/2000/svg" 
                                                        xmlns:xlink="http://www.w3.org/1999/xlink" 
                                                        viewBox="0 0 116 100" 
                                                        style="max-width: ' . $width . '; max-height: ' . $height . '; 
                                                        max-height: unset;
                                                        position: relative;
                                                        z-index: 1;">
                                                            <defs>
                                                                <mask id="mask-' . $block['id'] . '-' . $unique_id . '">
                                                                    <path 
                                                                        class="popup-anim path-anim" 
                                                                        d="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                                                                        data-path-to="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" 
                                                                        data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z"
                                                                        fill="#fff" 
                                                                        data-svg-origin="51 45.5" 
                                                                        transform="matrix(1,0,0,1,0,0)" 
                                                                        style="translate: none; rotate: none; scale: none; transform-origin: center center;"
                                                                    >
                                                                    </path>
                                                                </mask>
                                                            </defs>
                                                            <!-- Das Bild in ein eigenes SVG-Element wrappen -->
                                                            <svg 
                                                                xmlns="http://www.w3.org/2000/svg" 
                                                                viewBox="0 0 116 100" 
                                                                mask="url(#mask-' . $block['id'] . '-' . $unique_id . ')" 
                                                                style=position: relative; width: 100%; height: auto; top: 0px; left: 0px;"
                                                            >
                                                                <image 
                                                                    xlink:href="' . $content['mediaUrl'] . '" 
                                                                    y="' . $content['styles']['imageMaskTranslateY'] . '%" 
                                                                    x="' . $content['styles']['imageMaskTranslateX'] . '%" 
                                                                    transform="scale(' . $content['styles']['imageMaskScale'] . ')"
                                                                    width="100%"
                                                                >
                                                                </image>
                                                            </svg>
                                                        </svg>';
                                            echo '</div>';
                                        } else {
                                            echo    '<div class="content-item single-image" style="justify-content: ' . $imageAlignment . ';">
                                                        <img 
                                                            src="' . $content['mediaUrl'] . '" 
                                                            alt="Image" 
                                                            style="max-width: ' . $width . '; max-height: ' . $height . '; object-fit: ' . $objectFit . '"
                                                        >
                                                    </div>';
                                        }
                                    }
                                    break;
                                case 'tabel';
                                    echo '<div class="content-item tabel">';
                                        echo '<table>';

                                        foreach ($content['rows'] as $row) {
                                            echo '<tr>';
                                            
                                            foreach ($row['columns'] as $column) {
                                                echo '<td>';
                                                echo '<div class="cell-content">' . $column['content'] . '</div>';
                                                echo '</td>';
                                            }

                                            echo '</tr>';
                                        }

                                        echo '</table>';
                                    echo '</div>';
                                    ?>
                                    <style>
                                        /* Tabel */ 
                                        .content-item.tabel td {
                                            vertical-align: top;
                                        }
                                        
                                        .content-item.tabel .cell-content {
                                            vertical-align: top;
                                            padding: 0.5rem 1rem;
                                        }

                                        /* .content-item.tabel tr td:first-child .cell-content {
                                            padding-left: 0;
                                        } */
                                        tr:nth-child(odd) {
                                            background: #f0f0f0;
                                        }
                                    </style>
                                    <?php
                                    break;
                                case 'headline':
                                    echo '<div class="content-item headline">';
                                        echo '<h2 style="margin-bottom: 0">' . wp_kses_post($content['content']) . '</h2>';
                                    echo '</div>';
                                    break;
                                case 'paragraph':
                                    echo '<div class="content-item paragraph">';
                                        echo '<p class="intro">' . wp_kses_post($content['content']) . '</p>';
                                    echo '</div>';
                                    break;
                                case 'icon-with-text':
                                    echo '<div class="content-item icon-with-text">';
                                        if($content['useSvgCode']) {
                                            echo '<div class="icon">' . $content['svgCode'] . '</div>';
                                        } else {
                                            echo '<div class="icon"><img src="' . $content['mediaUrl'] . '" alt="' . $content['alt'] . '"></div>';
                                        }
                                        echo '<div class="text">' . $content['content'] . '</div>';
                                    echo '</div>';
                                    break;
                                case 'list':
                                    echo '<div class="content-item list">';
                                        echo '<ul>';
                                        foreach ($content['listItems'] as $listItem) {
                                            echo '<li>' . wp_kses_post($listItem['content']) . '</li>';
                                        }
                                        echo '</ul>';
                                    echo '</div>';
                                    break;
                                case 'accordion':
                                    ?>
                                    <div class="content-item q-and-a">
                                        <div class="grid grid-start">
                                            <div class="sticky-text">
                                                <?php 
                                                    $i = 1;
                                                    foreach ($content['accordionItems'] as $item) : 
                                                ?>
                                                    <!-- <p><?php echo $i ?></p> -->
                                                    <div class="single-q-and-a <?php echo $i !== 1 ? 'collapsed' : ''; ?>">
                                                        <div class="content-wrapper">
                                                            <?php if (!empty($item['question'])) : ?>
                                                                <h4><?php echo wp_kses_post($item['question']); ?></h4>
                                                            <?php endif; ?>
                                                            <?php if (!empty($item['answer'])) : ?>
                                                                <p style="<?php echo $i !== 1 ? 'height: 0;' : ''; ?>">
                                                                    <?php echo wp_kses_post($item['answer']); ?>
                                                                </p>
                                                            <?php endif; ?>
                                                        </div>
                                                        <div class="button-wrapper">
                                                            <button class="toggle-button">
                                                                <!-- <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                                    <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14m-7 7V5"/>
                                                                </svg> -->
                                                                <span class="expand-icon-elem"></span>
                                                                <span class="expand-icon-elem"></span>
                                                            </button>
                                                        </div>
                                                    </div>
                                                <?php $i++; endforeach; ?>
                                            </div>
                                        </div>
                                    </div>
                                    <?php
                                    // set var to make sure to include style and js only ones
                                    $hasAccordion = false;
                                    if($hasAccordion === false) {
                                        ?>
                                        <style>
                                            .single-q-and-a {
                                                padding: 1rem 0 0 0;
                                                border-bottom: solid 1px #EBEEF1;
                                                display: flex;
                                                gap: 2rem;
                                            }
                                            .single-q-and-a:first-child {
                                                border-top: solid 1px #EBEEF1;
                                            }

                                            .single-q-and-a .content-wrapper {
                                                flex-grow: 1;
                                            }

                                            .single-q-and-a h4,
                                            .single-q-and-a .button-wrapper {
                                                cursor: pointer;
                                            }


                                            .single-q-and-a h4,
                                            .single-q-and-a p {
                                                margin: 0 0 1rem 0;
                                            }
                                            .single-q-and-a p {
                                                overflow: hidden;
                                                /* height: 300px; */
                                                transition: all 0.66s cubic-bezier(0.77,0.2,0.05,1.0);
                                            }

                                            .single-q-and-a.collapsed p {
                                                height: 0;
                                                margin: 0;
                                            }


                                            .single-q-and-a .button-wrapper {
                                                display: flex;
                                                justify-content: flex-end;
                                            }

                                            .single-q-and-a .button-wrapper .toggle-button {
                                                outline: none;
                                                border: none;
                                                background: transparent;
                                                display: flex;
                                                align-items: center;
                                                gap: 1rem;
                                                width: 2rem;
                                                height: 2rem;
                                                text-align: center;

                                                cursor: pointer;
                                            }

                                            .single-q-and-a .button-wrapper .expand-icon-elem {
                                                width: 28px;
                                                height: 4px;
                                                display: absolute;
                                                position: absolute;
                                                background: var(--accent-color-secondary);
                                                border-radius: 999px;
                                                transform: rotate(0deg);

                                                transition: all 0.66s cubic-bezier(0.77,0.2,0.05,1.0);
                                            }
                                            .single-q-and-a.collapsed .button-wrapper .expand-icon-elem {
                                                transform: rotate(180deg);
                                            }

                                            .single-q-and-a .button-wrapper .expand-icon-elem:first-child {
                                                transform: rotate(0deg);
                                            }
                                            .single-q-and-a.collapsed .button-wrapper .expand-icon-elem:first-child {
                                                transform: rotate(90deg);
                                            }
                                        </style>
                                        <?php
                                        // Setze die Variable auf true, damit das Akkordeon-Stylesheet und Script nur einmal eingefügt werden
                                        $hasAccordion = true;
                                    }

                                    break;
                                case 'button':
                                    $data_modal = isset($content['useModal']) && $content['useModal'] ? 'data-modal-class="' . $content['link'] . '"' : '';

                                    echo '<div class="content-item button">';
                                        echo '<a href="'. wp_kses_post($content['link']) . '" ' . $data_modal . ' class="cta-button">';
                                            echo '<div>';
                                                echo wp_kses_post($content['content']);
                                                echo '<svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 12H5m14 0-4 4m4-4-4-4"></path>
                                                        </svg>';
                                            echo '</div>';
                                        echo '</a>';
                                    echo '</div>';
                                    break;
                            endswitch; ?>
                        <?php endforeach; ?>
                    </div>
                <? if($equalHeightColumns == 'false') : ?>
                </div>
                <? endif; ?>
            <?php endforeach; ?>
        </div>
    <?php endif; ?>
</div>

<?php
    // Variable zum Überprüfen, ob das Script bereits eingebunden wurde
    global $flexible_content_style_included;
    if ( ! isset( $flexible_content_style_included ) ) {
        $flexible_content_style_included = false;
    }
?>

<?php if ( ! $flexible_content_style_included ) : ?>
    <style>
        path.popup-anim.path-anim {
            transform-origin: center center !important;
        }


        .content-item {
            padding-bottom: 2rem;
        }
        .content-item:last-child {
            padding-bottom: 0;
        }

        /* Text Content */
        .flexible-content .text-content {
            display: flex;
            flex-direction: column;
            justify-content: center;
            margin-bottom: 2rem;
        }

        @media screen and (max-width: 999px) {
            .flexible-content .text-content {
                margin-bottom: 0;
            }
        }

        /* .flexible-content .text-content h2 {
            margin-bottom: 1rem;
        } */
        .flexible-content .text-content h2 {
            margin-top: 0rem;
            margin-bottom: 2rem;
        }

        .flexible-content .text-content h2:last-child {
            margin-bottom: 0;
        }

        .flexible-content .text-content p.paragraph {
            margin-bottom: 40px;
            /* new style from screendesign */
            color: var(--gray-40);
            font-size: 20px;
            font-weight: 400;
            font-style: Regular;
            letter-spacing: 0px;
            text-align: left;
            line-height: 38px;
        }
        .flexible-content .flexible-content-wrapper {
            display: grid;
            gap: 4rem;
        }

        @media screen and (max-width: 999px) {
            .flexible-content .text-content h2:last-child {
                margin-bottom: 1rem;
            }

            .flexible-content .flexible-content-wrapper {
                /* grid-template-columns: repeat(1, 1fr) !important; */
                gap: 2rem;
                display: flex;
                flex-direction: column;
            }

            .flexible-content .flexible-content-wrapper.reverse-columns-on-mobile {
                flex-direction: column-reverse;
            }
        }

        .flexible-content-wrapper .col {
            width: 100%;
        }


        /** IMAGE */
        .content-item.single-image {
            display: flex;
        }
        article .main-content ul.multi-image-list {
            list-style: none;
            margin-left: 0;
            padding-left: 0;
        }
        article .main-content ul.multi-image-list li {
            margin: 0;
            padding: 0;
        }


        /** ICON WITH TEXT */
        .flexible-content .icon-with-text {
            display: flex;
            align-items: normal;
            gap: 1rem;
            align-items: center;
        }

        .content-item.icon-with-text > div.icon {
            max-width: 50%;
            max-width: 48px;
        }





        /**
        * Special Appearance classes
        */
        /* Light Gray */
        .block-inner.flexible-content.light-gray {
            background: #F3F6FA;
            padding: 3rem 4rem;
            border-radius: 5px;
            margin: 0 !important;
        }

        /* Purple Gradient */
        .block-inner.flexible-content.purple-gradient {
            padding: 3rem 4rem;
            border-radius: 1rem;
            background-color: rgba(17, 17, 74, 1);
            background: linear-gradient(60deg, rgba(97,19,107,1) 0%, rgba(17,17,74,1) 22%, rgba(17,17,74,1) 58%, rgba(151,21,129,1) 100%);
        }
        @media screen and (max-width: 999px) {
            .block-inner.flexible-content.purple-gradient {
                padding: 2rem 1rem;
            }
        }

        .block-inner.flexible-content.purple-gradient h1, 
        .block-inner.flexible-content.purple-gradient h2, 
        .block-inner.flexible-content.purple-gradient h3, 
        .block-inner.flexible-content.purple-gradient h4, 
        .block-inner.flexible-content.purple-gradient h5, 
        .block-inner.flexible-content.purple-gradient h6, 
        .block-inner.flexible-content.purple-gradient p {
            color: white;
        } 

        .purple-gradient .single-q-and-a .button-wrapper .expand-icon-elem {
            background: #fff;
        }
    </style>

<?php 
$flexible_content_style_included = true; 
endif; 
?>
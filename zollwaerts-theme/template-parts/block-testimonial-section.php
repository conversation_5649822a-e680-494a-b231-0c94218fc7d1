<?php
if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

$blockId = $block['id'];
?>

<div id="<?php echo wp_kses_post( $block['id'] ); ?>" class="block-inner testimonial">
    <div class="testimonials-container">
        <!-- Linke Spalte -->
        <div class="fixed-column">
            <div class="testimonial-intro">
                <?php if (!empty($block['tagline'])) : ?>
                    <p class="tagline"><?php echo wp_kses_post($block['tagline']); ?></p>
                <?php endif; ?>

                <?php if (!empty($block['heading'])) : ?>
                    <h3><?php echo wp_kses_post($block['heading']); ?></h3>
                <?php endif; ?>

                <?php if (!empty($block['content'])) : ?>
                    <div class="paragraph intro"><?php echo wp_kses_post($block['content']); ?></div>
                <?php endif; ?>
                
            </div>
        </div>

        <!-- Rechte Spalte -->
        <div class="scrolling-column">
            <!-- linke Seite der rechten Spalte -->
            <div class="single-col odd-column">
                <?php 
                if(isset($block['testimonials']) && $block['testimonials']) {
                    foreach($block['testimonials'] as $index => $testimonial) {

                        // check if index is even
                        if($index % 2 == 0) {
                            // echo "Index is even<br>";
                            $imageUrl = get_template_directory_uri() . '/images/placeholder.png';
                            if(!empty($testimonial['image']['url'])) {
                                $imageUrl = $testimonial['image']['url'];
                            }

                            $imageScale = 1;
                            if(!empty($testimonial['image']['styles']['imageScale'])) {
                                $imageScale = $testimonial['image']['styles']['imageScale'];
                            }

                            $imageTranslateX = 0;
                            if(!empty($testimonial['image']['styles']['imageTranslateX'])) {
                                $imageTranslateX = $testimonial['image']['styles']['imageTranslateX'];
                            }

                            $imageTranslateY = 0;
                            if(!empty($testimonial['image']['styles']['imageTranslateY'])) {
                                $imageTranslateY = $testimonial['image']['styles']['imageTranslateY'];
                            }

                            $rotateMask = 0;
                            if(!empty($testimonial['image']['styles']['rotateMask'])) {
                                $rotateMask = $testimonial['image']['styles']['rotateMask'];
                            }

                            $backgroundColor = '#fff';
                            if(!empty($testimonial['image']['styles']['backgroundColor'])) {
                                $backgroundColor = $testimonial['image']['styles']['backgroundColor'];
                            }
                            ?>

                                <div class="testimonial-card">
                                    <div class="image-item">
                                        <svg class="image-mask-svg animation image-mask-svg-nw-jgiboxawe-uid-670941e6230d2" id="image-mask-svg-nw-jgiboxawe-uid-670941e6230d2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 116 100" style="
                                                max-width: 100%;
                                                position: relative;
                                                opacity: 1;
                                                width: 50%;
                                                height: auto;
                                            ">
                                            <defs>
                                                <mask id="<?php echo 'mask-' . $blockId .'-' . $index; ?>">
                                                    <!-- class="popup-anim path-anim"  -->
                                                    <path class="popup-anim path-anim" d="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" data-path-to="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" fill="#fff" style="translate: none; rotate: none; scale: none; transform-origin: 0px 0px;" data-svg-origin="57.925047874450684 49.49992245435715" transform="matrix(1,0,0,1,0,0)">
                                                    </path>
                                                </mask>
                                            </defs>

                                            <!-- Das Bild in ein eigenes SVG-Element wrappen -->
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 116 100" mask="url(#<?php echo 'mask-' . $blockId .'-' . $index; ?>)" style="position:" relative;="" width:="" 100%;="" height:="" auto;="" top:="" 0px;="" left:="" 0px;"="">
                                                <rect width="100%" height="100%" fill="<?php echo $backgroundColor; ?>" style="fill: <?php echo $backgroundColor; ?>;"></rect>
                                                <image 
                                                    xlink:href="<?php echo $imageUrl; ?>" 
                                                    y="<?php echo $imageTranslateY; ?>%" 
                                                    x="<?php echo $imageTranslateX; ?>%" 
                                                    transform="scale(<?php echo $imageScale; ?>)" 
                                                    width="100%"
                                                >
                                                </image>
                                            </svg>

                                            <svg>
                                                <path
                                                    class="popup-anim path-anim" 
                                                    stroke="#EBEEF1" 
                                                    d="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" 
                                                    data-path-to="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" 
                                                    data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                                                    fill="transparent" 
                                                    style="translate: none; rotate: none; scale: none; transform-origin: 0px 0px;" 
                                                    data-svg-origin="57.925047874450684 49.49992245435715" 
                                                    transform="matrix(1,0,0,1,0,0)">
                                                </path>
                                            </svg>
                                        </svg>
                                    </div>

                                    <?php if (isset($testimonial['name']) && $testimonial['name']) {
                                        echo '<h3>' . wp_kses_post($testimonial['name']) . '</h3>';
                                    } ?>

                                    <?php if (isset($testimonial['position']) && $testimonial['position']) {
                                        echo '<p>' . wp_kses_post($testimonial['position']) . '</p>';
                                    } ?>

                                    <?php if (isset($testimonial['content']) && $testimonial['content']) {
                                        echo '<p>' . wp_kses_post($testimonial['content']) . '</p>';
                                    } ?>
                                    <!-- <p>Die Grenzlotsen sind für mich als Zoll- und Exportkontrollverantwortlicher ein zuverlässiger und professioneller Partner. Durch den Lösungsansatz der Grenzlotsen haben wir unsere Sicherheitszahlung zurückerhalten. Außerdem konnten wir das Risiko für künftige Importe durch eine verbindliche Zolltarifauskunft minimieren: Zeitverzug und mehrfache Sicherheitszahlungen werden dadurch vermieden. Durch die verbindliche Auskunft haben wir an Planungssicherheit gewonnen.</p> -->
                                </div>

                            <?php
                        } 

                        // // print testimonial
                        // echo "Index: " . $index . "<br>";
                        // echo "<pre>";
                        // print_r($testimonial);
                        // echo "</pre>";
                    }
                }
                ?>
            </div>
            
            
            <!-- rechte Seite der rechten Spalte -->
            <div class="single-col even-column">
                <?php 
                if(isset($block['testimonials']) && $block['testimonials']) {
                    foreach($block['testimonials'] as $index => $testimonial) {

                        // check if index is even
                        if($index % 2 != 0) {
                            // echo "Index is even<br>";
                            $imageUrl = get_template_directory_uri() . '/images/placeholder.png';
                            if(!empty($testimonial['image']['url'])) {
                                $imageUrl = $testimonial['image']['url'];
                            }

                            $imageScale = 1;
                            if(!empty($testimonial['image']['styles']['imageScale'])) {
                                $imageScale = $testimonial['image']['styles']['imageScale'];
                            }

                            $imageTranslateX = 0;
                            if(!empty($testimonial['image']['styles']['imageTranslateX'])) {
                                $imageTranslateX = $testimonial['image']['styles']['imageTranslateX'];
                            }

                            $imageTranslateY = 0;
                            if(!empty($testimonial['image']['styles']['imageTranslateY'])) {
                                $imageTranslateY = $testimonial['image']['styles']['imageTranslateY'];
                            }

                            $rotateMask = 0;
                            if(!empty($testimonial['image']['styles']['rotateMask'])) {
                                $rotateMask = $testimonial['image']['styles']['rotateMask'];
                            }

                            $backgroundColor = '#fff';
                            if(!empty($testimonial['image']['styles']['backgroundColor'])) {
                                $backgroundColor = $testimonial['image']['styles']['backgroundColor'];
                            }
                            ?>

                                <div class="testimonial-card">
                                    <div class="image-item">
                                        <svg class="image-mask-svg animation image-mask-svg-nw-jgiboxawe-uid-670941e6230d2" id="image-mask-svg-nw-jgiboxawe-uid-670941e6230d2" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 116 100" style="
                                                max-width: 100%;
                                                position: relative;
                                                opacity: 1;
                                                width: 50%;
                                                height: auto;
                                            ">
                                            <defs>
                                                <mask id="<?php echo 'mask-' . $blockId .'-' . $index; ?>">
                                                    <!-- class="popup-anim path-anim"  -->
                                                    <path class="popup-anim path-anim" d="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" data-path-to="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" fill="#fff" style="translate: none; rotate: none; scale: none; transform-origin: 0px 0px;" data-svg-origin="57.925047874450684 49.49992245435715" transform="matrix(1,0,0,1,0,0)">
                                                    </path>
                                                </mask>
                                            </defs>

                                            <!-- Das Bild in ein eigenes SVG-Element wrappen -->
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 116 100" mask="url(#<?php echo 'mask-' . $blockId .'-' . $index; ?>)" style="position:" relative;="" width:="" 100%;="" height:="" auto;="" top:="" 0px;="" left:="" 0px;"="">
                                                <rect width="100%" height="100%" fill="<?php echo $backgroundColor; ?>" style="fill: <?php echo $backgroundColor; ?>;"></rect>
                                                <image 
                                                    xlink:href="<?php echo $imageUrl; ?>" 
                                                    y="<?php echo $imageTranslateY; ?>%" 
                                                    x="<?php echo $imageTranslateX; ?>%" 
                                                    transform="scale(<?php echo $imageScale; ?>)" 
                                                    width="100%"
                                                >
                                                </image>
                                            </svg>

                                            <svg>
                                                <path
                                                    class="popup-anim path-anim" 
                                                    stroke="#EBEEF1" 
                                                    d="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" 
                                                    data-path-to="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" 
                                                    data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                                                    fill="transparent" 
                                                    style="translate: none; rotate: none; scale: none; transform-origin: 0px 0px;" 
                                                    data-svg-origin="57.925047874450684 49.49992245435715" 
                                                    transform="matrix(1,0,0,1,0,0)">
                                                </path>
                                            </svg>
                                        </svg>
                                    </div>

                                    <?php if (isset($testimonial['name']) && $testimonial['name']) {
                                        echo '<h3>' . wp_kses_post($testimonial['name']) . '</h3>';
                                    } ?>

                                    <?php if (isset($testimonial['position']) && $testimonial['position']) {
                                        echo '<p>' . wp_kses_post($testimonial['position']) . '</p>';
                                    } ?>

                                    <?php if (isset($testimonial['content']) && $testimonial['content']) {
                                        echo '<p>' . wp_kses_post($testimonial['content']) . '</p>';
                                    } ?>
                                    <!-- <p>Die Grenzlotsen sind für mich als Zoll- und Exportkontrollverantwortlicher ein zuverlässiger und professioneller Partner. Durch den Lösungsansatz der Grenzlotsen haben wir unsere Sicherheitszahlung zurückerhalten. Außerdem konnten wir das Risiko für künftige Importe durch eine verbindliche Zolltarifauskunft minimieren: Zeitverzug und mehrfache Sicherheitszahlungen werden dadurch vermieden. Durch die verbindliche Auskunft haben wir an Planungssicherheit gewonnen.</p> -->
                                </div>

                            <?php
                        } 

                        
                        // // print testimonial
                        // echo "Index: " . $index . "<br>";
                        // echo "<pre>";
                        // print_r($testimonial);
                        // echo "</pre>";
                    }
                }
                ?>
            </div>
        
        </div>
    </div>

</div>


<style>
.testimonial h3 {
    hyphens: none;
}

.testimonials-container {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2rem;
    padding: 0;
}

.fixed-column {
    position: sticky;
    top: 140px;
    /* width: calc(33% - 2rem); */
    /* background-color: #f9f9f9;  */
    /* padding: 1rem; */
    /* box-shadow: 0 0 10px rgba(0,0,0,0.1); */
    border-radius: 8px;
    height: fit-content;
}

.testimonial-intro {
    margin-top: 72px;
}

.fixed-column .testimonial-intro:before {
    content: "";
    height: 56px;
    position: absolute;
    width: 56px;
    background-image: url("data:image/svg+xml,%3Csvg width='55px' height='55px' viewBox='0 0 55 55' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Ctitle%3Eicons/quote%3C/title%3E%3Cg id='warum-GL-–-success-story' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='success-story-–-detailpage' transform='translate(-294, -3056)' fill='%23BF1692' fill-rule='nonzero'%3E%3Cg id='section' transform='translate(294, 3056)'%3E%3Cg id='icons/quote' transform='translate(0, 6.0156)'%3E%3Cpath d='M12.8333333,43.828125 C19.1666667,43.828125 24.1666667,38.6224856 24.1666667,32.4093032 C24.1666667,26.1961207 19.8333333,21.8301006 14.3333333,21.8301006 C13.1666667,21.8301006 11.8333333,21.9980244 11.5,22.1659483 C12.5,16.4565374 18.1666667,9.57165948 23.5,6.38110632 L15.5,0 C6.33333333,6.54903017 0,16.9603089 0,28.5470546 C0,38.2866379 6,43.828125 12.8333333,43.828125 Z M43.6666667,43.828125 C50,43.828125 55,38.6224856 55,32.4093032 C55,26.1961207 50.6666667,21.8301006 45.1666667,21.8301006 C44,21.8301006 42.6666667,21.9980244 42.3333333,22.1659483 C43.3333333,16.4565374 49,9.57165948 54.3333333,6.38110632 L46.3333333,0 C37.1666667,6.54903017 30.8333333,16.9603089 30.8333333,28.5470546 C30.8333333,38.2866379 36.8333333,43.828125 43.6666667,43.828125 Z'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-size: contain;
    margin-top: -72px;
}


.testimonial-intro .paragraph.intro {
    margin: 0 0 2rem 0;
}

.scrolling-column {
    /* width: 70%; */
    display: grid;
    grid-template-columns: 1fr 1fr; 
    gap: 2rem;
}

.testimonial-card {
    margin-bottom: 5rem;
    background-color: #ffffff;
    border: 1px solid #EBEEF1;
    border-radius: 8px;
    padding: 1.5rem;
}

.testimonial-card:last-child {
    margin-bottom: 0;
}

.testimonial-card .image-item {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: -20%;
    padding-bottom: 2rem;
}

.testimonial-card p {
    font-size: 1rem;
    line-height: 2;
}

@media  screen and (max-width: 999px) {
    .testimonials-container {
        grid-template-columns: 1fr;
    }

    .fixed-column {
        position: relative;
        top: -2rem;
        padding-bottom: 2rem;
    }
}

@media  screen and (max-width: 699px) {
    .scrolling-column {
        display: grid;
        grid-template-columns: 1fr;
    }

    .single-col.even-column {
        margin-top: 5rem;
    }

    .testimonial-card .image-item {
        width: 100%;
        display: flex;
        justify-content: center;
        margin-top: -16%;
        padding-bottom: 2rem;
    }

    .testimonial-card .image-item > svg {
        max-width: 200px !important;
    }

 

}
</style>
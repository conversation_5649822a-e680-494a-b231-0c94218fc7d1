<?php
if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

// Überprüfung, ob es sich um den richtigen Blocktyp handelt
if ($block['type'] === 'pricing-table') :
    
// Hole die Spaltenanzahl und setze einen Standardwert
if(isset($block['pricingColCount'])) {
    $pricingColCount = (int)$block['pricingColCount'];
} else {
    $pricingColCount = 2; // Standardwert, falls nicht definiert
}

// Berechnung des Layouts: 4fr für die Beschreibungsspalte, dann gleich große Spalten für die Angebote
$gridTemplateColumns = "4fr";
for($i = 0; $i < $pricingColCount; $i++) {
    $gridTemplateColumns .= " 3fr";
}
?>


<!-- Pricing Module Test -->
<div id="<?php echo esc_attr($block['id']); ?>" class="block-inner pricing-table">

    <?php if (isset($block['tagline']) && !empty($block['tagline'])) : ?>
        <p class="module-intro"><?php echo wp_kses_post($block['tagline']); ?></p>
    <?php endif; ?>

    <?php if (isset($block['headline']) && !empty($block['headline'])) : ?>
        <h2><?php echo wp_kses_post($block['headline']); ?></h2>
    <?php endif; ?>

    <?php if (isset($block['content']) && !empty($block['content'])) : ?>
        <p class="module-intro"><?php echo wp_kses_post($block['content']); ?></p>
    <?php endif; ?>

    <?php
        // if(isset($block['masterPricingColumn']) && !empty($block['masterPricingColumn'])) {
        //     echo '<h3>masterPricingColumn</h3>';
        //     echo '<pre>';  // Fügt eine bessere Lesbarkeit durch Formatierung hinzu
        //         var_dump($block['masterPricingColumn']);
        //     echo '</pre>';
        // }

        // echo '<hr>';
        
        // if(isset($block['pricingColumns']) && !empty($block['pricingColumns'])) {
        //     echo '<h3>pricingColumns</h3>';
        //     echo '<pre>';  // Fügt eine bessere Lesbarkeit durch Formatierung hinzu
        //         var_dump($block['pricingColumns']);
        //     echo '</pre>';
        // }
    ?>

    <div class="pricing-table">
        <!-- Just description -->
        <div class="col pricing-header">
            <div class="row">
                <div class="row-inner">
                </div>
            </div>

            <?php
                if(isset($block['pricingColumns']) && !empty($block['pricingColumns'])) {
                    foreach($block['pricingColumns'] as $column) {
                        echo '<div class="row">';
                            echo '<div class="row-inner" style="display: block;">';
                                echo '<h5>' . $column['title'] . '</h5>';
                                if(isset($column['description']) && !empty($column['description'])) {
                                    echo '<p style="font-size: 14px; color: var(--accent-color-tertiary);">' . $column['description'] . '</p>';
                                }
                            echo '</div>';
                        echo '</div>';
                    }
                }
            ?>
        </div>
        

        <!-- Mobile pricing table header -->
        <div class="col mobile-pricing-header">
            <div class="row">
                <div class="row-inner">
                </div>
            </div>

            <?php
                if(isset($block['pricingColumns']) && !empty($block['pricingColumns'])) {
                    foreach($block['pricingColumns'] as $column) {
                        echo '<div class="row">';
                            echo '<div class="row-inner">';
                                $first = true; // Only for the first row
                                foreach($block['pricingColumns'] as $column) {
                                    echo '<span class="header-button ' . ($first ? 'active' : '') . '">';
                                        echo '<h5>' . $column['title'] . '</h5>';
                                    echo '</span>';

                                    // Setze $first auf false, nachdem das erste Element ausgegeben wurde
                                    $first = false;
                                }
                            echo '</div>';
                        echo '</div>';
                    }
                }
            ?>

                        <!-- 
                        <div class="row">
                            <div class="row-inner">
                                <span class="header-button active">
                                    <h5>Basisticket</h5>
                                </span>
                                <span class="header-button">
                                    <h5>VIP-Ticket</h5>
                                </span>
                            </div>
                        </div>

                        <div class="row">
                            <div class="row-inner">
                            <span class="header-button">
                                    <h5>Basisticket</h5>
                                </span>
                                <span class="header-button">
                                    <h5>VIP-Ticket</h5>
                                </span>
                            </div>
                        </div> 
                        -->
        </div>

        <!-- Pricing Table Feature rows -->
        <?php
            if(isset($block['masterPricingColumn']) && isset($block['masterPricingColumn']['rows']) && !empty($block['masterPricingColumn']['rows'])) {
                foreach($block['masterPricingColumn']['rows'] as $mainColumnIndex => $mainColumnRow) {
                    echo '<div class="col pricing-features">';
                        echo '<div class="row description">';
                            echo '<div class="row-inner">';
                                echo $mainColumnRow['name'];
                            echo '</div>';
                        echo '</div>';

                        if(isset($block['pricingColumns']) && !empty($block['pricingColumns'])) {
                            foreach($block['pricingColumns'] as $columnIndex => $column) {
                                echo '<div class="row">';
                                    echo '<div class="row-inner">';
                                        if(isset($column['rows']) && !empty($column['rows'])) {
                                            // Hier den aktuellen Index $mainColumnIndex verwenden
                                            if(isset($column['rows'][$mainColumnIndex]['check']) && $column['rows'][$mainColumnIndex]['check']) {
                                                include 'icons/checkmark.php';
                                            } else {
                                                include 'icons/x-mark.php';
                                            }
                                            echo '<p class="tablet-and-smaller">' . $mainColumnRow['name'] . '</p>';
                                        }
                                    echo '</div>';
                                echo '</div>';
                            }
                        }
                    echo '</div>';
                }
            }
        ?>

 
        <!-- Pricing Table Footer -->
        <div class="col pricing-footer">
        <?php
            if(isset($block['pricingColumns']) && !empty($block['pricingColumns'])) {
                // echo '<h2>$block[pricingColumns] is there</h2>';
                // echo '<h3>pricingColumns</h3>';
                // echo '<pre>';  // Fügt eine bessere Lesbarkeit durch Formatierung hinzu
                //     var_dump($block['pricingColumns']);
                // echo '</pre>';

                echo '<div class="row">';
                    echo '<div class="row-inner">';
                    echo '</div>';
                echo '</div>';
                
            
                foreach($block['pricingColumns'] as $columnIndex => $column) {
                    if(isset($column['ctaRow']) && !empty($column['ctaRow'])) {
                        // echo '<h3>ctaRow</h3>';
                        // echo '<pre>';  // Fügt eine bessere Lesbarkeit durch Formatierung hinzu
                        //     var_dump($column['ctaRow']);
                        // echo '</pre>';
                        echo '<div class="row">';
                            echo '<div class="row-inner">';

                                // Price 
                                if(isset($column['ctaRow']['price']) && !empty($column['ctaRow']['price'])) {
                                    echo '<h3 class="main-price">';
                                    echo $column['ctaRow']['price'];
                                    echo '</h3>';
                                }

                                // rows
                                if(isset($column['ctaRow']['rows']) && !empty($column['ctaRow']['rows'])) {
                                    foreach($column['ctaRow']['rows'] as $ctaInnerRowIndex => $ctaInnerRow) {
                                        echo '<p>';
                                            if(isset($ctaInnerRow['leftContent']) && !empty($ctaInnerRow['leftContent'])) {
                                                echo '<span>' . $ctaInnerRow['leftContent'] . '</span>';
                                            }
                
                                            if(isset($ctaInnerRow['rightContent']) && !empty($ctaInnerRow['rightContent'])) {
                                                echo '<span>' . $ctaInnerRow['rightContent'] . '</span>';
                                            }
                                        echo '</p>';
                                    }
                                }

                            echo '</div>';
                        echo '</div>';
                    }
                }
            }
        ?>
        </div>


        <!-- Pricing Table CTAs -->
        <div class="col pricing-buttons">
            <?php
                if(isset($block['pricingColumns']) && !empty($block['pricingColumns'])) {
                    // echo '<h2>$block[pricingColumns] is there</h2>';
                    // echo '<h3>pricingColumns</h3>';
                    // echo '<pre>';  // Fügt eine bessere Lesbarkeit durch Formatierung hinzu
                    //     var_dump($block['pricingColumns']);
                    // echo '</pre>';

                    echo '<div class="row">';
                        echo '<div class="row-inner">';
                        echo '</div>';
                    echo '</div>';
                    
                
                    foreach($block['pricingColumns'] as $columnIndex => $column) {
                        if(isset($column['ctaRow']) && !empty($column['ctaRow'])) {
                            // echo '<h3>ctaRow</h3>';
                            // echo '<pre>';  // Fügt eine bessere Lesbarkeit durch Formatierung hinzu
                            //     var_dump($column['ctaRow']);
                            // echo '</pre>';

                            echo '<div class="row">';
                                echo '<div class="row-inner">';

                                    // CTA 
                                    if(isset($column['ctaRow']['buttonText']) && !empty($column['ctaRow']['buttonText']) && isset($column['ctaRow']['buttonLink']) && !empty($column['ctaRow']['buttonLink'])) {
                                        if(isset($column['ctaRow']['useModal']) && $column['ctaRow']['useModal']) {
                                            echo '<a href="' . $column['ctaRow']['buttonLink'] . '" data-modal-class="' . $column['ctaRow']['buttonLink'] . '" class="cta-button">';
                                                echo '<div>';
                                                    echo $column['ctaRow']['buttonText'];
                                                    echo '<svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 12H5m14 0-4 4m4-4-4-4"></path>
                                                        </svg>';
                                                echo '</div>';
                                            echo '</a>';
                                        } else {
                                            echo '<a href="' . $column['ctaRow']['buttonLink'] . '" class="cta-button">';
                                                echo '<div>';
                                                    echo $column['ctaRow']['buttonText'];
                                                    echo '<svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 12H5m14 0-4 4m4-4-4-4"></path>
                                                        </svg>';
                                                echo '</div>';
                                            echo '</a>';
                                        }
                                    }

                                    echo '</div>';
                            echo '</div>';
                        }
                    }
                }
            ?>
        </div>

    </div>
</div>


<?php
// Globale Variable oder statische Variable verwenden, um zu prüfen, ob das Skript bereits eingebunden wurde
global $pricing_table_scripts_included;
if (!isset($pricing_table_scripts_included)) {
    $pricing_table_scripts_included = false;
}

if (!$pricing_table_scripts_included) {
?>  
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const pricingTables = document.querySelectorAll('.pricing-table');

        pricingTables.forEach((pricingTable) => {
            const mobilePricingHeader = pricingTable.querySelector('.mobile-pricing-header');
            console.log('mobilePricingHeader', mobilePricingHeader);

            // const allRows = pricingTable.querySelectorAll('.row');
            const pricingFeatures = pricingTable.querySelectorAll('.pricing-features');
            console.log('pricingFeatures', pricingFeatures);

            const pricingHeaderRows = mobilePricingHeader.querySelectorAll('.row');
            console.log('pricingHeaderRows', pricingHeaderRows);

            const pricingFooterRows = pricingTable.querySelectorAll('.pricing-footer .row');
            console.log('%c pricingFooterRows', 'color: red', pricingFooterRows);

            pricingHeaderRows.forEach((row) => {
                console.log(row);

                const buttons = row.querySelectorAll('.header-button');
                console.log('buttons: ', buttons);

                buttons.forEach((button, index) => {
                    button.addEventListener('click', () => {
                        console.log('header button clicked: ', index + 1);

                        pricingFooterRows.forEach((row, rowIndex) => {
                            if (index + 1 === rowIndex) {
                                console.log('footer row expanded: ', row, rowIndex);
                                row.classList.add('expanded');
                                row.classList.remove('collapsed');
                            } else {
                                console.log('footer row collapsed: ', row, rowIndex);
                                row.classList.remove('expanded');
                                row.classList.add('collapsed');
                            }
                        })

                        pricingFeatures.forEach((feature) => {

                            const rows = feature.querySelectorAll('.row');

                            // button.classList.remove('active');
                            buttons.forEach((button) => {
                                button.classList.remove('active');
                            })

                            console.log('button index: ', index);
                            buttons[index].classList.add('active');

                            rows.forEach((row, rowIndex) => {
                                if (index + 1 === rowIndex) {
                                    row.classList.add('expanded');
                                    row.classList.remove('collapsed');
                                } else {
                                    row.classList.remove('expanded');
                                    row.classList.add('collapsed');
                                }
                            })
                        })

                        row.classList.add('expanded');

                        // addDynamicStyle(`
                        //     .pricing-table .col .row:nth-child(${index}) {
                        //         display: none;
                        //     }
                        //     .pricing-table .col .row:nth-child(${index + 1}) {
                        //         display: block;
                        //     }
                        //     .pricing-table .col .row:nth-child(${index + 2}) {
                        //         display: none;
                        //     }
                        // `);
                    })
                })
            })
        })
    })
</script>


<style>
    .pricing-table {
        /* display: grid;
        grid-template-colums: 2fr 1fr 1fr; */
        /* max-width: 1400px;
        margin: 0 auto; */
        /* box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1); */
    }

    /*
    .pricing-table .col {
        display: grid;
        grid-template-columns: 4fr 3fr 3fr;
        gap: 36px;
    }
    */

    .pricing-table .col {
        display: grid;
        grid-template-columns: <?php echo $gridTemplateColumns; ?>;
        gap: 36px;
    }

    .pricing-table .col .row {
        /* background-color: #eeeeee; */
        /* padding-left: 2rem; */
        text-align: center;
    }
    .pricing-table .col .row:first-child {
        background-color: transparent;
        padding: 0;
        text-align: left;
    }
    
    
    .pricing-table .col .row .row-inner {
        background-color: #F3F6FA;
        padding: 1rem 0;
        height: 100%;
        align-items: center;
        display: flex;
        justify-content: center;
    }
    .pricing-table .col .row:first-child .row-inner {
        background-color: transparent;
        padding: 1rem 0;
        align-items: center;
        justify-content: left;
    }

    /* col footer */
    .pricing-table .col.pricing-footer .row .row-inner {
        display: block;
    }

    /* disable mobile header on Desktop */
    .pricing-table .col.mobile-pricing-header {
        display: none;
    }

    /* Border radius in row-inner for first and last colum only */
    .pricing-table .col.pricing-header .row .row-inner {
        border-radius: 12px 12px 0px 0px;
    }
    .pricing-table .col.pricing-buttons .row .row-inner {
        border-radius: 0px 0px 12px 12px;
    }

    /* Table Header */
    .pricing-table .col.pricing-header .row .row-inner {
        padding: 2rem 1rem;
    }
    .pricing-table .col.pricing-header .row .row-inner h5 {
        margin-bottom: 0;
        text-transform: uppercase;
    }

    /* Table Rows */
    .col.pricing-features:first-child,
    .col.pricing-features:nth-child(2) {
        border-top: solid 1px var(--light-gray);
    }
    .col.pricing-features {
        border-bottom: solid 1px var(--light-gray);
    }


    .col.pricing-footer .row .row-inner p {
        display: flex;
        justify-content: space-between;
        padding: 0 1rem;
        line-height: 2;
        font-size: 16px;
    }
    .col.pricing-footer .row .row-inner h3.main-price {
        text-align: center;
        align-items: center;
        justify-content: space-around;
        line-height: 2;
    }

    .pricing-table a {
        width: calc(100% - 2rem);
        cursor: pointer;
    }

    .pricing-table a div {
        background: var(--accent-color);
        border: none;
        border-radius: 4px;
        outline: none;
        color: #fff;
        /* width: calc(100% - 2rem); */
        width: 100%;
        padding: 1rem;
        font-size: 1rem;
        margin-top: -1rem;
        margin-bottom: 0;
        cursor: pointer;
    }

    .tablet-and-smaller {
        display: none;
    }


    @media screen and (max-width: 999px) {
        .pricing-table .col {
            display: grid;
            grid-template-columns: 1fr;
            gap: 36px;
        } 
        /* .pricing-table .col .row:first-child,
        .pricing-table .col .row:last-child {
            display: none;
        } */
        .pricing-table .col .row {
            display: none;
        }
        .pricing-table .col .row:nth-child(2) {
            display: block;
        }

        .pricing-table a {
            width: 100%;
            cursor: pointer;
        }

        /* Mobile pricing table header */
        .pricing-table .col.mobile-pricing-header {
            display: block;
            border-bottom: solid 1px var(--light-gray);
        }
        .pricing-table .col.mobile-pricing-header .row .row-inner {
            display: grid;
            grid-template-columns: 1fr 1fr;
            justify-content: center;
            align-items: center;
            background: none;
            padding: 0;
        }

        .col.mobile-pricing-header .row .row-inner span {
            border-bottom: solid 4px transparent;
            padding: 1rem;
        }
        .col.mobile-pricing-header .row .row-inner span.active {
            border-bottom: solid 4px var(--accent-color);
            padding: 1rem;
        }


        .col.mobile-pricing-header .row .row-inner span.header-button {
            text-align: center;
        }

        /* disable desktop pricing table header */
        .pricing-table .col.pricing-header {
            display: none;
        }

        /* expanded and collapsed feature rows */
        .row.collapsed {
            display: none !important;
        }
        .row.expanded {
            display: block !important;
        }

        .pricing-table .col .row .row-inner svg {
            min-width: 30px;
            width: 30px;
        }

        /* SIngle row col on mobile */
        .tablet-and-smaller {
            display: block;
        }
        .pricing-table .col .row .row-inner {
            padding: 1rem 1rem;
            justify-content: flex-start;
            text-align: left;
        }
        .pricing-table .col .row .row-inner p {
            padding: 0 0 0 1rem;
        }


        .pricing-table button {
            width: 100%;
        }
    }

    /** style for mobile price expansion and collapsing */
    @media screen and (max-width: 999px) {
        .pricing-table .col.pricing-footer .row.expanded {
            display: block;
        }
        .pricing-table .col.pricing-footer .row.collapsed {
            display: none;
        }
    }


    /* TODO: Disable Pricing row when selected in backend */
    /* Disable Pricing */ 
    /*
    .col.pricing-footer {
        display: none;
    }
    .pricing-table button {
        margin-top: 0;
        margin-bottom: 0;
        position: relative;
    }
    */

    /** TODO: use only one CTA for both cols when selected in backend */
    /* One button for both cols */
    /*
    @media screen and (min-width: 1000px) {
        .pricing-table .col.pricing-buttons .row {
            width: calc(200% + 36px);
            z-index: 9;
            position: relative;
        }

        .pricing-table .col.pricing-buttons .row:last-child {
            z-index: 0;
            display: none;
        }
    }
    */

</style>
<?php
$agenda_overview_scripts_included = true;
}
?>

<?php endif; ?>
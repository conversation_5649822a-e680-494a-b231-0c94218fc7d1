<?php
if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

// Überprüfung, ob es sich um den richtigen Blocktyp handelt
if ($block['type'] === 'featureGrid') :


// TODO: unbedingt in page.php auslagern und variable übergeben 
// Holen der gespeicherten JSON-Daten aus den Post-Metadaten
$theme_settings_json = get_option('grenzlotsen_page_builder_content');

// echo '<p>theme_settings_json:' . $theme_settings_json . '</p>';

// Verwen<PERSON> von wp_unslash, um unnötige Escape-Zeichen zu entfernen
$unslashed_theme_settings = wp_unslash($theme_settings_json);

// echo '<p>unslashed_data:' . $unslashed_data . '</p>';

// Dekodieren der JSON-Daten in ein PHP-Array
$theme_settings_array = json_decode($unslashed_theme_settings, true); 

$centerContent = isset($block['centerContent']) ? $block['centerContent'] : 0;
$centerGridItems = isset($block['centerGirdItems']) ? $block['centerGirdItems'] : 0;


?>

<?php
/*
<!-- TODO: unbedingt diese Logig in Page auslagern und es im besten Fall irgendwie schaffen, dass nur das angeforderte Feld auch ankommt -->
<!-- <script>
    let themeSettings = JSON.parse(<?php echo json_encode($theme_settings); ?>)
    console.log('themeSettings: ', themeSettings)
    console.log('themeSettings.htmlForms: ', themeSettings.htmlForms)
    // const htmlForms = themeSettings.htmlForms
</script> -->


<!-- <p>$theme_settings_array: <?php echo $theme_settings_array; ?></p> -->
*/
?>


<div id="<?php echo esc_attr($block['id']); ?>" class="block-inner feature-grid">
    <!-- Tagline -->
    <?php if (!empty($block['tagline'])) : ?>
        <p class="tagline"><?php echo wp_kses_post($block['tagline']); ?></p>
    <?php endif; ?>

    <?php if (!empty($block['heading'])) : ?>
        <h2><?php echo wp_kses_post($block['heading']); ?></h2>
    <?php endif; ?>

    <?php if (!empty($block['paragraph'])) : ?>
        <p class="module-intro"><?php echo wp_kses_post($block['paragraph']); ?></p>
    <?php endif; ?>


    <?php 
    $gridOrFlexStyle = $centerGridItems ? 'display: flex; flex-wrap: wrap; justify-content: center;' : 'grid-template-columns: repeat(' . intval($block['gridTemplate']) . ', 1fr);';
    ?>

    <div class="grid <?php echo $centerGridItems ? 'flex' : '' ?>" style="<?php echo $gridOrFlexStyle ?>">
        <?php foreach ($block['gridItems'] as $item) : ?>
            <?php
            $amountOfColumns = intval($block['gridTemplate']); 
                
            ?>

            <?php
                $imageUrl = null;
                if (isset($item['image']) && isset($item['image']['id'])) {
                    $image_id = $item['image']['id'];
                    $imageUrl = wp_get_attachment_url($image_id);
                }

                $isIconImage = isset($item['isIconImage']) ? $item['isIconImage'] : 0;
            
                // echo '<p>' . $amountOfColumns . '</p>';
            ?>

            <div class="grid-item" style="
                <?php echo $centerContent ? 'text-align: center;' : ''; ?>
                <?php 
                if($centerGridItems) {
                    if ($amountOfColumns > 0) {
                        echo 'width: calc((100% / ' . $amountOfColumns . ') - ((' . $amountOfColumns . ' - 1) * 36px) / ' . $amountOfColumns . ');';
                    } else {
                        echo 'width: calc(50% - 17px);';
                    }   
                }
                ?>
            ">
                <div class="icon-wrapper <?php echo $block['useNumbersAboveIcon'] ? 'use-number' : ''; ?>">
                    <!-- Feature Icon -->
                    <?php if ($isIconImage && $imageUrl) : ?>
                        <img src="<?php echo $imageUrl; ?>" alt="Feature Icon"/>
                    <?php else : ?>
                        <?php if ($item['icon']) : ?>
                            <?php echo $item['icon']; ?>
                        <?php else : ?>
                            <svg width="116px" height="105px" viewBox="0 0 116 100" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="
                                width: 116px;
                                height: auto;
                            ">
                                <path class="path-anim" d="M 14 75 c 8.824 17.457 22.659 24.989 41.504 22.596 c 18.845 -2.392 32.496 -12.596 44.496 -26.596 c 10 -14 21 -26 15 -48 C 109 3 80.915 3.777 61.627 0.676 C 42 -1 21 4 13 21 c -8.38 18.254 -8.158 36.11 0.665 53.566 Z" data-path-to="M20.788 76.971c8.824 17.457 22.659 24.989 41.504 22.596 18.845-2.392 33.92-11.12 45.228-26.185 11.307-15.064 11.307-30.172 0-45.325C96.213 12.904 80.915 3.777 61.627.676 42.338-2.426 28.504 5.15 20.123 23.405c-8.38 18.254-8.158 36.11.665 53.566Z" vector-effect="non-scaling-stroke" id="Path" opacity="0.2" style="fill: #bf1692;"></path>
                            </svg>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>


                <?php if (isset($item['title']) && !empty($item['title'])) : ?>
                    <h4><?php echo wp_kses_post($item['title']); ?></h4>
                <?php endif; ?>
                
                <div class="paragraph">
                    <?php if (isset($item['content']) && !empty($item['content'])) : ?>
                        <p><?php echo wp_kses_post($item['content']); ?></p>
                    <?php endif; ?>
                </div>

                <!-- Forms -->
                <?php if (isset($item['choosenForm']) && !empty($item['choosenForm'])) : ?>
                    <div class="form-wrapper">
                    <?php 
                        $id_to_find = $item['choosenForm'];
                        $form = find_form_by_id($theme_settings_array, $id_to_find);
                        if (!empty($form)) {
                            echo '<div>' . $form['html'] . '</div>';
                        }
                    ?> 
                    </div>
                <?php endif; ?>
            </div>
        <?php endforeach; ?>
    </div>
</div>


<style>
div#<?php echo esc_attr($block['id']); ?> {
    --bg-one-opacity: 0;
    --bg-one-transformation-size: 16%;
    --bg-one-transformation-x: 50%;

    /* overflow: hidden; */
    position: relative;
}

div#<?php echo esc_attr($block['id']); ?>:before {
    content: '';
    z-index: -1;
    width: 100vw;
    height: 100vw;
    background: aliceblue;
    position: absolute;
    margin-left: calc(-50vw + 50%);
    margin-top: -6%;

    background: radial-gradient(circle at 12% var(--bg-one-transformation-x), rgb(191 22 146 / 40%) 0%, #eee0 var(--bg-one-transformation-size)), radial-gradient(circle at 31% 36%, rgb(24 212 242 / 33%) 0%, #eee0 24%);
    opacity: var(--bg-one-opacity);
}

@media screen and (max-width: 999px) {
    .block-inner.feature-grid .grid.flex .grid-item {
        width: calc(50% - 18px) !important;
    }
}

@media screen and (max-width: 699px) {
    .block-inner.feature-grid .grid.flex .grid-item {
        width: 100% !important;
    }
}

.block-inner.feature-grid .grid .grid-item .icon-wrapper {
	/* padding: 1rem 3rem; */
	/* max-width: calc(132px + 6rem); */
}

.block-inner.feature-grid .form-wrapper ul,
.block-inner.feature-grid .form-wrapper ul li {
    margin-left: 0;
    padding-left: 0;
    padding-bottom: 0;
    padding-right: 0;
    margin: 0;
    position: relative;
}

.block-inner.feature-grid .grid .grid-item h2, 
.block-inner.feature-grid .grid .grid-item h3, 
.block-inner.feature-grid .grid .grid-item h4, 
.block-inner.feature-grid .grid .grid-item h5,
.block-inner.feature-grid .grid .grid-item h6,
.block-inner.feature-grid .grid .grid-item .paragraph,
.block-inner.feature-grid .grid .grid-item p {
    width: 100%;
}
</style>

<?php endif; ?>

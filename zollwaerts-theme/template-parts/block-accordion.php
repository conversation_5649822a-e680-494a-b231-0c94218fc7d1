<?php
if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

// Überprüfung, ob es sich um den richtigen Blocktyp handelt
if ($block['type'] === 'accordion') :



// Think about optimising this:
// $section_count = 0;
// foreach ($block['sections'] as $item) {
//     $section_count++;
// }

?>

<!-- <svg width="116px" height="100px" viewBox="0 0 116 100" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="height: auto; ">
    <defs>
        <clipPath id="custom-mask" maskUnits="objectBoundingBox" maskContentUnits="objectBoundingBox">
            <path class="popup-anim path-anim" d="M20.788 76.971c8.824 17.457 22.659 24.989 41.504 22.596 18.845-2.392 33.92-11.12 45.228-26.185 11.307-15.064 11.307-30.172 0-45.325C96.213 12.904 80.915 3.777 61.627.676 42.338-2.426 28.504 5.15 20.123 23.405c-8.38 18.254-8.158 36.11.665 53.566Z" data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" vector-effect="non-scaling-stroke" style="fill: black; opacity: 1;"></path>
        </clipPath>
    </defs>
</svg> -->


<div id="<?php echo esc_attr($block['id']); ?>" class="block-inner q-and-a">
    <!-- Real content -->
    <?php if (!empty($block['heading'])) : ?>
        <h2><?php echo wp_kses_post($block['heading']); ?></h2>
    <?php endif; ?>

    <div class="grid grid-start">
        <div class="sticky-text">
            <?php 
                $i = 1;
                foreach ($block['accordionItems'] as $item) : 
            ?>
                <!-- <p><?php echo $i ?></p> -->
                <div class="single-q-and-a <?php echo $i !== 1 ? 'collapsed' : ''; ?>">
                    <div class="content-wrapper">
                        <?php if (!empty($item['question'])) : ?>
                            <h4><?php echo wp_kses_post($item['question']); ?></h4>
                        <?php endif; ?>
                        <?php if (!empty($item['answer'])) : ?>
                            <p style="<?php echo $i !== 1 ? 'height: 0;' : ''; ?>">
                                <?php echo wp_kses_post($item['answer']); ?>
                            </p>
                        <?php endif; ?>
                    </div>
                    <div class="button-wrapper">
                        <button class="toggle-button">
                            <!-- <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14m-7 7V5"/>
                            </svg> -->
                            <span class="expand-icon-elem"></span>
                            <span class="expand-icon-elem"></span>
                        </button>
                    </div>
                </div>
            <?php $i++; endforeach; ?>
        </div>

    </div>
</div>
<?php endif; ?>


<style>
    .single-q-and-a {
        padding: 1rem 0 0 0;
        border-bottom: solid 1px #EBEEF1;
        display: flex;
        gap: 2rem;
    }
    .single-q-and-a:first-child {
        border-top: solid 1px #EBEEF1;
    }

    .single-q-and-a .content-wrapper {
        flex-grow: 1;
    }

    .single-q-and-a h4,
    .single-q-and-a .button-wrapper {
        cursor: pointer;
    }


    .single-q-and-a h4,
    .single-q-and-a p {
        margin: 0 0 1rem 0;
    }
    .single-q-and-a p {
        overflow: hidden;
        /* height: 300px; */
        transition: all 0.66s cubic-bezier(0.77,0.2,0.05,1.0);
    }

    .single-q-and-a.collapsed p {
        height: 0;
        margin: 0;
    }


    .single-q-and-a .button-wrapper {
        display: flex;
        justify-content: flex-end;
    }

    .single-q-and-a .button-wrapper .toggle-button {
        outline: none;
        border: none;
        background: transparent;
        display: flex;
        align-items: center;
        gap: 1rem;
        width: 2rem;
        height: 2rem;
        text-align: center;

        cursor: pointer;
    }

    .single-q-and-a .button-wrapper .expand-icon-elem {
        width: 28px;
        height: 4px;
        display: absolute;
        position: absolute;
        background: var(--accent-color-secondary);
        border-radius: 999px;
        transform: rotate(0deg);

        transition: all 0.66s cubic-bezier(0.77,0.2,0.05,1.0);
    }
    .single-q-and-a.collapsed .button-wrapper .expand-icon-elem {
        transform: rotate(180deg);
    }

    .single-q-and-a .button-wrapper .expand-icon-elem:first-child {
        transform: rotate(0deg);
    }
    .single-q-and-a.collapsed .button-wrapper .expand-icon-elem:first-child {
        transform: rotate(90deg);
    }

</style>

<script>
    // debugger;

    const accordionItems = document.querySelectorAll('.single-q-and-a');
    accordionItems.forEach((item, index) => {
        const toggleButton = item.querySelector('.toggle-button');
        const question = item.querySelector('.content-wrapper h4');
        const answer = item.querySelector('.content-wrapper p');

        if(index === 0) {
            answer.style.height = `${answer.scrollHeight}px`;
        }

        toggleButton.addEventListener('click', () => {
            toggleAccordionItems(index);
        })

        question.addEventListener('click', () => {
            toggleAccordionItems(index);
        })
    })

    function toggleAccordionItems(index, answer) {
        accordionItems.forEach((item, i) => {
            const answer = item.querySelector('.content-wrapper p');
            const scrollHeight = answer.scrollHeight;

            const isItemCollapsed = item.classList.contains('collapsed');

            if(i === index) {
                if (!isItemCollapsed) {
                    item.classList.add('collapsed');
                    answer.style.height = 0;
                } else {
                    item.classList.remove('collapsed');
                    answer.style.height = `${scrollHeight}px`;
                }
            } else {
                item.classList.add('collapsed');
                answer.style.height = 0;
            }
        })
    }
</script>





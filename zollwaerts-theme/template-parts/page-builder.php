<div>
<?php
    // TODO: Das gibt es aktuell noch in der DB, wird aber im Page Builder nicht verwendet 
    // if ($mein_text_input) {
    //     echo '<div class="mein-text-input">' . wp_kses_post($mein_text_input) . '</div>';
    // }

    // Page Builder Blocks Output
    /* Original Code der, abgesehen von den "n" bei Umbrüchen, funktionierte
    $page_builder_blocks = get_post_meta( get_the_ID(), '_page_builder_blocks', true );
    */

    if ($page_builder_blocks && is_array($page_builder_blocks)) {
        foreach ($page_builder_blocks as $block) {
            // echo '<div style="border: solid red 2px;">';
            // echo htmlspecialchars(json_encode($block));
            // echo '</div>';

            // Block überspringen, wenn 'disableModule' true ist
            if (isset($block['disableModule']) && $block['disableModule'] === true) {
                continue; // Überspringt den aktuellen Block und geht zum nächsten
            }

            if(!isset($block['type'])) {
                continue;
            }

            // check if block is flexible content
            $is_flexible_content = $block['type'] === 'flexible-content';
            $appearance = '';
            if ($is_flexible_content) {
                $appearance = isset($block['advancedSettings']['appearance']) && $block['advancedSettings']['appearance'] ? $block['advancedSettings']['appearance'] : 'default';
            }

            // check if section is testimonial section
            $is_testimonial_section = $block['type'] === 'testimonial-section';
            if ($is_testimonial_section) {
                $appearance = 'gray-fullwidth';
            }

            $is_sticky = $block['type'] === 'sticky-text-image';
            if($is_sticky) {
                $appearance = 'overflow-visible';
            }

            echo '<div class="overflow-wrapper ' . $appearance . '">';

            switch ( $block['type'] ) {
                case 'hero':
                    $block_type = 'hero';
                    $has_hero_block = true;
                    break;
                case 'text-image':
                    $block_type = 'text-image';
                    break;
                case 'flexible-content':
                    $block_type = 'flexible-content';
                    break;
                case 'testimonial-section':  // Dein neuer Blocktyp für testimonial-section
                    $block_type = 'testimonial-section';
                    break;
                case 'timeline-section':  // Dein neuer Blocktyp für timeline-section
                    $block_type = 'timeline-section';
                    break;
                case 'featureGrid':  // Dein neuer Blocktyp für featureGrid
                    $block_type = 'featureGrid';
                    break;
                case 'imageGrid':  // Dein neuer Blocktyp für imageGrid
                    $block_type = 'imageGrid';
                    break;
                case 'post-list':  // Dein neuer Blocktyp für post-list
                    $block_type = 'post-list';
                    break;
                case 'success-story-teaser':  // Dein neuer Blocktyp für success-story-teaser
                    $block_type = 'success-story-teaser';
                    break;
                case 'cta-section':  // Dein neuer Blocktyp für cta-section
                    $block_type = 'cta-section';
                    break;
                case 'slider-section':  // Dein neuer Blocktyp für slider-section
                    $block_type = 'slider-section';
                    break;
                case 'sticky-text-image':  // Dein neuer Blocktyp für sticky-text-image
                    $block_type = 'sticky-text-image';
                    break;
                case 'pricing-table':  // Dein neuer Blocktyp für pricing-table
                    $block_type = 'pricing-table';
                    break;
                case 'agenda-overview':  // Dein neuer Blocktyp für agenda-overview
                    $block_type = 'agenda-overview';
                    break;
                case 'modal':  // Dein neuer Blocktyp für modal
                    $block_type = 'modal';
                    break;
                case 'raw-html':  // Dein neuer Blocktyp für HTML-Inhalte
                    $block_type = 'raw-html';
                    break;
                case 'headline':
                    $block_type = 'headline';
                    break;
                case 'paragraph':
                    $block_type = 'paragraph';
                    break;
                default:
                    $block_type = '';
                    break;
            }
            if ( $block_type ) {
                set_query_var( 'block', $block );

                // TODO: make actual fullwidth settings 
                // $is_fullwidth = $block['advancedSettings']['fullwidth'];
                $fullwidth_types = ['hero', 'slider-section'];
                $is_fullwidth = in_array($block_type, $fullwidth_types) ? 'fullwidth' : '';

                // echo '<p>' . $block_type . ' ' . $is_fullwidth . '</p>';


                $spacing_top = isset($block['advancedSettings']['spacing']['top']) ? $block['advancedSettings']['spacing']['top'] : 'default_value';
                $spacing_right = isset($block['advancedSettings']['spacing']['right']) ? $block['advancedSettings']['spacing']['right'] : 'default_value';
                $spacing_bottom = isset($block['advancedSettings']['spacing']['bottom']) ? $block['advancedSettings']['spacing']['bottom'] : 'default_value';
                $spacing_left = isset($block['advancedSettings']['spacing']['left']) ? $block['advancedSettings']['spacing']['left'] : 'default_value';

                $custom_spacing_top = isset($block['advancedSettings']['spacing']['topCustom']) ? $block['advancedSettings']['spacing']['topCustom'] : 'default_value';
                $custom_spacing_right = isset($block['advancedSettings']['spacing']['rightCustom']) ? $block['advancedSettings']['spacing']['rightCustom'] : 'default_value';
                $custom_spacing_bottom = isset($block['advancedSettings']['spacing']['bottomCustom']) ? $block['advancedSettings']['spacing']['bottomCustom'] : 'default_value';
                $custom_spacing_left = isset($block['advancedSettings']['spacing']['leftCustom']) ? $block['advancedSettings']['spacing']['leftCustom'] : 'default_value';


                $isSpacingTopCustom = $spacing_top === 'custom';
                $isSpacingRightCustom = $spacing_right === 'custom';
                $isSpacingBottomCustom = $spacing_bottom === 'custom';
                $isSpacingLeftCustom = $spacing_bottom === 'custom';

                $spacing_styles = '';
                if ($isSpacingTopCustom) {
                    $spacing_styles .= "padding-top: {$custom_spacing_top}px; ";
                }
                if ($isSpacingRightCustom) {
                    $spacing_styles .= "padding-right: {$custom_spacing_right}px; ";
                }
                if ($isSpacingBottomCustom) {
                    $spacing_styles .= "padding-bottom: {$custom_spacing_bottom}px; ";
                }
                if ($isSpacingLeftCustom) {
                    $spacing_styles .= "padding-left: {$custom_spacing_left}px; ";
                }
                ?>

                <div 
                    class="block <?php echo $is_fullwidth . ' ' . 'top-spacing-' . $spacing_top ?> <?php echo 'right-spacing-' . $spacing_bottom ?> <?php echo 'bottom-spacing-' . $spacing_bottom ?> <?php echo 'left-spacing-' . $spacing_left ?>" 
                >
                    <div class="block-wrapper" style="<?php echo esc_attr($spacing_styles); ?>">
                        <?php get_template_part( 'template-parts/block', $block_type ); ?>
                    </div>
                </div>
                <?php
            }

            echo '</div>';
        
        } 
    } else {
        echo '<div> <!-- NO BLOCKS --> </div>';
    }
?>
</div>

<style>
    main .tinymce-output ol {
        margin-left: 1rem;
    }
    main .tinymce-output ol li {
        line-height: 2;
        margin-left: 0.5rem;
        padding-left: 1rem;
        padding-bottom: 2rem;
    }
    main .tinymce-output ul {
        margin-left: 1rem;
    }
    main .tinymce-output ul li {
        line-height: 2;
        margin-left: 0.5rem;
        padding-left: 1rem;
        padding-bottom: 1rem;
        position: relative;
    }
</style>
<?php
if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

// Angenommen $block enthält deine Datenstruktur
$textContent = $block['textContent'] ?? []; // Sicherstellen, dass textContent existiert
?>

<div id="<?php echo esc_attr( $block['id'] ); ?>" class="block-inner text-image-block">
    <!-- Text Content -->
    <div class="text-content" style="order: <?php echo $block['reverseOrder'] ?>">
        <?php if ( ! empty( $block['tagline'] ) ) : ?>
            <p class="tagline"><?php echo wp_kses_post( $block['tagline'] ); ?></p>
        <?php endif; ?>

        <?php if ( ! empty( $block['text'] ) ) : ?>
            <h2><?php echo wp_kses_post( $block['text'] ); ?></h2> <!-- Verändert zu <h2> für Überschriften -->
        <?php endif; ?>

        <?php foreach ($textContent as $content) : ?>
            <?php switch ($content['type']) :
                case 'headline':
                    echo '<h3>' . wp_kses_post($content['content']) . '</h3>';
                    break;
                case 'paragraph':
                    echo '<p class="intro">' . wp_kses_post($content['content']) . '</p>';
                    break;
                case 'list':
                    echo '<ul>';
                    foreach ($content['listItems'] as $listItem) {
                        echo '<li>' . wp_kses_post($listItem['content']) . '</li>';
                    }
                    echo '</ul>';
                    break;
                case 'button':
                    $data_modal = isset($content['useModal']) && $content['useModal'] ? 'data-modal-class="' . $content['link'] . '"' : '';

                    echo '<a href="'. wp_kses_post($content['link']) . '" ' . $data_modal . ' class="cta-button" style="padding-bottom: 40px;">';
                        echo '<div>';
                            echo wp_kses_post($content['content']);
                        echo '</div>';
                    echo '</a>';
                    break;
            endswitch; ?>
        <?php endforeach; ?>
    </div>

    <!-- Image -->
    <div class="image">
        <?php if ( ! empty( $block['image'] ) ) : ?>
            <img src="<?php echo esc_url( $block['image'] ); ?>" alt="Image">
        <?php endif; ?>
    </div>
</div>

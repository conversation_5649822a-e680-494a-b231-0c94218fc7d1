<?php
if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

// Angenommen $block enthält deine Datenstruktur
$spacing_top = $block['advancedSettings']['spacing']['top'];
$spacing_right = $block['advancedSettings']['spacing']['right'];
$spacing_bottom = $block['advancedSettings']['spacing']['bottom'];
$spacing_left = $block['advancedSettings']['spacing']['left'];

// CPT Beiträge holen
$args = array(
    'post_type' => 'externe_beitraege',
    'posts_per_page' => 4, // Anzahl der Beiträge, die du anzeigen möchtest
    'meta_query' => array(
        array(
            'key' => '_externe_beitraege_url', // Meta-Schlüssel für die externe URL
            'value' => '', // Leerer Wert
            'compare' => '!=', // Nur Beiträge mit einer nicht-leeren externen URL anzeigen
        ),
    ),
);

$query = new WP_Query($args);
?>

<div id="<?php echo esc_attr( $block['id'] ); ?>" class="post-list block-inner" style="padding-top: <?php echo esc_attr($spacing_top); ?>; padding-right: <?php echo esc_attr($spacing_right); ?>; padding-bottom: <?php echo esc_attr($spacing_bottom); ?>; padding-left: <?php echo esc_attr($spacing_left); ?>;">
    
    <div class="content-wrapper">
        <div class="content">
            <?php if ( ! empty( $block['tagline'] ) ) : ?>
                <p class="tagline"><?php echo wp_kses_post( $block['tagline'] ); ?></p>
            <?php endif; ?>

            <?php if ( ! empty( $block['heading'] ) ) : ?>
                <h2><?php echo wp_kses_post( $block['heading'] ); ?></h2>
            <?php endif; ?>

            <?php if ( ! empty( $block['content'] ) ) : ?>
                <?php $content_with_br = nl2br($block['content']); ?>
                <p class="paragraph" style="margin-bottom: 2rem;"><?php echo $content_with_br; ?></p>
            <?php endif; ?>
        </div>
    </div>

    <?php 
    if($block['postAppearance'] === 'featured') {
    ?>
    <div class="post-preview-module featured">
        <div class="post-preview-main">
            <?php if ( $query->have_posts() ) : ?>
                <?php $query->the_post(); // Der erste Beitrag wird als Hauptbeitrag verwendet ?>
                <div class="post-preview-featured">
                    <a href="<?php echo esc_url(get_post_meta(get_the_ID(), '_externe_beitraege_url', true)); ?>" target="_blank" rel="noopener noreferrer">
                        <img src="<?php echo esc_url(get_the_post_thumbnail_url()); ?>" alt="<?php echo esc_attr(get_the_title()); ?>" class="post-preview-image">
                        <div class="post-preview-content">
                            <div class="post-preview-meta">
                                <span class="post-preview-date"><?php echo esc_html(get_the_date('d M Y')); ?></span> 
                                <!--
                                | <span class="post-preview-author"><?php echo esc_html(get_the_author()); ?></span>
                                -->
                            </div>
                            <h2 class="post-preview-title"><?php echo esc_html(get_the_title()); ?></h2>
                            <p class="post-preview-excerpt"><?php echo esc_html(get_the_excerpt()); ?></p>
                            <div class="post-preview-tags">
                                <?php
                                // Spezielle Kategorien für den Custom Post Type "externe_beitraege" abrufen
                                $terms = get_the_terms(get_the_ID(), 'externe_kategorien');
                                if ($terms && !is_wp_error($terms)) {
                                    foreach ($terms as $term) {
                                        if (isset($term->name)) {
                                            echo '<span class="post-preview-tag">' . esc_html($term->name) . '</span>';
                                        }
                                    }
                                } 
                                ?>
                            </div>
                        </div>
                    </a>
                </div>
            <?php endif; ?>
        </div>
        <div class="post-preview-list">
            <?php while ( $query->have_posts() ) : ?>
                <?php $query->the_post(); ?>
                <div class="post-preview-item">
                    <a href="<?php echo esc_url(get_post_meta(get_the_ID(), '_externe_beitraege_url', true)); ?>" target="_blank" rel="noopener noreferrer">
                        <div class="post-image-wrapper">
                            <img src="<?php echo esc_url(get_the_post_thumbnail_url()); ?>" alt="<?php echo esc_attr(get_the_title()); ?>" class="post-preview-item-image">
                        </div>
                        <div class="post-preview-item-content">
                            <div class="post-preview-meta">
                                <span class="post-preview-date"><?php echo esc_html(get_the_date('d M Y')); ?></span>
                                <!-- |
                                <span class="post-preview-author"><?php echo esc_html(get_the_author()); ?></span>
                                -->
                            </div>
                            <h3 class="post-preview-item-title"><?php echo esc_html(get_the_title()); ?></h3>
                            <p class="post-preview-item-excerpt"><?php echo esc_html(get_the_excerpt()); ?></p>
                            <div class="post-preview-tags">
                                <?php
                                // Spezielle Kategorien für den Custom Post Type "externe_beitraege" abrufen
                                $terms = get_the_terms(get_the_ID(), 'externe_kategorien');
                                if ($terms && !is_wp_error($terms)) {
                                    foreach ($terms as $term) {
                                        if (isset($term->name)) {
                                            echo '<span class="post-preview-tag">' . esc_html($term->name) . '</span>';
                                        }
                                    }
                                } 
                                ?>
                            </div>
                        </div>
                    </a>
                </div>
            <?php endwhile; ?>
        </div>
    </div>
    <?php
    }
    ?>

    <?php 
    if($block['postAppearance'] === 'standard') {
    ?>
    <div class="post-preview-module standard">
         <div class="post-preview-list grid col-2">
            <?php while ( $query->have_posts() ) : ?>
                <?php $query->the_post(); ?>
                <div class="post-preview-item">
                    <a href="<?php echo esc_url(get_post_meta(get_the_ID(), '_externe_beitraege_url', true)); ?>" target="_blank" rel="noopener noreferrer">
                        <div class="post-image-wrapper">
                            <img src="<?php echo esc_url(get_the_post_thumbnail_url()); ?>" alt="<?php echo esc_attr(get_the_title()); ?>" class="post-preview-item-image">
                        </div>
                        <div class="post-preview-item-content">
                            <div class="post-preview-meta">
                                <span class="post-preview-author"><?php echo esc_html(get_the_author()); ?></span> • <span class="post-preview-date"><?php echo esc_html(get_the_date('d M Y')); ?></span>
                            </div>
                            <h3 class="post-preview-item-title"><?php echo esc_html(get_the_title()); ?></h3>
                            <p class="post-preview-item-excerpt"><?php echo esc_html(get_the_excerpt()); ?></p>
                            <div class="post-preview-tags">
                                <?php
                                // Spezielle Kategorien für den Custom Post Type "externe_beitraege" abrufen
                                $terms = get_the_terms(get_the_ID(), 'externe_kategorien');
                                if ($terms && !is_wp_error($terms)) {
                                    foreach ($terms as $term) {
                                        if (isset($term->name)) {
                                            echo '<span class="post-preview-tag">' . esc_html($term->name) . '</span>';
                                        }
                                    }
                                } 
                                ?>
                            </div>
                        </div>
                    </a>
                </div>
            <?php endwhile; ?>
        </div>
    </div>
    <?php
    }
    ?>
</div>

<?php wp_reset_postdata(); // Query resetten ?>



<style>
.post-preview-module.featured {
    display: grid;
    gap: 36px;
    grid-template-columns: 1fr 1fr;
}

.post-preview-main {
    flex: 2;
}

.post-preview-list {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 36px;
}

.post-preview-featured {
    position: relative;
    overflow: hidden;
    border-radius: 4px;
}

.post-preview-image {
    width: 100%;
    height: auto;
    object-fit: cover;
    border-radius: 4px;
}

.post-preview-content {
    padding-top: 1rem;
}

.post-preview-meta {
    font-size: 14px;
    opacity: 0.8;
    margin-bottom: 10px;
}

.post-preview-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 1rem;
}

.post-preview-excerpt {
    margin: 10px 0;
}

.post-preview-tags {
    margin-top: 10px;
}

.post-preview-tag {
    display: inline-block;
    background-color: var(--accent-color-secondary);
    color: #fff;
    padding: 5px 10px;
    margin-right: 5px;
    font-size: 12px;
    border-radius: 4px;
}

.featured .post-preview-item a {
    display: flex;
    gap: 10px;
}

.featured .post-image-wrapper {
    width: 50%;
}

.featured .post-preview-item-image {
    /* width: 50%; */
    height: auto;
    object-fit: cover;
    border-radius: 4px;
    aspect-ratio: 16 / 9;
    display: block;
}

.post-preview-item-title {
    font-size: 18px;
    color: var(--accent-color-secondary);
    font-weight: bold;
    margin: 0;
}

.post-preview-item-content {
    flex: 1;
}

/* standart list */ 
.post-list .grid {
    display: grid;
}
.post-list .col-2 {
    grid-template-columns: 1fr 1fr;
}

.post-preview-module.standard .post-preview-item {
    margin-bottom: 2rem;
}

.post-preview-module.standard .post-image-wrapper {
    width: 100%;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 1rem;
}
.post-preview-module.standard .post-image-wrapper img {  
    display: block;
}   

@media screen and (max-width: 999px) {
    /* Standard Grid */ 
    .post-list .col-2 {
        grid-template-columns: 1fr;
    }

    /* Featured Image */ 
    .post-preview-module.featured {
        grid-template-columns: 1fr;
    }
    .featured .post-preview-item a {
        display: grid;
        gap: 10px;
        grid-template-columns: 1fr;
    }
    .featured .post-image-wrapper {
        width: 100%;
    }

    .post-preview-title {
        font-size: 18px;
        color: var(--accent-color-secondary);
        font-weight: bold;
        margin: 0;
    }
}

</style>
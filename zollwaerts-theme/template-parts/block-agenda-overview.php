<?php

if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}


/** Get all "Sessions" */
if (!function_exists('get_sessions_data')) {
    function get_sessions_data($selected_date) {
        $sessions = new WP_Query(array(
            'post_type' => 'sessions',
            'posts_per_page' => -1,
            'meta_query' => array(
                array(
                    'key' => '_session_date', // Das Meta-Feld, das das Datum speichert
                    'value' => $selected_date,
                    'compare' => '='
                )
            )
        ));

        $sessions_data = array();
        $start_times = [];
        $end_times = [];

        while ($sessions->have_posts()) {
            $sessions->the_post();

            /** Get the time Data to collect */
            $start_time = get_post_meta(get_the_ID(), '_session_start_time', true);
            $end_time = get_post_meta(get_the_ID(), '_session_end_time', true);

            // collect times
            $start_times[] = $start_time;
            $end_times[] = $end_time;

            // Get location ID or null if not set
            $location_terms = wp_get_post_terms(get_the_ID(), 'session_location', array("fields" => "ids"));
            $location_id = isset($location_terms[0]) ? $location_terms[0] : null;

            // Get all session leaders
            $session_leaders = get_post_meta(get_the_ID(), '_session_leaders', true);
            $leaders = array();
            
            if (!empty($session_leaders) && is_array($session_leaders)) {
                foreach ($session_leaders as $leader) {
                    if (!empty($leader['name']) || !empty($leader['image_id'])) {
                        $leaders[] = array(
                            'name' => $leader['name'],
                            'image_url' => !empty($leader['image_id']) ? wp_get_attachment_url($leader['image_id']) : '',
                            'position' => isset($leader['position']) ? $leader['position'] : '' // Position hinzugefügt
                        );
                    }
                }
            }

            $sessions_data[] = array(
                'post_id' => get_the_ID(),
                'title' => get_the_title(),
                'subtitle' => get_post_meta(get_the_ID(), '_session_subtitle', true),
                'start_time' => $start_time,
                'end_time' => $end_time,
                'location_id' => $location_id,
                'leaders' => $leaders
            );
        }

        wp_reset_postdata();

        // Rest der Funktion bleibt gleich...
        return array(
            'sessions_data' => $sessions_data,
            'earliest_time' => min($start_times),
            'latest_time' => max($end_times)
        );
    }
}

// Den $selected_date Parameter an get_sessions_data übergeben
$selected_date = null;
$selected_date = isset($block['selectedDate']) && !empty($block['selectedDate']) ? $block['selectedDate'] : null; 


$sessions_info = get_sessions_data($selected_date);
$sessions_data = $sessions_info['sessions_data'];
$earliest_time = $sessions_info['earliest_time'];
$latest_time = $sessions_info['latest_time'];




/** Create time interval from earliest to latest, showing only full hours */
if(!function_exists('generate_time_intervals')) {
    function generate_time_intervals($earliest_time, $latest_time) {
        $intervals = [];
        if ($earliest_time && $latest_time) {
            // Starte bei der nächsten vollen Stunde vor oder gleich der frühesten Zeit
            $current_time = strtotime(date('H:00', strtotime($earliest_time)));
            $end_time = strtotime($latest_time);
    
            // Gehe über die Zeiträume hinweg und addiere 1 Stunde
            while ($current_time <= $end_time) {
                $intervals[] = date('H:i', $current_time);
                $current_time = strtotime('+1 hour', $current_time);
            }
        }
    
        return $intervals;
    }
}

$time_intervals = generate_time_intervals($earliest_time, $latest_time);



/** Get time offset for first session */
if(!function_exists('get_time_offset')) {
    function get_time_offset($earliest_time) {
        // Berechne die nächste volle Stunde, die der frühesten Zeit am nächsten liegt, aber nicht später als sie
        $earliest_full_hour = date('H:00', strtotime($earliest_time));
        $earliest_full_hour_in_minutes = convert_time_to_minutes($earliest_full_hour);
        $earliest_time_in_minutes = convert_time_to_minutes($earliest_time);
        
        // Differenz in Minuten zwischen der frühesten Zeit und der nächsten vollen Stunde
        return $earliest_time_in_minutes - $earliest_full_hour_in_minutes;
    }
}

if(!function_exists('convert_time_to_minutes')) {
    function convert_time_to_minutes($time) {
        $parts = explode(':', $time);
        return intval($parts[0]) * 60 + intval($parts[1]);
    }
}

$time_offset = get_time_offset($earliest_time);

/** Get all "Session Target Audience" Categories */
$target_audiences = get_terms(array(
    'taxonomy' => 'target_audience',
    'hide_empty' => true, // Zeigt nur Kategorien, die in Posts verwendet werden
    'object_ids' => get_posts(array(
        'post_type' => 'sessions',
        'fields' => 'ids', // Holt nur die IDs der Posts
        'posts_per_page' => -1, // Holt alle Posts
    )),
));


/** Get all "Session Locations" */
$locations = get_terms(array(
    'taxonomy' => 'session_location',
    'hide_empty' => false,
));
?>

<div id="<?php echo esc_attr( $block['id'] ); ?>" class="block-inner agenda-overview">
    <div class="content-wrapper">
        <div class="content">
            <?php if ( ! empty( $block['tagline'] ) ) : ?>
                <p class="tagline"><?php echo wp_kses_post( $block['tagline'] ); ?></p>
            <?php endif; ?>

            <?php if ( ! empty( $block['heading'] ) ) : ?>
                <h2><?php echo wp_kses_post( $block['heading'] ); ?></h2>
            <?php endif; ?>

            <?php if ( ! empty( $block['content'] ) ) : ?>
                <?php $content_with_br = nl2br($block['content']); ?>
                <p class="paragraph"><?php echo $content_with_br; ?></p>
            <?php endif; ?>
        </div>


        <?php
        if (!$selected_date || empty($selected_date)) {
            echo '<p>No date selected</p>';
            echo '</div>';
            echo '</div>';
            return;
        }
        ?>
        

        <div class="filter-buttons">
            <button class="filter-button" data-target-audience="all">Alle</button>
            <?php foreach ($target_audiences as $audience) : ?>
                <button class="filter-button" data-target-audience="<?php echo esc_attr($audience->slug); ?>">
                    <?php echo esc_html($audience->name); ?>
                </button>
            <?php endforeach; ?>
        </div>

        <div class="agenda-wrapper">
            <div class="agenda-column time-indicator">
                <?php foreach ($time_intervals as $interval) : ?>
                    <div class="time-slot">
                        <?php echo esc_html($interval); ?>
                    </div>
                <?php endforeach; ?>
            </div>

            <div class="agenda-table" data-earliest-time="<?php echo esc_attr($earliest_time); ?>" style="display: grid; grid-template-columns: repeat(<?php echo count($locations); ?>, 1fr);">
                <!-- Render global events -->
                <div class="global-events">
                    <?php
                    foreach ($sessions_data as $session) {
                        // Überprüfen, ob das Event global ist
                        $is_global_event = get_post_meta($session['post_id'], '_global_event', true);

                        // Session Leader Daten abrufen
                        $leader_name = get_post_meta($session['post_id'], '_session_leader_name', true);
                        $leader_image_id = get_post_meta($session['post_id'], '_session_leader_image_id', true);
                        $leader_image_url = $leader_image_id ? wp_get_attachment_url($leader_image_id) : '';

                        // Partner-Status bereits hier abrufen
                        $is_partner = get_post_meta($session['post_id'], '_is_partner', true);
                        
                        if ($is_global_event) {
                            $post_id = $session['post_id'];

                            $audiences = wp_get_post_terms($post_id, 'target_audience', array("fields" => "slugs"));

                            // Abrufen des Session Type
                            $session_types = wp_get_post_terms($post_id, 'session_type', array("fields" => "names"));
                            // Prüfen, ob der Meta-Wert gesetzt ist
                            $no_link_to_detail = get_post_meta($post_id, '_no_link_to_detail', true);
                            ?>

                            <?php if(!$no_link_to_detail) : ?>
                            <a href="<?php echo get_permalink($post_id); ?>" class="single-agenda-item-link">
                            <?php endif; ?>
                                <div 
                                    class="single-agenda-item global-event" 
                                    data-start-time="<?php echo esc_attr($session['start_time']); ?>"
                                    data-end-time="<?php echo esc_attr($session['end_time']); ?>"
                                    data-target-audience="<?php echo implode(' ', array_map('esc_attr', $audiences)); ?>"
                                >
                                    <?php if (!empty($session_types)) : ?>
                                        <div class="session-meta">
                                            <div class="session-type-wrapper">
                                                <?php if (!empty($session_types)) : ?>
                                                    <?php foreach ($session_types as $type) : ?>
                                                        <p class="session-type">
                                                            <?php echo esc_html($type); ?>
                                                        </p>
                                                    <?php endforeach; ?>
                                                <?php endif; ?>

                                                <?php if ($is_partner) : ?>
                                                    <p class="session-type">
                                                        <span class="Partner">Partner</span>
                                                    </p>
                                                <?php endif; ?>
                                            </div>
                                            <div class="time-wrapper">
                                                <?php if (!empty($session['start_time']) && !empty($session['end_time'])) : ?>
                                                    <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 24 24">
                                                        <path fill-rule="evenodd" d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm11-4a1 1 0 1 0-2 0v4a1 1 0 0 0 .293.707l3 3a1 1 0 0 0 1.414-1.414L13 11.586V8Z" clip-rule="evenodd"/>
                                                    </svg>
                                                    <?php echo esc_html($session['start_time']); ?> - <?php echo esc_html($session['end_time']); ?>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    <h5><?php echo esc_html($session['title']); ?></h5>
                                    <?php if (!empty($session['subtitle'])) : ?>
                                        <p class="session-subtitle"><?php echo esc_html($session['subtitle']); ?></p>
                                    <?php endif; ?>

                                    <!-- Session-Leader: Für globale Events -->
                                    <div class="session-leader-wrapper">
                                        <?php
                                        if (!empty($session['leaders'])) {
                                            foreach ($session['leaders'] as $leader) {
                                                echo '<div class="session-leader">';
                                                    if ($leader['image_url']) {
                                                        echo '<img src="' . esc_url($leader['image_url']) . '" alt="' . esc_attr($leader['name']) . '" class="session-leader-image">';
                                                    }
                                                    echo '<div class="session-leader-info">';
                                                        if ($leader['name']) {
                                                            echo '<h4 class="session-leader-name">' . esc_html($leader['name']) . '</h4>';
                                                        }
                                                        if ($leader['position']) {
                                                            echo '<p class="session-leader-position">' . esc_html($leader['position']) . '</p>';
                                                        } 
                                                        // else {
                                                        //     // print whole leader array
                                                        //     echo '<pre>';
                                                        //     print_r($leader);
                                                        //     echo '</pre>';
                                                        // }
                                                    echo '</div>';
                                                echo '</div>';
                                            }
                                        } 
                                        // else {
                                        //     echo '<div class="session-leader">';
                                        //         if ($leader_image_url) {
                                        //             echo '<img src="' . esc_url($leader_image_url) . '" alt="' . esc_attr($leader_name) . '" class="session-leader-image">';
                                        //         }
                                        //         if ($leader_name) {
                                        //             echo '<h4 class="session-leader-name">' . esc_html($leader_name) . '</h4>';
                                        //         }
                                        //     echo '</div>';
                                        // }
                                        ?>
                                    </div>

                                </div>
                            <?php if(!$no_link_to_detail) : ?>
                            </a>
                            <?php endif; ?>

                            <?php
                        }
                    }
                    ?>
                </div>

                <?php foreach ($locations as $location) : ?>
                    <div class="agenda-column">
                        <!-- Column Header -->
                        <div class="agenda-column-header">
                            <h4><?php echo esc_html($location->name); ?></h4>
                        </div>

                    
                        <!-- Actual Sessions -->
                        <?php
                        foreach ($sessions_data as $session) {
                            if ($location->term_id == $session['location_id']) {
                                // Verwende die richtige Post-ID für wp_get_post_terms
                                $post_id = $session['post_id'];
                                $audiences = wp_get_post_terms($post_id, 'target_audience', array("fields" => "slugs"));

                                // Abrufen des Session Type
                                $session_types = wp_get_post_terms($post_id, 'session_type', array("fields" => "names"));
                                
                                // Prüfen, ob der Meta-Wert gesetzt ist
                                $no_link_to_detail = get_post_meta($post_id, '_no_link_to_detail', true);

                                // Session Leader Daten abrufen
                                $leader_name = get_post_meta($session['post_id'], '_session_leader_name', true);
                                $leader_image_id = get_post_meta($session['post_id'], '_session_leader_image_id', true);
                                $leader_image_url = $leader_image_id ? wp_get_attachment_url($leader_image_id) : '';

                                // Partner-Status bereits hier abrufen
                                $is_partner = get_post_meta($session['post_id'], '_is_partner', true);
                                ?>

                                <?php if(!$no_link_to_detail) : ?>
                                <a href="<?php echo get_permalink($post_id); ?>" class="single-agenda-item-link">
                                <?php endif; ?>
                                    <div 
                                        class="single-agenda-item" 
                                        data-start-time="<?php echo esc_attr($session['start_time']); ?>"
                                        data-end-time="<?php echo esc_attr($session['end_time']); ?>"
                                        data-target-audience="<?php echo implode(' ', array_map('esc_attr', $audiences)); ?>"
                                    >
                                        <?php if (!empty($session_types)) : ?>
                                            <div class="session-meta">
                                                <div class="session-type-wrapper">
                                                    <?php if (!empty($session_types)) : ?>
                                                        <?php foreach ($session_types as $type) : ?>
                                                            <p class="session-type">
                                                                <?php echo esc_html($type); ?>
                                                            </p>
                                                        <?php endforeach; ?>
                                                    <?php endif; ?>

                                                    <?php if ($is_partner) : ?>
                                                        <p class="session-type partner">
                                                            <span>Partner</span>
                                                        </p>
                                                    <?php endif; ?>
                                                </div>
                                                <div class="time-wrapper">
                                                    <?php if (!empty($session['start_time']) && !empty($session['end_time'])) : ?>
                                                        <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="18" height="18" fill="currentColor" viewBox="0 0 24 24">
                                                            <path fill-rule="evenodd" d="M2 12C2 6.477 6.477 2 12 2s10 4.477 10 10-4.477 10-10 10S2 17.523 2 12Zm11-4a1 1 0 1 0-2 0v4a1 1 0 0 0 .293.707l3 3a1 1 0 0 0 1.414-1.414L13 11.586V8Z" clip-rule="evenodd"/>
                                                        </svg>
                                                        <?php echo esc_html($session['start_time']); ?> - <?php echo esc_html($session['end_time']); ?>
                                                    <?php endif; ?>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                        <h5><?php echo esc_html($session['title']); ?></h5>
                                        <?php if (!empty($session['subtitle'])) : ?>
                                            <p class="session-subtitle"><?php echo esc_html($session['subtitle']); ?></p>
                                        <?php endif; ?>

                                        <!-- Session-Leader: Für normale Sessions in den Location-Spalten -->
                                        <div class="session-leader-wrapper">
                                            <?php
                                            if (!empty($session['leaders'])) {
                                                foreach ($session['leaders'] as $leader) {
                                                    echo '<div class="session-leader">';
                                                        if ($leader['image_url']) {
                                                            echo '<img src="' . esc_url($leader['image_url']) . '" alt="' . esc_attr($leader['name']) . '" class="session-leader-image">';
                                                        }
                                                        echo '<div class="session-leader-info">';
                                                            if ($leader['name']) {
                                                                echo '<h4 class="session-leader-name">' . esc_html($leader['name']) . '</h4>';
                                                            }
                                                            if ($leader['position']) {
                                                                echo '<p class="session-leader-position">' . esc_html($leader['position']) . '</p>';
                                                            } 
                                                            // else {
                                                            //     // print whole leader array
                                                            //     echo '<pre>';
                                                            //     print_r($leader);
                                                            //     echo '</pre>';
                                                            // }
                                                        echo '</div>';
                                                    echo '</div>';
                                                }
                                            }
                                            // else {
                                            //     echo '<div class="session-leader">';
                                            //         if ($leader_image_url) {
                                            //             echo '<img src="' . esc_url($leader_image_url) . '" alt="' . esc_attr($leader_name) . '" class="session-leader-image">';
                                            //         }
                                            //         if ($leader_name) {
                                            //             echo '<h4 class="session-leader-name">' . esc_html($leader_name) . '</h4>';
                                            //         }
                                            //     echo '</div>';
                                            // }
                                            ?>
                                        </div>
                                        
                                    </div>
                                <?php if(!$no_link_to_detail) : ?>
                                </a>
                                <?php endif; ?>
                                <?php
                            }
                        }
                        ?>

                        <!-- Just for optical purposes -->
                        <div class="time-slot-wrapper">
                            <?php foreach ($time_intervals as $interval) : ?>
                                <div class="time-slot">
                                    <?php echo '<div class="optical-row"></div>'; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>
</div>

<?php
// Globale Variable oder statische Variable verwenden, um zu prüfen, ob das Skript bereits eingebunden wurde
global $agenda_overview_scripts_included;
if (!isset($agenda_overview_scripts_included)) {
    $agenda_overview_scripts_included = false;
}

if (!$agenda_overview_scripts_included) {
?>  
    <script>
    document.addEventListener('DOMContentLoaded', function() {

        const agendaOverviewModules = document.querySelectorAll('.agenda-overview');

        agendaOverviewModules.forEach(function(agendaModule) {
            /** position the sessions */
            const agendaTable = agendaModule.querySelector('.agenda-table');

            const earliestTime = agendaTable.getAttribute('data-earliest-time'); // Holen der spezifischen earliest_time für diese Tabelle
            console.log('earliestTime: ', earliestTime);
            // const minutesFromPreviousFullHour = getMinutesFromPreviousFullHour(earliestTime);
            // console.log('minutesFromPreviousFullHour: ', minutesFromPreviousFullHour);
            // const earliestFullHour = earliestTime && minutesFromPreviousFullHour ? earliestTime - minutesFromPreviousFullHour : '08:00';
            const earliestFullHour = earliestTime ? getFullHour(earliestTime) : '08:00';
            console.log('earliestFullHour after conversion to full hour: ', earliestFullHour);
            const earliestFullHourInMinutes = convertTimeToMinutes(earliestFullHour);
            console.log('earliestFullHourInMinutes: ', earliestFullHourInMinutes);
        
            const padding = 5; // Abstand also Addition bzw. Abzug oben und unten 
            const timeSlotHeight = 304; // Höhe des Zeitintervalls in Pixeln (z.B. 108px für jede Stunde)
        
            agendaModule.querySelectorAll('.single-agenda-item').forEach(function(item) {
                const startTime = item.getAttribute('data-start-time'); // Zeit im Format 'HH:mm'
                const endTime = item.getAttribute('data-end-time'); // Endzeit im Format 'HH:mm'
                const startTimeInMinutes = convertTimeToMinutes(startTime);
                const endTimeInMinutes = convertTimeToMinutes(endTime);
        
                // Berechne die Differenz in Minuten zur frühesten vollen Stunde
                const timeDifference = startTimeInMinutes - earliestFullHourInMinutes;
        
                // Berechne das margin-top basierend auf der Zeitdifferenz
                const marginTop = (timeDifference / 60) * timeSlotHeight + padding; // Umrechnung in Pixel
                item.style.marginTop = marginTop + 'px';
        
                // Berechne die Höhe basierend auf der Dauer
                const eventDuration = endTimeInMinutes - startTimeInMinutes;
                const height = (eventDuration / 60) * timeSlotHeight - (padding * 2); // Umrechnung in Pixel
                item.style.height = height + 'px';
            });
        
            function convertTimeToMinutes(time) {
                const parts = time.split(':');
                return parseInt(parts[0]) * 60 + parseInt(parts[1]);
            }
        
            /** Filter Button functions */
            const filterButtons = agendaModule.querySelectorAll('.filter-button');
            const agendaItems = agendaModule.querySelectorAll('.single-agenda-item');
        
            function applyFilter(targetAudience) {
                agendaItems.forEach(item => {
                    const itemAudiences = item.getAttribute('data-target-audience').split(' ');
        
                    if (targetAudience === 'all') {
                        item.classList.remove('active'); // reset to default
                        item.classList.remove('inactive'); // reset to default
                    } else if (itemAudiences.includes(targetAudience)) {
                        item.classList.add('active'); // Zeigt die Session an
                        item.classList.remove('inactive'); // Entfernt die Inactive-Klasse
                    } else {
                        item.classList.add('inactive'); // Blendet die Session aus
                        item.classList.remove('active'); // Entfernt die Active-Klasse
                    }
                });
        
                // Active Button markieren
                filterButtons.forEach(btn => btn.classList.remove('active'));
                agendaModule.querySelector(`.filter-button[data-target-audience="${targetAudience}"]`).classList.add('active');
            }
        
            // Funktion, um die URL-Parameter zu setzen
            function setUrlParameter(param, value) {
                const url = new URL(window.location);
                url.searchParams.set(param, value);
                window.history.pushState({}, '', url);
            }
        
            // Bei Klick auf einen Button den Filter anwenden und die URL aktualisieren
            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const targetAudience = this.getAttribute('data-target-audience');
                    applyFilter(targetAudience);
                    setUrlParameter('audience', targetAudience);
                });
            });
        
            // URL-Parameter auslesen und Filter anwenden
            const urlParams = new URLSearchParams(window.location.search);
            const initialAudience = urlParams.get('audience') || 'all';
            applyFilter(initialAudience);

            // Helper functions
            function convertTimeToMinutes(time) {
                const parts = time.split(':');
                return parseInt(parts[0]) * 60 + parseInt(parts[1]);
            }

            function dateToHour(dateString) {
                const date = new Date(dateString);
                let hours = date.getHours();
                let minutes = date.getMinutes();
                if (minutes < 10) minutes = "0" + minutes;
                return hours + ":" + minutes;
            }

            function getFullHour(time) {
                const [hours, minutes] = time.split(':').map(Number); // Zerlegen der Zeit in Stunden und Minuten
                const fullHour = hours; // Die Stunden bleiben übrig, da die volle Stunde der Nullpunkt ist
                return `${fullHour}:00`; // fullHour;
            }

            function getMinutesFromPreviousFullHour(time) {
                const [hours, minutes] = time.split(':').map(Number); // Zerlegen der Zeit in Stunden und Minuten
                const minutesFromPreviousFullHour = minutes; // Die Minuten bleiben übrig, da die volle Stunde der Nullpunkt ist
                return minutesFromPreviousFullHour;
            }
        })
    });

    </script>

    <style>
    /* html {
        font-size: 18px;
    } */

    .block-inner.agenda-overview .content p.paragraph {
        padding-bottom: 40px;
        color: #606081;
    }

    .agenda-wrapper {
        display: flex;
        --agenda-col-width: 240px;
        --time-indicator-width: 66px;
    }

    /** Filter Buttons */
    .filter-buttons {
        position: relative;
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        /* border: solid 1px #ddd; */
        border-radius: 1rem;
        /* overflow: hidden; */
        /* overflow-x: auto; */
        width: 100%;
        border-radius: 4px;
        margin-bottom: 1rem;
    }

    button.filter-button {
        background: #10114a;
        color: #fff;
        outline: none;
        border: none;
        padding: 0.6rem 1rem;
        flex-grow: 1;
        font-size: 1rem;
        border-radius: 4px;
        cursor: pointer;
        transition: background 0.3s var(--effect-transition-timing-function);
    }
    button.filter-button.active {
        background: var(--accent-color);
        color: #fff;
    }

    /** Agenda Table */
    .agenda-table {
        position: relative;
        border: solid 1px #ddd;
        border-top-right-radius: 1rem;
        border-bottom-right-radius: 1rem;
        overflow: hidden;
        overflow-x: auto;
        width: 100%;
    }

    .time-indicator {
        padding-top: 4rem;
        border-right: 1px solid #ddd;
        width: var(--time-indicator-width);
    }
    /* new time indicator outside of agenda */ 
    .time-indicator {
        padding-top: 4rem;
        border: solid 1px #ddd;
        width: var(--time-indicator-width);
        border-right: none !important;
        border-top-left-radius: 1rem;
        border-bottom-left-radius: 1rem;
        overflow: hidden;
    }

    .time-indicator .time-slot {
        /* padding: 0 1rem; */
        justify-content: center;
    }

    .time-slot {
        display: flex;
        align-items: baseline;
        height: 304px;
        border-top: 1px solid #ddd;
        text-align: center;
        padding-top: 5px !important;
        color: #10114a;
    }

    .time-slot:nth-child(odd) {
        background-color: #f9f9f9;
    }
    /* .time-slot:nth-child(even) {
        background-color: #fff;
    } */

    /** Global Sessions (e.g. Mittagspause) */
    .global-events {
        position: absolute;
        margin-top: 64px;
        /* padding-left: calc(var(--time-indicator-width)); */
        z-index: 9;
        width: 100%;
        /* min-width: calc(var(--time-indicator-width) + var(--agenda-col-width) * <?php echo count($locations); ?>); */
        min-width: calc(var(--agenda-col-width) * <?php echo count($locations); ?>);
        height: 0px;
    }

    .single-agenda-item.global-event {
        background: #aeb4bd;
        margin: 0 5px;
        border-radius: 0.5rem;
        border-radius: 0.5rem;
        padding: 0.5rem 1rem;
        margin: 0 5px;
        width: calc(100% - 10px);
        min-width: calc(100% - var(--time-indicator-width) - 10px);
        position: absolute;
        box-sizing: border-box;
        overflow: hidden;
    }
    .single-agenda-item.global-event h5 {
        color: #fff;
        margin-top: 0.25rem !important;
    }

    /* Agenda Column */
    .agenda-column { 
        position: relative;
        border-right: solid 1px #ddd;
        background: #fff;
    }
    .agenda-column:last-child {
        border-right: none;
    }

    .agenda-column-header {
        min-width: var(--agenda-col-width);
        width: var(--agenda-col-width);
        padding: 0 1rem;
    }

    .single-agenda-item {
        padding: 0 1rem;
    }

    .agenda-column .agenda-column-header h4 {
        height: 4rem;
        display: flex;
        align-items: center;
        /* color: #fff; */
        /* font-size: 1.5rem; */
        font-weight: 400;
        justify-content: center;
    }
    /* 606081 */

    .agenda-column .single-agenda-item {
        position: absolute;
        overflow: hidden;
        box-sizing: border-box;
        /* width: 100%; */
        width: calc(100% - 5px - 5px);
        /* border: solid 1px #ffffff; */
        background: rgb(209 210 224);
        background: linear-gradient(180deg, rgba(209 210 224, 1) 0%, rgba(255 255 255, 1) 100%);
        background: #10114a;
        border-radius: 0.5rem;
        padding: 0.5rem 0.5rem;
        margin: 0 5px;

        word-break: keep-all;
        overflow-wrap: break-word;
        hyphens: auto;
        /* overflow: hidden; */

        transition: background 0.3s var(--effect-transition-timing-function), opacity 0.3s var(--effect-transition-timing-function);
    }
    .agenda-column .single-agenda-item.active {
        /* background: #3e3f5e;
        background: var(--accent-color); */
    }
    .agenda-column .single-agenda-item.inactive {
        opacity: 0.3;
        /* scale: 0.5; */
        filter: saturate(0.0);
    }

    .agenda-column a .single-agenda-item:hover {
        background: #3e3f5e;
        background: var(--accent-color);
        opacity: 1;
        filter: saturate(1.0);
    }

    /**
     * Session meta 
     */
    .session-meta {
        display: flex;
        justify-content: space-between;
        color: #fff;
    }

    

        /* Session types */ 
    .single-agenda-item .session-type-wrapper {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        margin-bottom: 0.25rem;
        overflow: hidden;
        box-sizing: border-box;
        max-width: 100%;
        width: fit-content;
    }
    .single-agenda-item .session-type-wrapper .session-type {
        padding: 0rem 0.25rem;
        letter-spacing: -0.5px;
        line-height: 1.5;
        margin: 0 !important;
        background: var(--accent-color);
        background: #10114a;
        background: #606081;
        color: #fff;
        font-size: 0.75rem;
        font-weight: 400;
        border-radius: 1rem;

        transition: all 0.3s var(--effect-transition-timing-function);
    }

    a .single-agenda-item:hover .session-type-wrapper .session-type {
        background: #fff;
        color: #10114a;
    }
    

    .single-agenda-item .session-type-wrapper .session-type.partner {
        background: var(--accent-color);
    }
    a .single-agenda-item:hover .session-type-wrapper .session-type.partner {
        background: var(--accent-color-secondary);
        color: #fff;
    }

    .single-agenda-item .session-meta .time-wrapper {
        line-height: 1.5rem; */
        font-size: 0.75rem;
        color: #fff;
        display: flex;
        align-items: center;
        height: 20px;
        font-size: 0.75rem;
    }
    .single-agenda-item .session-meta .time-wrapper svg {
        margin-right: 0.25rem;
    }

    .single-agenda-item h5 {
        color: #fff;
        font-size: 16px;
        line-height: 1.2;
        font-weight: 500;
        margin-top: 0.25rem;
        margin-bottom: 0.5rem;
        text-transform: none;

        margin: 0 !important;
        padding: 0 !important;
    }

    p.session-subtitle {
        color: #fff;
        line-height: 1.25;
        font-size: 14px;
        margin-top: 6px;
        margin-bottom: 0;
    }

    .session-leader {
        margin-top: 0.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: #fff;
    }

    .session-leader img {
        width: 50px;
        height: 50px;
        border-radius: 999px;
        object-fit: cover;
    }

   

    .session-leader h4 {
        color: #fff;
        font-size: 16px;
        font-weigth: 400;
        font-style: italic;
        margin: 0;
        padding: 0;
    }

    p.session-leader-position {
        color: #fff;
        font-size: 15px;
        font-weigth: 400 !important;
        font-style: normal;
        margin: 0;
        padding: 0;
        line-height: 1.5;
    }


     @media screen and (max-width: 1240px) {
        .session-leader img {
            width: 45px;
            height: 46px;
        }
        .session-leader h4 {
            font-size: 15px;
        }
    }

    </style>
<?php
$agenda_overview_scripts_included = true;
}
?>


<style>
div#<?php echo esc_attr($block['id']); ?> {
    --bg-one-opacity: 0;
    /* --bg-one-transformation-size: 16%;
    --bg-one-transformation-x: 80%; */
    --bg-one-transformation-size: 39%;
    --bg-one-transformation-x: 38%;

    /* overflow: hidden; */
    position: relative;
}

div#<?php echo esc_attr($block['id']); ?>:before {
    content: '';
    z-index: -1;
    width: 100vw;
    height: 100vw;
    position: absolute;
    margin-left: calc(-50vw + 50%);
    margin-top: -6%;

    /* background: radial-gradient(circle at 12% var(--bg-one-transformation-x), rgb(191 22 146 / 40%) 0%, #eee0 var(--bg-one-transformation-size)), radial-gradient(circle at 31% 36%, rgb(24 212 242 / 33%) 0%, #eee0 24%); */
    background: radial-gradient(circle at 75% var(--bg-one-transformation-x), rgb(191 22 146 / 40%) 0%, #eee0 var(--bg-one-transformation-size)), radial-gradient(circle at 49% 29%, rgb(24 212 242 / 33%) 0%, #eee0 24%);
    /* opacity: var(--bg-one-opacity); */
    opacity: 0.5; 
}
</style>

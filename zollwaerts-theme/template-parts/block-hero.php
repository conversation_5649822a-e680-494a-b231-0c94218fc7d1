<?php
if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

/**
 * Background Video or Bubbles 
 */  
$useImageVideoBackground = $block['useImageVideoBackground'];
$blockCtaLink = isset($block['ctaLink']) ? $block['ctaLink'] : '';
$modalDataAttribute = isset($block['ctaUseModal']) && $block['ctaUseModal'] ? ' data-modal-class="' . $blockCtaLink . '"' : '';

$unique_id = uniqid('uid-');


/**
 * use Typing Animation 
 */  
$useTypingAnimation = 0;
if(isset($block['useTypingAnimation']) && $block['useTypingAnimation']) {
    $useTypingAnimation = 1;
}

$typingAnimationContent = '';
// $typingAnimationContentArray = [];
if(isset($block['typingAnimationContent']) && $block['typingAnimationContent']) {
    $typingAnimationContent = $block['typingAnimationContent'];
    // seperate string by commas
    // $typingAnimationContentArray = explode(',', $typingAnimationContent);
}

$headlineAfterTyping = '';
if(isset($block['headlineAfterTyping']) && $block['headlineAfterTyping']) {
    $headlineAfterTyping = $block['headlineAfterTyping'];
}



/**
 * Video Lightsbox 
 */  
$heroIsVideoLightbox = isset($block['bubbleBackgrounds']['rightBubbles']['first']['isVideoLightbox']) && $block['bubbleBackgrounds']['rightBubbles']['first']['isVideoLightbox'];

$bubbleRightFirstImageId = null;
$posterId = isset($block['bubbleBackgrounds']['rightBubbles']['first']['videoSettings']['poster']['id']) ? $block['bubbleBackgrounds']['rightBubbles']['first']['videoSettings']['poster']['id'] : null;
$rightBubbleFirstImageId = isset($block['bubbleBackgrounds']['rightBubbles']['first']['id']) ? $block['bubbleBackgrounds']['rightBubbles']['first']['id'] : null;
$bubbleRightFirstImageId = $heroIsVideoLightbox ? $posterId : $rightBubbleFirstImageId;

$bubbleRightFirstImageUrl = '';
if($bubbleRightFirstImageId) {
    $bubbleRightFirstImageUrl = wp_get_attachment_url($bubbleRightFirstImageId);
}

// gets viodeo id if set 
$lightboxVideoId = $heroIsVideoLightbox ? $block['bubbleBackgrounds']['rightBubbles']['first']['videoSettings']['id'] : '';
// gets viodeo url from id
$lightboxVideoUrl;
if($lightboxVideoId) {
    $lightboxVideoUrl = wp_get_attachment_url($lightboxVideoId);
}


?>

<div class="mouse-circle"></div>
<div id="rect-info-panel"></div>

<div id="<?php echo wp_kses_post( $block['id'] ); ?>" class="block-inner hero <?php echo $block['useImageVideoBackground'] ? 'background-hero' : 'basic-hero'; ?>">
    <?php 
    if($block['useImageVideoBackground']) {
    ?>
        <div class="background-wrapper">
            <?php if(isset($block['background']['url'])) { ?>
                <?php if(isset($block['backgroundType']) && $block['backgroundType'] === 'image') { ?>
                    <img class="background-image" src="<?php echo wp_kses_post( $block['background']['url'] ); ?>" alt="Background Image">
                <?php } else { ?>
                    <video class="background-video" width="100%" muted playsinline autoplay loop>
                        <source src="<?php echo wp_kses_post( $block['background']['url'] ); ?>">
                    </video>
                <?php } ?>
            <?php } ?>
            <!-- <canvas id="stage" class="background-overlay"></canvas> -->
        </div>
        <div class="content-wrapper video-hero <?php echo wp_kses_post( $block['isCentered'] ? 'center' : '' ); ?>">
            <!-- <h3><?php echo wp_kses_post( $block['id'] ); ?></h3> -->
            <h3>
                <?php echo wp_kses_post( $block['tagline'] ); ?>
            </h3>
            <div class="main-heading">
                <!-- <canvas class="hero-headline-scene"></canvas> -->
                <!-- <svg class="hero-headline">
                    <text x="-1" y="70" font-family="Poppins, sans-serif" font-size="4rem" font-weight="800" fill="#fff">
                        <tspan>Der Kongress für ​Innovation in</tspan>
                        <tspan x="-1" dy="97">Z</tspan>
                    </text>
                </svg> -->
                <div class="headline-wrapper">
                    <h1 class="hero-heading"><?php echo wp_kses_post( $block['headline'] ); ?></h1>
                </div>
            </div>
            <p><?php echo nl2br(esc_html( $block['content'] )); ?></p>
            <?php
            if($block['ctaLink']) {
            ?>
                <a href="<?php echo wp_kses_post( $block['ctaLink'] ); ?>" <?php echo wp_kses_post($modalDataAttribute); ?> class="cta-button">
                    <div>
                        <?php echo wp_kses_post( $block['ctaText'] ); ?>
                        <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 12H5m14 0-4 4m4-4-4-4"/>
                        </svg>

                    </div>
                </a>
            <?php
            }
            ?>
        </div>
    <?php
    } else {
    ?>
        <div class="content-wrapper hero-wrapper basic-hero <?php echo $heroIsVideoLightbox ? 'contains-video-lightbox' : ''; ?> <?php echo wp_kses_post( $block['isCentered'] ? 'center' : '' ); ?>">
            <div class="left-side">

                <!-- Left Bubble -->
                <div class="left-bubble first">
                    <?php
                    if($block['bubbleBackgrounds']['leftBubble'] && $block['bubbleBackgrounds']['leftBubble']['first'] && $block['bubbleBackgrounds']['leftBubble']['first']['url']) {
                    ?>
                        <svg class="active" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 116 116">
                            <defs>
                                <mask id="left-bubble-first-mask-<?php echo $block['id'] ?>-<?php echo $unique_id ?>">
                                    <!-- <path class="popup-anim path-anim" d="M 8.5668 84.6681 c 9.7064 19.2027 24.9249 27.4879 45.6544 24.8556 c 20.7295 -2.6312 37.312 -12.232 49.7508 -28.8035 c 12.4377 -16.5704 12.4377 -33.1892 0 -49.8575 C 91.5343 14.1944 74.7065 4.1547 53.4897 0.7436 C 32.2718 -2.6686 17.0544 5.665 7.8353 25.7455 c -9.218 20.0794 -8.9738 39.721 0.7315 58.9226 Z" data-path-to="M20.788 76.971c8.824 17.457 22.659 24.989 41.504 22.596 18.845-2.392 33.92-11.12 45.228-26.185 11.307-15.064 11.307-30.172 0-45.325C96.213 12.904 80.915 3.777 61.627.676 42.338-2.426 28.504 5.15 20.123 23.405c-8.38 18.254-8.158 36.11.665 53.566Z" data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" fill="#fff">
                                    </path> -->
                                    <!-- d="M 107.4332 23.7987 c -9.7064 -17.2824 -24.9249 -24.7391 -45.6544 -22.37 c -20.7295 2.3681 -37.312 11.0088 -49.7788 21.1713 c -13 10.4 -17 29.4 4 51.4 C 21 80 35 95 62.5103 99.3308 C 83.7282 102.4017 98.9456 94.9015 108.1647 76.829 c 9.218 -18.0715 8.9738 -35.7489 -0.7315 -53.0303 Z"  -->
                                    <path 
                                        class="popup-anim path-anim" 
                                        d="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                                        data-path-to="M 8.5668 84.6681 c 9.7064 19.2027 24.9249 27.4879 45.6544 24.8556 c 20.7295 -2.6312 37.312 -12.232 49.7508 -28.8035 c 12.4377 -16.5704 12.4377 -33.1892 0 -49.8575 C 91.5343 14.1944 74.7065 4.1547 53.4897 0.7436 C 32.2718 -2.6686 17.0544 5.665 7.8353 25.7455 c -9.218 20.0794 -8.9738 39.721 0.7315 58.9226 Z" 
                                        data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                                        fill="#fff"
                                    >
                                    </path>
                                </mask>
                            </defs>
                            <!-- Das Bild in ein eigenes SVG-Element wrappen -->
                            <svg 
                                xmlns="http://www.w3.org/2000/svg" 
                                viewBox="0 0 116 116" 
                                mask="url(#left-bubble-first-mask-<?php echo $block['id'] ?>-<?php echo $unique_id ?>)" 
                                style="position: relative; width: 100%; height: auto; top: 0px; left: 0px;"
                            >
                                <!-- mask="url(#bubble-first-mask)"  -->
                                <image 
                                    xlink:href="<?php echo wp_kses_post( $block['bubbleBackgrounds']['leftBubble']['first']['url'] ); ?>" 
                                    y="<?php echo $block['bubbleBackgrounds']['leftBubble']['first']['styles']['imageMaskTranslateY'] ?>"
                                    x="<?php echo $block['bubbleBackgrounds']['leftBubble']['first']['styles']['imageMaskTranslateX']?>" 
                                    transform="scale(<?php echo $block['bubbleBackgrounds']['leftBubble']['first']['styles']['imageMaskScale']?>)"
                                    style="width: 100%;"
                                >
                                </image>
                            </svg>
                        </svg>
                    <?php
                    }
                    ?>
                </div>

                <!-- Tagline -->
                <?php
                if($block['tagline']) {
                ?>
                    <h3>
                        <?php echo wp_kses_post( $block['tagline'] ); ?>
                    </h3>
                <?php
                }
                ?>

                <!-- Main Heading -->
                <?php
                if($block['headline']) {
                ?>
                    <div class="main-heading">
                        <div class="headline-wrapper">
                            <h1 class="hero-heading <?php echo $useTypingAnimation ? 'typing-animation' : ''; ?>">
                                <?php 
                                    echo wp_kses_post( $block['headline'] ); 

                                    if($useTypingAnimation) {
                                        echo '<span class="typewriter" data-typing="' . wp_kses_post($typingAnimationContent) . '"></span>';

                                        echo wp_kses_post($headlineAfterTyping);
                                    }
                                ?>
                            </h1>

                        </div>
                    </div>
                <?php
                }
                ?>

                <!-- Content -->
                <?php
                if($block['content']) {
                ?>
                    <p><?php echo nl2br(esc_html( $block['content'] )); ?></p>
                <?php
                }
                ?>

                <!-- CTA -->
                <?php
                if($block['ctaLink']) {
                ?>
                    <a href="<?php echo wp_kses_post( $block['ctaLink'] ); ?>" <?php echo wp_kses_post($modalDataAttribute); ?> class="cta-button">
                        <div>
                            <?php echo wp_kses_post( $block['ctaText'] ); ?>
                            <svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 12H5m14 0-4 4m4-4-4-4"/>
                            </svg>

                        </div>
                    </a>
                <?php
                }
                ?>
            </div>
            <div class="right-side">
                <div class="stretch-right">
                    <div class="right-bubble first">
                        <?php
                        if(!empty($bubbleRightFirstImageUrl)) {
                        ?>

                            <svg class="active" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 116 116">
                                <defs>
                                    <mask id="bubble-first-mask-<?php echo $block['id'] ?>-<?php echo $unique_id ?>">
                                        <!-- <path class="popup-anim path-anim" d="M 8.5668 84.6681 c 9.7064 19.2027 24.9249 27.4879 45.6544 24.8556 c 20.7295 -2.6312 37.312 -12.232 49.7508 -28.8035 c 12.4377 -16.5704 12.4377 -33.1892 0 -49.8575 C 91.5343 14.1944 74.7065 4.1547 53.4897 0.7436 C 32.2718 -2.6686 17.0544 5.665 7.8353 25.7455 c -9.218 20.0794 -8.9738 39.721 0.7315 58.9226 Z" data-path-to="M20.788 76.971c8.824 17.457 22.659 24.989 41.504 22.596 18.845-2.392 33.92-11.12 45.228-26.185 11.307-15.064 11.307-30.172 0-45.325C96.213 12.904 80.915 3.777 61.627.676 42.338-2.426 28.504 5.15 20.123 23.405c-8.38 18.254-8.158 36.11.665 53.566Z" data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" fill="#fff">
                                        </path> -->
                                        <!-- d="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z"  -->
                                        <path 
                                            class="popup-anim path-anim" 
                                            d="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                                            data-path-to="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" 
                                            data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                                            fill="#fff"
                                        >
                                        </path>
                                        
                                    </mask>
                                </defs>
                                <!-- Das Bild in ein eigenes SVG-Element wrappen -->
                                <svg 
                                    xmlns="http://www.w3.org/2000/svg" 
                                    viewBox="0 0 116 116" 
                                    mask="url(#bubble-first-mask-<?php echo $block['id'] ?>-<?php echo $unique_id ?>)" 
                                    style="position: relative; width: 100%; height: auto; top: 0px; left: 0px;"
                                >
                                    <!-- mask="url(#bubble-first-mask)"  -->
                                    
                                    <image 
                                        xlink:href="<?php  echo wp_kses_post($bubbleRightFirstImageUrl); ?>" 
                                        y="<?php echo $block['bubbleBackgrounds']['rightBubbles']['first']['styles']['imageMaskTranslateY'] ?>" 
                                        x="<?php echo $block['bubbleBackgrounds']['rightBubbles']['first']['styles']['imageMaskTranslateX']?>"
                                        transform="scale(<?php echo $block['bubbleBackgrounds']['rightBubbles']['first']['styles']['imageMaskScale']?>)" 
                                        style="width: 100%;"
                                    >
                                    </image>
                                </svg>
                            </svg>

                            <?php if($heroIsVideoLightbox) { ?>
                                <div class="play-btn-outer-wrapper">
                                    <div class="play-btn-inner-wrapper">
                                        <svg aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="64" height="64" fill="#fff" viewBox="0 0 24 24">
                                            <path fill-rule="evenodd" d="M8.6 5.2A1 1 0 0 0 7 6v12a1 1 0 0 0 1.6.8l8-6a1 1 0 0 0 0-1.6l-8-6Z" clip-rule="evenodd"/>
                                        </svg>
                                    </div>
                                </div>
                            <?php } ?>

                            <?php if($heroIsVideoLightbox) { ?>
                                <svg class="click-path-wrapper" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 116 116">
                                    <path class="click-path open-video-lightbox-listener" data-video-url="<?php echo $lightboxVideoUrl ?>" d="M 8.5668 76.2013 c 9.7064 17.2824 24.9249 24.7391 45.6544 22.37 c 20.7295 -2.3681 37.312 -11.0088 49.7788 -21.1713 c 13 -10.4 17 -29.4 -4 -51.4 C 95 20 81 5 53.4897 0.6692 C 32.2718 -2.4017 17.0544 5.0985 7.8353 23.171 c -9.218 18.0715 -8.9738 35.7489 0.7315 53.0303 Z" fill="transparent" data-svg-origin="51 45.5" transform="matrix(1,0,0,1,0,0)"></path>';
                                </svg>
                            <?php } ?>
                            </svg>
                            <!-- <p><?php echo wp_kses_post( $block['bubbleBackgrounds']['rightBubbles']['first']['url'] ); ?></p> -->
                        <?php
                        }
                        ?>
                    </div>
                    <div class="right-bubble second">
                        <?php
                        if($block['bubbleBackgrounds']['rightBubbles'] && $block['bubbleBackgrounds']['rightBubbles']['second'] && $block['bubbleBackgrounds']['rightBubbles']['second']['url']) {
                        ?>
                            <svg class="active" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 116 116">
                                <defs>
                                    <mask id="bubble-second-mask-<?php echo $block['id'] ?>-<?php echo $unique_id ?>">
                                        <!-- <path class="popup-anim path-anim" d="M20.788 76.971c8.824 17.457 22.659 24.989 41.504 22.596 18.845-2.392 33.92-11.12 45.228-26.185 11.307-15.064 11.307-30.172 0-45.325C96.213 12.904 80.915 3.777 61.627.676 42.338-2.426 28.504 5.15 20.123 23.405c-8.38 18.254-8.158 36.11.665 53.566Z" data-path-to="M20.788 76.971c8.824 17.457 22.659 24.989 41.504 22.596 18.845-2.392 33.92-11.12 45.228-26.185 11.307-15.064 11.307-30.172 0-45.325C96.213 12.904 80.915 3.777 61.627.676 42.338-2.426 28.504 5.15 20.123 23.405c-8.38 18.254-8.158 36.11.665 53.566Z" data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" fill="#fff"> 
                                            </path> -->
                                        <path 
                                            class="popup-anim path-anim" 
                                            d="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                                            data-path-to="M 107.4332 23.7987 c -9.7064 -17.2824 -24.9249 -24.7391 -45.6544 -22.37 c -20.7295 2.3681 -37.312 11.0088 -49.7788 21.1713 c -13 10.4 -17 29.4 4 51.4 C 21 80 35 95 62.5103 99.3308 C 83.7282 102.4017 98.9456 94.9015 108.1647 76.829 c 9.218 -18.0715 8.9738 -35.7489 -0.7315 -53.0303 Z" 
                                            data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                                            fill="#fff"
                                        >
                                        </path>
                                    </mask>
                                </defs>
                                <!-- Das Bild in ein eigenes SVG-Element wrappen -->
                                <svg 
                                    xmlns="http://www.w3.org/2000/svg" 
                                    viewBox="0 0 116 116" 
                                    mask="url(#bubble-second-mask-<?php echo $block['id'] ?>-<?php echo $unique_id ?>)" 
                                    style="position: relative; width: 100%; height: auto; top: 0px; left: 0px;"
                                >
                                    <!-- mask="url(#bubble-second-mask)"  -->
                                    <image 
                                        xlink:href="<?php echo wp_kses_post( $block['bubbleBackgrounds']['rightBubbles']['second']['url'] )?>" 
                                        y="<?php echo $block['bubbleBackgrounds']['rightBubbles']['second']['styles']['imageMaskTranslateY'] ?>" 
                                        x="<?php echo $block['bubbleBackgrounds']['rightBubbles']['second']['styles']['imageMaskTranslateX']?>" 
                                        transform="scale(<?php echo $block['bubbleBackgrounds']['rightBubbles']['second']['styles']['imageMaskScale']?>)" 
                                        style="width: 100%;"
                                    >
                                    </image>
                                </svg>
                            </svg>
                        <?php
                        }
                        ?>
                    </div>
                    <div class="right-bubble third">
                        <?php
                        if($block['bubbleBackgrounds']['rightBubbles'] && $block['bubbleBackgrounds']['rightBubbles']['third'] && $block['bubbleBackgrounds']['rightBubbles']['third']['url']) {
                        ?>
                            <svg class="active" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 116 116">
                                <defs>
                                    <mask id="bubble-third-mask-<?php echo $block['id'] ?>-<?php echo $unique_id ?>">
                                        <path 
                                            class="popup-anim path-anim" 
                                            d="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                                            data-path-to="M 8.5668 84.6681 c 9.7064 19.2027 24.9249 27.4879 45.6544 24.8556 c 20.7295 -2.6312 37.312 -12.232 49.7508 -28.8035 c 12.4377 -16.5704 12.4377 -33.1892 0 -49.8575 C 91.5343 14.1944 74.7065 4.1547 53.4897 0.7436 C 32.2718 -2.6686 17.0544 5.665 7.8353 25.7455 c -9.218 20.0794 -8.9738 39.721 0.7315 58.9226 Z" 
                                            data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                                            fill="#fff"
                                        >
                                        </path>

                                    </mask>
                                </defs>
                                <!-- Das Bild in ein eigenes SVG-Element wrappen -->
                                <svg 
                                    xmlns="http://www.w3.org/2000/svg" 
                                    viewBox="0 0 116 116" 
                                    mask="url(#bubble-third-mask-<?php echo $block['id'] ?>-<?php echo $unique_id ?>)" 
                                    style="position: relative; width: 100%; height: auto; top: 0px; left: 0px;"
                                >
                                    <!-- mask="url(#bubble-third-mask)"  -->
                                    <image 
                                        xlink:href="<?php echo wp_kses_post( $block['bubbleBackgrounds']['rightBubbles']['third']['url'] )?>" 
                                        y="<?php echo $block['bubbleBackgrounds']['rightBubbles']['third']['styles']['imageMaskTranslateY'] ?>" 
                                        x="<?php echo $block['bubbleBackgrounds']['rightBubbles']['third']['styles']['imageMaskTranslateX']?>" 
                                        transform="scale(<?php echo $block['bubbleBackgrounds']['rightBubbles']['third']['styles']['imageMaskScale']?>)" 
                                        style="width: 100%;"
                                    >
                                    </image>
                                </svg>
                            </svg>
                        <?php
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    <?php
    }
    ?>
</div>



<?php
    // Variable zum Überprüfen, ob das Script bereits eingebunden wurde
    global $typing_animation_script_loaded;
    if ( ! isset( $typing_animation_script_loaded ) ) {
        $typing_animation_script_loaded = false;
    }
    
    if ( ! $typing_animation_script_loaded ) : 
?>
<script>
document.addEventListener("DOMContentLoaded", () => {
    const typewriterElements = document.querySelectorAll(".typewriter");

    typewriterElements.forEach((typewriterElement) => {
        const texts = typewriterElement.dataset.typing.split("|").map(text => " " + text.trim()); // Text trimmen und Leerzeichen hinzufügen
        let currentTextIndex = 0;

        const typeText = () => {
            if (currentTextIndex >= texts.length) {
                currentTextIndex = 0; // Zurück zum ersten Textsegment
            }

            let currentText = texts[currentTextIndex];
            let charIndex = 0;

            const typeCharacter = () => {
                if (charIndex < currentText.length) {
                    typewriterElement.textContent += currentText.charAt(charIndex);
                    charIndex++;
                    setTimeout(typeCharacter, 100); // Geschwindigkeit der Animation beim Schreiben
                } else {
                    typewriterElement.classList.add("waiting");
                    setTimeout(deleteText, 1500); // Wartezeit, bevor der Text gelöscht wird
                }
            };

            const deleteText = () => {
                if (charIndex > 1) {
                    typewriterElement.textContent = currentText.substring(0, charIndex - 1);
                    charIndex--;
                    setTimeout(deleteText, 50); // Geschwindigkeit der Animation beim Löschen (doppelt so schnell wie Schreiben)
                } else {
                    currentTextIndex++;
                    typewriterElement.classList.remove("waiting");
                    setTimeout(typeText, 500); // Wartezeit vor dem nächsten Text
                }
            };

            typeCharacter();
        };

        typeText();
    });
});


</script>
<?php 
$typing_animation_script_loaded = true; 
endif; 
?>


<?php
    // Variable zum Überprüfen, ob das Script bereits eingebunden wurde
    global $hero_style_included;
    if ( ! isset( $hero_style_included ) ) {
        $hero_style_included = false;
    }

    if ( ! $hero_style_included ) : 
?>
<style>
@keyframes scaleIn {
    0% {
        opacity: 0;
        transform: scale(0);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* 
.mouse-circle {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #73b740;
    position: absolute;

    pointer-events: none;
} 
*/

/* Background Video */ 
.block-inner.hero.background-hero h1 {
    color: #fff;
}


.main-heading {
    width: 100%;
    position: relative;
}

.hero-headline-scene {
    background: rgba(0, 0, 0, 0.5);
    position: absolute;
    z-index: -1;
}

.hero-headline {
    position: absolute;
    width: 100%;
}

.hero-headline text {
    width: 100%;
}

.block-inner.hero {
    padding: 0;
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center; /* Center content horizontally */
    flex-direction: column;
    width: 100%; /* Ensure the wrapper takes the full width */
    max-width: none;
}

.block-inner.hero .background-wrapper {
    position: absolute; /* Position the background wrapper absolutely */
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}


.hero.background-hero h3 {
    color: #fff;
}

@keyframes reveal-clip-from-bottom {
    to {
        opacity: 1;
    }
}


.block-inner.hero .background-wrapper:before {
    content: '';
    /* z-index: 1; */
    background: rgba(0, 0, 0, 0.5);
    opacity: 0.33;
    box-shadow: inset 0 0 10vw rgba(5,5,0,0.0);
    width: 100%;
    height: 100%;
    position: absolute;
    will-change: box-shadow;

    transition: opacity 2s ease-in-out, box-shadow 2s ease-in-out;

    /* background: radial-gradient(circle at center, transparent 150px, rgba(0, 0, 0, 0.5) 151px); */

    /* animation-name: reveal-clip-from-bottom;
    animation-duration: 1200ms;
    animation-delay: 600ms;
    animation-timing-function: ease-in-out; // cubic-bezier(.21,.61,.72,.99); // var(--elastic-transition);
    animation-fill-mode: forwards; */
}

.block-inner.hero .background-wrapper.active:before {
    opacity: 1;
    box-shadow: inset 0 0 10vw rgba(5,5,0,0.66);
}

.block-inner.hero .background-wrapper video.background-video,
.block-inner.hero .background-wrapper img.background-image,
.block-inner.hero .background-wrapper canvas.background-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover; /* Ensure the video, image and canvas cover the background */
    z-index: -1;
}



.block-inner.hero .content-wrapper {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    max-width: var(--content-width);
    padding: var(--content-padding);
    padding-top: 18%;
    padding-bottom: 18%;

    width: 100%; /* Ensure the content wrapper takes the full width */
    box-sizing: border-box; /* Include padding in the width calculation */
    z-index: 1; /* Ensure the content is above the background elements */

    color: #fff;
}

.block-inner.hero .content-wrapper.center {
    align-items: center;
    text-align: center;
}

/* .block-inner.hero .content-wrapper h1 {
    font-size: 4rem;
} */



.block-inner.hero .content-wrapper .headline-wrapper {
    padding: 20px 0;
}

@media screen and (min-width: 1221px) {
    .block-inner.hero .content-wrapper.video-hero .headline-wrapper {
        max-width: 85%;
    }
}
@media screen and (min-width: 1350px) {
    .block-inner.hero .content-wrapper.video-hero .headline-wrapper {
        max-width: 76%;
    }
}


.block-inner.hero .content-wrapper .headline-wrapper h1 {
    position: relative;
    font-size: 4rem;
    line-height: 1.25;
    /* -webkit-text-stroke-width: 1px; */
    /* -webkit-text-stroke-color: white; */
    font-weight: 700;
    width: 100%;
    --circle-radius: 100px;
    --path-position-x: -999px;
    --path-position-y: -999px;
    /* color: transparent;
    background: radial-gradient(circle at var(--path-position-x) var(--path-position-y), #ffffff00 var(--circle-radius), rgb(255 255 255) calc(var(--circle-radius) + 1px));
    background-clip: text; */
    opacity: 1;
}
/* .block-inner.hero .content-wrapper .headline-wrapper h1:before {
    content: 'Der Kongress für Innovation in Zoll und Exportkontrolle';
    position: absolute;
    color: transparent;  
    clip-path: circle(150px at 50% 50%);
    clip-path: var(--clip-path);
    clip-path: circle(var(--circle-radius) at var(--path-position-x) var(--path-position-y));
    -webkit-text-stroke-width: 1px;
    -webkit-text-stroke-color: white;
    pointer-events: none;
} 
*/


.block-inner.hero .content-wrapper p {
    font-size: 24px;
    font-weight: 400;
}



.block-inner.hero .content-wrapper a.cta-button {
    margin-top: 1rem;
    display: inline-block;
}

/********************************** */
/** Additional Style for Basic Hero */
/********************************** */
.block.fullwidth .block-inner.basic-hero {
    height: fit-content;
    max-height: 720px;
    overflow: visible;
}

@media screen and (max-width: 999px) {
    .block.fullwidth .block-inner.basic-hero {
        height: fit-content;
        max-height: unset;
    }
}

.block-inner.hero.basic-hero:before {
    content: '';
    width: 45vw;
    max-width: 740px;
    height: 45vw;
    max-height: 740px;
    position: absolute;
    background: radial-gradient(circle at 50% 50%, rgb(191 22 146 / 20%) 0%, #eee0 50%);
    /* background: #73b; */
    /* border: solid 2px #73b740; */
    transform: translate(60%, 13%);
}

.block-inner.hero .content-wrapper.basic-hero {
    display: grid; 
    grid-template-columns: 1fr 1fr; 
    grid-gap: 2rem;
    padding-top: 0;
    padding-bottom: 0;
    height: 100%;
    align-items: center;
}

@media screen and (max-width: 999px) {
    .block-inner.hero .content-wrapper.basic-hero {
        grid-template-columns: 1fr;  
        padding-top: 1rem;
        padding-bottom: 0px;
    }
}


/** 
 * LEFT SIDE 
 */
.block-inner.hero .content-wrapper.basic-hero .left-side {
    position: relative;
}

/* tagline h3 */
.block-inner.hero .content-wrapper.basic-hero h3 {
    font-size: 1.2rem;
    color: var(--accent-color-tertiary);
}

/* Left Bubble */ 
.block-inner.hero .content-wrapper.basic-hero .left-bubble.first svg {
    position: absolute;
    width: 25%;
    margin-left: -42%;
    margin-top: 10%;
    transition: all 0.3s var(--effect-transition-timing-function);
}

@media screen and (max-width: 1620px) {
    .block-inner.hero .content-wrapper.basic-hero .left-bubble.first svg {
        opacity: 0;
        scale: 0;
    }
}

.block-inner.hero .content-wrapper.basic-hero .left-bubble.first svg image {
    width: 100%;
}

/* headline h1 */ 
.block-inner.hero .content-wrapper.basic-hero .headline-wrapper h1 {
    font-size: 3rem;
    font-weight: 600;
    line-height: 1.25;
    width: 100%;
    color: var(--accent-color-secondary);
    text-wrap: balance;
}

/* description p */ 
.block-inner.hero .content-wrapper.basic-hero p {
    /* font-size: 1rem;
    color: var(--accent-color-tertiary); */
    padding-bottom: 1rem; 
    color: var(--accent-color-tertiary);
    font-size: 1.2rem;
}

.block-inner.hero .content-wrapper.basic-hero .right-side {
    position: relative;
    /* height: 100%;
    aspect-ratio: 1 / 1; */
    height: 36vw;
    max-height: 720px;
}

@media screen and (max-width: 999px) {
    .block-inner.hero .content-wrapper.basic-hero .right-side {
        height: auto;
        max-height: unset;
        width: 100%;
        aspect-ratio: 4 / 3;
    }
}


/** 
 * RIGHT SIDE 
 */
.block-inner.hero .content-wrapper.basic-hero .right-side {
    position: relative;
    /* height: 100%; */
}



.block-inner.hero .content-wrapper.basic-hero .right-side .stretch-right {
    align-self: stretch;
    position: absolute;
    width: 50vw;
    max-width: 960px;
    /* background-color: var(--accent-color-tertiary); */
    /* min-height: 300px; */
}

@media screen and (max-width: 999px) {
    .block-inner.hero .content-wrapper.basic-hero .right-side .stretch-right {
        width: 100%;
    }
}

.block-inner.hero .content-wrapper.basic-hero .right-side .stretch-right .right-bubble svg {
    position: absolute;
}
.block-inner.hero .content-wrapper.basic-hero .right-side .stretch-right .right-bubble svg image {
    width: 100%;
    transition: all 240ms ease-out;
    
    /* x: 10%; */
}
.block-inner.hero .content-wrapper.basic-hero .right-side .stretch-right .right-bubble.hover svg image {
    transition: all 240ms ease-out;
    
}

.block-inner.hero .content-wrapper.basic-hero .right-side .stretch-right .right-bubble.first svg {
    max-width: 60%;
    margin-top: 3%;
}

/* Hero Video Thumbnail */
path.click-path {
    cursor: pointer;
}
.block-inner.hero .content-wrapper.basic-hero .right-side .play-btn-outer-wrapper {
    /* background: #00000014; */
    max-width: 60%;
    margin-top: 3%;
    /* height: 2000px; */
    aspect-ratio: 8 / 7;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;

    /* add keyfranme animation */
    /* opacity: 0;
    scale: 0; */

    animation-name: scaleIn;
    animation-fill-mode: forwards;
    animation-duration: 1.1s;
    animation-delay: 0s;
    animation-timing-function: ease-in-out;
}
.block-inner.hero .content-wrapper.basic-hero .right-side .play-btn-outer-wrapper .play-btn-inner-wrapper {
    width: 74px;
    aspect-ratio: 1 / 1;
    /* height: 33%; */
    display: flex;
    justify-content: center;
    align-items: center;
    border: solid 3px #fff;
    border-radius: 50%;

    transition: all 240ms ease-out;
}
.block-inner.hero .content-wrapper.basic-hero .right-side .play-btn-outer-wrapper .play-btn-inner-wrapper svg {
    margin: 0 0 0 5px !important;
    padding: 0 !important;

    transition: all 240ms ease-out;
}

/* END Hero Video Thumbnail */


.block-inner.hero .content-wrapper.basic-hero .right-side .stretch-right .right-bubble.second svg {
    max-width: 36%;
    margin-top: 42%;
    margin-left: 52%;
}

.block-inner.hero .content-wrapper.basic-hero .right-side .stretch-right .right-bubble.third svg {
    max-width: 24%;
    margin-left: 70%;
    margin-top: 12%;
}




/**
 * Fade in Effects  
 */
 .block-inner.hero .content-wrapper.basic-hero h3 {
    opacity: 0;
    padding-bottom: 0;
    /* add keyfranme animation */
    animation-name: fadeInOne;
    animation-fill-mode: forwards;
    animation-duration: 1.2s;
    animation-delay: 0s;
    animation-timing-function: ease-in-out;
 }
.block-inner.hero .basic-hero h1.hero-heading {
    opacity: 0;
    /* add keyfranme animation */
    animation-name: fadeInTwo;
    animation-fill-mode: forwards;
    animation-duration: 1.1s;
    animation-delay: 0.1s;
    animation-timing-function: ease-in-out;
}
.block-inner.hero .content-wrapper.basic-hero p {
    opacity: 0;
    /* add keyfranme animation */
    animation-name: fadeInThree;
    animation-fill-mode: forwards;
    animation-duration: 1s;
    animation-delay: 0.2s;
    animation-timing-function: ease-in-out;
}
.block-inner.hero .content-wrapper.basic-hero a.cta-button {
    opacity: 0;
    /* add keyfranme animation */
    animation-name: fadeInThree;
    animation-fill-mode: forwards;
    animation-duration: 0.9s;
    animation-delay: 0.3s;
    animation-timing-function: ease-in-out;
}

@keyframes fadeInOne {
    0% {
        opacity: 0;
        transform: translateY(32px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}
@keyframes fadeInTwo {
    0% {
        opacity: 0;
        transform: translateY(48px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}
@keyframes fadeInThree {
    0% {
        opacity: 0;
        transform: translateY(64px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
<?php 
$video_lightbox_script_loaded = true; 
endif; 
?>


<script>
    document.addEventListener('DOMContentLoaded', function () {
        const heroBackgroundWrapper = document.querySelector('.block-inner.hero .background-wrapper');
        if (!heroBackgroundWrapper) return;
        heroBackgroundWrapper.classList.add('active');
    });
</script>
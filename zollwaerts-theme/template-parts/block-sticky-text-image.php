<?php
if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

// Überprüfung, ob es sich um den richtigen Blocktyp handelt
if ($block['type'] === 'sticky-text-image') :

// Think about optimising this:
$section_count = 0;
foreach ($block['sections'] as $item) {
    $section_count++;
}



// Get id from URL 
function get_attachment_id_from_url($url) {
    global $wpdb;
    $attachment_id = 0;

    // Originale URL auf die upload-basierte URL kürzen
    $upload_dir_paths = wp_upload_dir();
    $base_url = $upload_dir_paths['baseurl'] . '/';
    if (strpos($url, $base_url) !== false) {
        $file = basename($url);
        
        // Die ID des Anhangs anhand des Dateinamens finden
        $attachment_id = $wpdb->get_var($wpdb->prepare("SELECT ID FROM {$wpdb->posts} WHERE guid LIKE %s", '%' . $file . '%'));
    }

    return $attachment_id;
}


$img_index = 1
?>

<!-- <svg width="116px" height="100px" viewBox="0 0 116 100" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="height: auto; ">
    <defs>
        <clipPath id="custom-mask" maskUnits="objectBoundingBox" maskContentUnits="objectBoundingBox">
            <path class="popup-anim path-anim" d="M20.788 76.971c8.824 17.457 22.659 24.989 41.504 22.596 18.845-2.392 33.92-11.12 45.228-26.185 11.307-15.064 11.307-30.172 0-45.325C96.213 12.904 80.915 3.777 61.627.676 42.338-2.426 28.504 5.15 20.123 23.405c-8.38 18.254-8.158 36.11.665 53.566Z" data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" vector-effect="non-scaling-stroke" style="fill: black; opacity: 1;"></path>
        </clipPath>
    </defs>
</svg> -->

<div id="<?php echo esc_attr($block['id']); ?>" class="block-inner sticky-text-image">
    <div class="block-bg-wrapper">
        <?php 
            $section_spacing = 100 / $section_count;
            $relative_percentage = (100 / $section_count) / 2;
            $i = 1;
            foreach ($block['sections'] as $item) : 
        ?>
            <div class="bg-elem" style="height: calc(<?php echo $section_spacing ?>%);">
                <svg class="<?php echo $i % 2 === 0 ? 'even' : 'odd'; ?>" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 116 100">
                    <path 
                        class="background-path path-anim" 
                        d="M20.788 76.971c8.824 17.457 22.659 24.989 41.504 22.596 18.845-2.392 33.92-11.12 45.228-26.185 11.307-15.064 11.307-30.172 0-45.325C96.213 12.904 80.915 3.777 61.627.676 42.338-2.426 28.504 5.15 20.123 23.405c-8.38 18.254-8.158 36.11.665 53.566Z" data-path-to="M20.788 76.971c8.824 17.457 22.659 24.989 41.504 22.596 18.845-2.392 33.92-11.12 45.228-26.185 11.307-15.064 11.307-30.172 0-45.325C96.213 12.904 80.915 3.777 61.627.676 42.338-2.426 28.504 5.15 20.123 23.405c-8.38 18.254-8.158 36.11.665 53.566Z" 
                        data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z"
                        fill="var(--accent-color)" 
                        style="opacity: 0.2;">
                    </path>                           
                </svg>
            </div>
        <?php $i++; endforeach; ?>
    </div>

    <!-- Real content -->
    <?php if (!empty($block['heading'])) : ?>
        <h2><?php echo wp_kses_post($block['heading']); ?></h2>
    <?php endif; ?>

    <div class="grid grid-start" style="grid-template-columns: repeat(2, 1fr);">
        <div class="sticky-text">
            <?php foreach ($block['sections'] as $item) : ?>

                <?php 

                $imageUrl = '';
                if (isset($item['imageData']) && isset($item['imageData']['id'])) {
                    $image_id = $item['imageData']['id'];
                    $imageUrl = wp_get_attachment_url($image_id);
                }

                ?>


                <div class="single-text-wrapper">
                    <div class="single-text">
                        <?php if (!empty($item['heading'])) : ?>
                            <h2><?php echo wp_kses_post($item['heading']); ?></h2>
                        <?php endif; ?>
                        <?php if (!empty($item['paragraph'])) : ?>
                            <p><?php echo wp_kses_post($item['paragraph']); ?></p>
                        <?php endif; ?>
                    </div>

                    <!-- Images for smartphones -->
                    <?php $position = ($img_index % 2 === 0) ? 'right' : 'left'; ?>
                    <div class="single-image-wrapper">
                        <div class="single-image <?php echo $position; ?> single-image-<?php echo $img_index; ?>">
                            <?php if ($imageUrl) : ?>
                                <svg class="one-third popup-anim-mood-mobile-svg" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 116 100">
                                    <path class="popup-anim-mood-mobile path-anim" d="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" data-path-to="M20.788 76.971c8.824 17.457 22.659 24.989 41.504 22.596 18.845-2.392 33.92-11.12 45.228-26.185 11.307-15.064 11.307-30.172 0-45.325C96.213 12.904 80.915 3.777 61.627.676 42.338-2.426 28.504 5.15 20.123 23.405c-8.38 18.254-8.158 36.11.665 53.566Z" data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" fill="var(--accent-color)" style="opacity: 0.2;">

                                    </path>                           
                                </svg>

                                <svg class="two-thirds" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 116 100">
                                    <defs>
                                        <mask id="mask-<?php echo $img_index; ?>">
                                            <path 
                                                class="popup-anim-mobile path-anim" 
                                                d="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                                                data-path-to="M20.788 76.971c8.824 17.457 22.659 24.989 41.504 22.596 18.845-2.392 33.92-11.12 45.228-26.185 11.307-15.064 11.307-30.172 0-45.325C96.213 12.904 80.915 3.777 61.627.676 42.338-2.426 28.504 5.15 20.123 23.405c-8.38 18.254-8.158 36.11.665 53.566Z" 
                                                data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                                                fill="#fff">
                                            </path>

                                        </mask>
                                    </defs>
                                    <?php
                                        /*
                                        // Beispiel: Bild-URL
                                        $image_url = $item['image'];

                                        // Die Anhangs-ID aus der URL ermitteln
                                        $attachment_id = get_attachment_id_from_url($image_url);

                                        if ($attachment_id) {
                                            // Die URL des Bildes in der Größe "medium" abrufen
                                            $image = wp_get_attachment_image_src($attachment_id, 'large');
                                            
                                            if ($image) {
                                                $medium_image_url = $image[0]; // URL des Bildes
                                                echo '<image xlink:href="' . esc_url($medium_image_url) . '" mask="url(#mask-' . $img_index . ')" x="-10" y="0" />'; // . '" alt="' . esc_attr(get_the_title($attachment_id)) . '" />';
                                            }
                                        } else {
                                            // Fallback auf das Originalbild, falls keine ID gefunden wurde
                                            echo '<image xlink:href="' . esc_url($image_url) . '" mask="url(#mask-' . $img_index . ')" x="-10" y="0" />';
                                            // echo '<p>Kein large image gefunden</p>';
                                        }
                                        */
                                        echo '<image xlink:href="' . esc_url($imageUrl) . '" mask="url(#mask-' . $img_index . ')" x="-10" y="0" />'; // . '" alt="' . esc_attr(get_the_title($attachment_id)) . '" />';
                                    ?>
                                </svg>
                            <?php endif; ?>
                            <!-- Count index up -->
                            <?php $img_index = $img_index + 1; ?>

                        </div>

                    </div>
                </div>
            <?php endforeach; ?>
        </div>

        <div class="sticky-image">
            <?php foreach ($block['sections'] as $item) : ?>

                <?php 

                $imageUrl = '';
                if (isset($item['imageData']) && isset($item['imageData']['id'])) {
                    $image_id = $item['imageData']['id'];
                    $imageUrl = wp_get_attachment_url($image_id);
                }

                ?>

                <?php $position = ($img_index % 2 === 0) ? 'right' : 'left'; ?>
                <div class="single-image-wrapper">
                    <div class="single-image <?php echo $position; ?> single-image-<?php echo $img_index; ?>">
                        <?php if ($imageUrl) : ?>
                            <svg class="one-third" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 116 100">
                                <path class="popup-anim-mood path-anim" d="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" data-path-to="M20.788 76.971c8.824 17.457 22.659 24.989 41.504 22.596 18.845-2.392 33.92-11.12 45.228-26.185 11.307-15.064 11.307-30.172 0-45.325C96.213 12.904 80.915 3.777 61.627.676 42.338-2.426 28.504 5.15 20.123 23.405c-8.38 18.254-8.158 36.11.665 53.566Z" data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" fill="var(--accent-color)" style="opacity: 0.2;">

                                </path>                           
                            </svg>

                            <svg class="two-thirds" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 116 100">
                                <defs>
                                    <mask id="mask-<?php echo $img_index; ?>">
                                         <path 
                                            class="popup-anim path-anim" 
                                            d="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                                            data-path-to="M20.788 76.971c8.824 17.457 22.659 24.989 41.504 22.596 18.845-2.392 33.92-11.12 45.228-26.185 11.307-15.064 11.307-30.172 0-45.325C96.213 12.904 80.915 3.777 61.627.676 42.338-2.426 28.504 5.15 20.123 23.405c-8.38 18.254-8.158 36.11.665 53.566Z" 
                                            data-default-path="M 51 46 c 0 0 0 -0 0 -0 c 0 0 0 -0 0 0 c -0 0 -0 0 0 0 C 51 46 51 46 51 46 C 51 46 51 46 51 45 c 0 1 0 1 0 1 Z" 
                                            fill="#fff">
                                        </path>

                                    </mask>
                                </defs>
                                <?php
                                    /*
                                    // Beispiel: Bild-URL
                                    $image_url = $item['image'];

                                    // Die Anhangs-ID aus der URL ermitteln
                                    $attachment_id = get_attachment_id_from_url($image_url);

                                    if ($attachment_id) {
                                        // Die URL des Bildes in der Größe "medium" abrufen
                                        $image = wp_get_attachment_image_src($attachment_id, 'large');
                                        
                                        if ($image) {
                                            $medium_image_url = $image[0]; // URL des Bildes
                                            echo '<image xlink:href="' . esc_url($medium_image_url) . '" mask="url(#mask-' . $img_index . ')" x="-10" y="0" />'; // . '" alt="' . esc_attr(get_the_title($attachment_id)) . '" />';
                                        }
                                    } else {
                                        // Fallback auf das Originalbild, falls keine ID gefunden wurde
                                        echo '<image xlink:href="' . esc_url($image_url) . '" mask="url(#mask-' . $img_index . ')" x="-10" y="0" />';
                                        // echo '<p>Kein large image gefunden</p>';
                                    }
                                    */
                                    echo '<image xlink:href="' . esc_url($imageUrl) . '" mask="url(#mask-' . $img_index . ')" x="-10" y="0" />';
                                ?>
                            </svg>
                        <?php endif; ?>
                        <!-- Count the index up -->
                        <?php $img_index = $img_index + 1; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
        
    </div>
</div>

<?php endif; ?>


<style>

    /* Testing style because of bug */ 
    .block-inner.sticky-text-image .sticky-text {
        position: relative;
    }

    .block-inner.sticky-text-image .single-text-wrapper {
        position: relative;
    }
    /* End Testing style because of bug */

    .block-inner.sticky-text-image  {
        position: relative;
    }
    /* TODO: Reconfigure this values */
    .block-inner.sticky-text-image .block-bg-wrapper {
        width: 100vw;
        min-height: 300px;
        /* background: rgb(56, 22, 191, .2); */
        position: absolute;
        margin-left: calc(-50vw + 50%);
        height: 100%;
        pointer-events: none;
    }

    .block-inner.sticky-text-image .bg-elem {
        display: flex;
        align-items: center;
        overflow: hidden;
    }


    .block-inner.sticky-text-image .sticky-text .single-text {
        display: flex;
        flex-direction: column;
        justify-content: center;
        width: 100%;
        /* height: 440px; */
        /* height: 100vh; */
        /* margin-bottom: 120px; */
        min-height: 50vh;

        /* transform: translate(0, 4rem); */
        opacity: 0;
        transition: all 1s ease;
        
        transform: translate(0, 100px);
    }

    .block-inner.sticky-text-image .sticky-text .single-text.active {
        opacity: 1;
        transform: translate(0, 0);
    }

    @media screen and (min-width: 1000px) {
        .block-inner.sticky-text-image .sticky-text .single-text-wrapper .single-text.fade-out {
            transform: translate(0, -100px);
            opacity: 0;
        }
    
        /** 
         * set first and last item to default active style 
         */
        /* First child is active when scroll-in */
        .block-inner.sticky-text-image .sticky-text .single-text-wrapper:first-child .single-text {
            opacity: 1;
            transform: translate(0, 0);
        }
        .block-inner.sticky-text-image .sticky-text .single-text-wrapper:first-child .single-text.fade-out {
            transform: translate(0, -100px);
            opacity: 0;
        }

        /* last child stays active when scroll-out */
        .block-inner.sticky-text-image .sticky-text .single-text-wrapper:last-child .single-text.fade-out {
            opacity: 1;
            transform: translate(0, 0);
        }
    }

    /* SVG TRANSFORMATIONS */ 
    .block-inner.sticky-text-image .sticky-image .single-image svg.two-thirds {
        /* transform: translate(0, 200px); */
        transition: all 1s ease;
        z-index: 1;
    }
    .block-inner.sticky-text-image .sticky-image .single-image svg.two-thirds.active {
        transform: translate(0, 0);
        z-index: 9
    }
    .block-inner.sticky-text-image .sticky-image .single-image svg.two-thirds.fade-out {
        /* transform: translate(0, -200px); */
        z-index: 1;
    }




    .block-inner.sticky-text-image .sticky-text .single-text-wrapper:last-child .single-text {
        margin-bottom: 0;
    }

    .block-inner.sticky-text-image .sticky-image {
        position: sticky;
        position: sticky;
        top: calc(50vh - 220px);
        height: 50vh;
        display: inline-block;
        /* height: 440px; */
    }

    .block-inner.sticky-text-image .sticky-image img {
        width: 100%;
        position: absolute;
        transform-origin: center center;
    }

    .block-inner.sticky-text-image .sticky-image .single-image-wrapper {
        position: absolute;
        /* height: 440px; */
        width: 100%;
        z-index: 1;
    }
    .block-inner.sticky-text-image .sticky-image .single-image-wrapper.active {
        position: absolute;
        /* height: 440px; */
        width: 100%;
        z-index: 9;
    }

    .block-inner.sticky-text-image .sticky-image .single-image-wrapper .single-image {
        position: relative;
        width: 100%;
    }


    /* .sticky-image .single-image.right {
        text-align: right;
    } */
    .block-inner.sticky-text-image .sticky-image .single-image:last-child {
        position: relative !important;
    }

    .block-inner.sticky-text-image .sticky-image .single-image svg {
        position: absolute;
    }
    
    
    .block-inner.sticky-text-image .sticky-image .single-image.right svg {
        width: 100%;
        position: absolute;
    }

    
    
    
    .block-inner.sticky-text-image .sticky-image .single-image svg.two-thirds {
        max-width: 66%;
    }
    .block-inner.sticky-text-image .sticky-image .single-image.right svg.two-thirds {
        left: 34%;
    }

    .block-inner.sticky-text-image .sticky-image .single-image svg.one-third {
        max-width: 33%;
    }
    .block-inner.sticky-text-image .sticky-image .single-image.right svg.one-third {
        left: 17%;
        top: 2vh;
        max-width: 50%;
    }


    .block-inner.sticky-text-image svg image {
        width: auto;
        /* height: auto; */
        height: 100%;
    }


    /** NEW HEIGHT */
    .block-inner.sticky-text-image .single-image-wrapper {
        height: 50vh;
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    .block-inner.sticky-text-image .single-image {
        height: 50vh;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .block-inner.sticky-text-image .sticky-image .single-image svg.one-third {
        max-width: 33%;
        left: 66%;
    }



    /** SVG Decorators */
    .block-inner.sticky-text-image .bg-elem svg.even {
        width: 25vw;
        position: absolute;
        max-width: 440px;
        margin-left: calc(50vw - (1400px / 2) + 4rem - 30vw) !important;
    }
    .block-inner.sticky-text-image .bg-elem svg.odd {
        width: 12vw;
        position: absolute;
        max-width: 320px;
        margin-top: -14%;
        margin-left: calc(50vw + (1400px / 2) + 4rem + 0vw) !important;
    }

    .block-inner.sticky-text-image .single-text-wrapper .single-image-wrapper {
        display: none;
    }


    @media screen and (max-width: 999px) {
        .block-inner.sticky-text-image .single-text-wrapper .single-image-wrapper {
            display: flex;
        }
        
        .block-inner.sticky-text-image .grid {
            grid-template-columns: 1fr !important;
        }
        .block-inner.sticky-text-image .sticky-text .single-text-wrapper .single-text {
            opacity: 1;
            transform: translate(0, 0);
            opacity: 1;
            transition: all 1s ease;
            transform: translate(0, 0px);
            padding-top: 4rem;
            padding-bottom: 2rem;
        }
        .block-inner.sticky-text-image .sticky-text .single-text-wrapper:first-child .single-text {
            padding-top: 0;
        }
        /* .sticky-text .single-text-wrapper:last-child .single-text {
            padding-bottom: 0;
        } */

        .block-inner.sticky-text-image .sticky-text .single-text-wrapper:first-child .single-text.fade-out,
        .block-inner.sticky-text-image .sticky-text .single-text-wrapper .single-text.fade-out {
            transform: translate(0, 0px);
            opacity: 1;
        }

        .block-inner.sticky-text-image .sticky-image {
            display: none;
        }
    }
    
    .block-inner.sticky-text-image .single-text-wrapper .single-image-wrapper {
        position: relative;
        height: auto;
    }
    .block-inner.sticky-text-image .single-text-wrapper .single-image-wrapper .single-image {
        width: 100%;
        height: auto;
        aspect-ratio: 16 / 9;
        justify-content: flex-start;
        align-items: baseline;

        /* try to fix chrome render issues */ 
        /* -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
        transform-style: preserve-3d;
        transform: translateZ(0); */
    }
    .block-inner.sticky-text-image .single-text-wrapper .single-image-wrapper svg {
        width: 100%;
        height: auto;
        position: absolute;

        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
        transform-style: preserve-3d;
        transform: translateZ(0);
    }
    .block-inner.sticky-text-image .single-text-wrapper .single-image-wrapper svg.one-third {
        width: 33%;
        margin-left: 0%;
    }
    .block-inner.sticky-text-image .single-text-wrapper .single-image-wrapper svg.two-thirds {
        width: 66%;
        margin-left: 33%;
    }

    .block-inner.sticky-text-image .single-text-wrapper .single-image-wrapper svg path {
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
        transform-style: preserve-3d;
    }

</style>


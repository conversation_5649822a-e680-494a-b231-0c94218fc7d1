<?php
if ( ! defined( 'ABSPATH' ) ) {
    exit; // Exit if accessed directly.
}

// Überprüfung, ob es sich um den richtigen Blocktyp handelt
if ($block['type'] === 'slider-section') :

$sliderStyle = isset($block['sliderStyle']) ? $block['sliderStyle'] : 'default';
$maxSlides = 3;
$slideAggregation = 1;

$imageObjectFit = 'cover'; // default value

if($sliderStyle === 'default') {
    $imageAspectRatioWidth = 16;
    $imageAspectRatioHeight = 9;
    $maxSlides = 3;
    $slideAggregation = 1;
}

if($sliderStyle === 'team') {
    $imageAspectRatioWidth = 1;
    $imageAspectRatioHeight = 1;
    $maxSlides = 3;
    $slideAggregation = 1;
}

if($sliderStyle === 'logos') {
    $imageAspectRatioWidth = 16;
    $imageAspectRatioHeight = 9;
    $imageObjectFit = 'contain';
    $maxSlides = 4;
    $slideAggregation = 4;
}


// get "imageAspectRatioWidth" and "imageAspectRatioHeight" from block
$imageAspectRatioWidth = isset($block['imageAspectRatioWidth']) ? $block['imageAspectRatioWidth'] : $imageAspectRatioWidth;
$imageAspectRatioHeight = isset($block['imageAspectRatioHeight']) ? $block['imageAspectRatioHeight'] : $imageAspectRatioHeight;

$imageObjectFit = isset($block['imageObjectFit']) ? $block['imageObjectFit'] : $imageObjectFit;

$maxSlides = isset($block['maxSlides']) ? $block['maxSlides'] : $maxSlides;

$slideAggregation = isset($block['slideAggregation']) && $block['slideAggregation'] ? $block['slideAggregation'] : $slideAggregation;
?>
        
<div id="<?php echo esc_attr($block['id']); ?>" class="block-inner slider-section">

    <!-- <section> -->
    <div class="content-wrapper">

        <?php if (!empty($block['tagline'])) : ?>
            <p class="tagline"><?php echo wp_kses_post($block['tagline']); ?></p>
        <?php endif; ?>

        <?php if (!empty($block['heading'])) : ?>
            <h2><?php echo wp_kses_post($block['heading']); ?></h2>
        <?php endif; ?>
        
        <?php if (!empty($block['paragraph'])) : ?>
            <p class="module-intro"><?php echo wp_kses_post($block['paragraph']); ?></p>
        <?php endif; ?>

        <div class="nw-slide <?php echo $sliderStyle; ?>" data-slide-max-slides="<?php echo $maxSlides; ?>" data-slide-gap="36px" data-side-aggregation="<?php echo $slideAggregation; ?>">
            <div class="nw-slide__wrapper">
                <ul class="nw-slide__list">
                    <?php foreach ($block['sliderItems'] as $item) : ?>
                        <?php
                        // Slider images 
                        $imageUrl = isset($item['image']) && isset($item['image']['url']) && $item['image']['url'] ? $item['image']['url'] : '/wp-content/plugins/grenzlotsen-page-builder/placeholder/image-placeholder.png';

                        // Überprüfen der Link-Optionen
                        $useLink = !empty($item['useLink']);
                        $linkHref = $useLink ? esc_url($item['linkHref']) : '';
                        $openLinkInNewTab = !empty($item['openLinkInNewTab']) ? 'target="_blank"' : '';
                        $linkTarget = $useLink && !empty($item['openLinkInNewTab']) ? '_blank' : '';


                        ?>

                        <?php
                        /*
                        <li class="nw-slide__item <?php echo $useLink && $linkHref ? 'has-link' : '' ?>" 
                            <?php if ($useLink) : ?>
                                data-link-url="<?php echo $linkHref; ?>"
                                <?php if ($linkTarget) : ?>
                                    data-link-target="<?php echo esc_attr($linkTarget); ?>"
                                <?php endif; ?>
                            <?php endif; ?>
                        >
                        */
                        ?>
                        <li class="nw-slide__item <?php echo $useLink && $linkHref ? 'has-link' : ''; ?>"
                            <?php 
                                if ($useLink) {
                                    echo ' data-link-url="' . $linkHref . '"';
                                    if ($linkTarget) {
                                        echo ' data-link-target="' . esc_attr($linkTarget) . '"';
                                    }
                                }
                            ?>
                        >
                            <div class="background"></div>
                            <!-- Slider Item -->
                            <div class="item-content">
                                <img 
                                    src="<?php echo esc_url($imageUrl); ?>" 
                                    alt="<?php echo esc_attr($item['name'] . ' – ' . $item['title']); ?>" 
                                    style="<?php echo 'aspect-ratio: ' . $imageAspectRatioWidth . ' / ' . $imageAspectRatioHeight; ?>; object-fit: <?php echo $imageObjectFit; ?>;"
                                />
                                <?php if($sliderStyle === 'team') : ?>
                                    <h6><?php echo wp_kses_post($item['title']); ?></h6>
                                    <h4><?php echo wp_kses_post($item['name']); ?></h4>
                                <?php endif; ?>
                            </div>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>
        </div>

        <?php 
        // print_r($block['cta']);
        // echo '<hr>';
        // print_r($block['cta']['useCta']);
        ?>

        <?php 
            if (!empty($block['cta'] && $block['cta']['useCta'])) {
                
                $data_modal = isset($block['cta']['useModal']) && $block['cta']['modalId'] ? 'data-modal-class="' . $block['cta']['modalId'] . '"' : '';
                $openLinkInNewTab = isset($block['cta']['openLinkInNewTab']) && $block['cta']['openLinkInNewTab'] && !$block['cta']['useModal'] ? 'target="_blank"' : '';
                $ctaStyle = isset($block['cta']['ctaStyle']) ? $block['cta']['ctaStyle'] : '';

                echo '<div class="nw-slide__cta">';
                    echo '<a href="' . esc_url($block['cta']['ctaLink']) . '"  class="cta-button ' . $ctaStyle . '" ' . $data_modal . '' . $openLinkInNewTab . '>';
                        echo '<div>';
                            echo wp_kses_post($block['cta']['ctaText']);
                            echo '<svg class="w-6 h-6 text-gray-800 dark:text-white" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24">
                                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 12H5m14 0-4 4m4-4-4-4"></path>
                                    </svg>';
                        echo '</div>';
                    echo '</a>';
                echo '</div>';
            }
        ?>
        
    </div>
</div>

<?php endif; ?>

